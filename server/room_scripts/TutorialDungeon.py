"""
Tutorial Dungeon Room Script
Handles chain breaking events, parrot flight, and camera movement expansion
"""

def on_entity_destroyed(session, entity_id, entity_name, all_sessions):
    """
    Called when an entity is destroyed in the tutorial dungeon
    """
    if entity_name == "Chains02":
        print(f"[{session.addr}] Chains broken! Triggering parrot to fly and expanding camera movement...")
        
        # Trigger the IntroParrot to fly - this is the key mechanism
        trigger_parrot_flight(session, all_sessions)
        
        # Send dynamic collision update to disable movement restrictions
        send_dynamic_collision_update(session, "am_DynamicCollision_ChainBarrier", False)
        
        # Send tutorial state update to indicate chains are broken
        send_tutorial_state_update(session, "ChainsBroken", True)
        
        # Broadcast to all players in the same level
        for other_session in all_sessions:
            if (other_session != session and 
                other_session.current_level == "TutorialDungeon" and
                other_session.world_loaded):
                send_dynamic_collision_update(other_session, "am_DynamicCollision_ChainBarrier", False)
                send_tutorial_state_update(other_session, "ChainsBroken", True)

def trigger_parrot_flight(session, all_sessions):
    """
    Trigger the IntroParrot to start flying - this should expand camera movement
    """
    try:
        # Find the IntroParrot entity (ID 1 in TutorialDungeon.json)
        parrot_entity_id = 1
        
        # Send entity animation command to make parrot fly
        send_entity_animation(session, parrot_entity_id, "Fly")
        
        # Send entity movement command to make parrot move to target coordinates
        # The parrot should fly towards coordinates around (1680, 1899)
        send_entity_goto(session, parrot_entity_id, 1680, 1899)
        
        # Broadcast to all players in the same level
        for other_session in all_sessions:
            if (other_session != session and 
                other_session.current_level == "TutorialDungeon" and
                other_session.world_loaded):
                send_entity_animation(other_session, parrot_entity_id, "Fly")
                send_entity_goto(other_session, parrot_entity_id, 1680, 1899)
                
        print(f"[{session.addr}] Triggered IntroParrot to fly to (1680, 1899)")
        
    except Exception as e:
        print(f"[{session.addr}] Error triggering parrot flight: {e}")

def send_entity_animation(session, entity_id, animation_name):
    """
    Send entity animation command using PKTTYPE_LEVEL_STATE (0x40)
    """
    try:
        from BitUtils import BitBuffer
        import struct
        
        # Create packet for SetEntityAnimation using Level.method_112 format
        bb = BitBuffer()
        bb.write_method_26(f"2^SetEntityAnimation^{entity_id}")
        bb.write_method_26(animation_name)
        
        payload = bb.to_bytes()
        packet = struct.pack(">HH", 0x40, len(payload)) + payload
        
        session.conn.sendall(packet)
        print(f"[{session.addr}] Sent entity animation: Entity {entity_id} -> {animation_name}")
        
    except Exception as e:
        print(f"[{session.addr}] Error sending entity animation: {e}")

def send_entity_goto(session, entity_id, target_x, target_y):
    """
    Send entity movement command - this might need a different packet type
    """
    try:
        from BitUtils import BitBuffer
        import struct
        
        # This might need to use a different packet type for entity movement
        # For now, try using the level state command format
        bb = BitBuffer()
        bb.write_method_26(f"2^SetEntityPosition^{entity_id}")
        bb.write_method_26(f"{target_x},{target_y}")
        
        payload = bb.to_bytes()
        packet = struct.pack(">HH", 0x40, len(payload)) + payload
        
        session.conn.sendall(packet)
        print(f"[{session.addr}] Sent entity goto: Entity {entity_id} -> ({target_x}, {target_y})")
        
    except Exception as e:
        print(f"[{session.addr}] Error sending entity goto: {e}")

def send_dynamic_collision_update(session, collision_name, enabled):
    """
    Send a dynamic collision update packet using PKTTYPE_LEVEL_STATE (0x40)
    """
    try:
        from BitUtils import BitBuffer
        import struct
        
        # Create packet for SetDynamicCollision using Level.method_112 format
        bb = BitBuffer()
        bb.write_method_26(f"2^SetDynamicCollision^{collision_name}")
        bb.write_method_26("On" if enabled else "Off")
        
        payload = bb.to_bytes()
        packet = struct.pack(">HH", 0x40, len(payload)) + payload
        
        session.conn.sendall(packet)
        print(f"[{session.addr}] Sent dynamic collision update: {collision_name} = {'On' if enabled else 'Off'}")
        
    except Exception as e:
        print(f"[{session.addr}] Error sending dynamic collision update: {e}")

def send_tutorial_state_update(session, tutorial_name, enabled):
    """
    Send a tutorial state update packet using PKTTYPE_LEVEL_STATE (0x40)
    """
    try:
        from BitUtils import BitBuffer
        import struct
        
        # Create packet for SetTutorialState using Level.method_112 format
        bb = BitBuffer()
        bb.write_method_26(f"2^SetTutorialState^{tutorial_name}")
        bb.write_method_26("On" if enabled else "Off")
        
        payload = bb.to_bytes()
        packet = struct.pack(">HH", 0x40, len(payload)) + payload
        
        session.conn.sendall(packet)
        print(f"[{session.addr}] Sent tutorial state update: {tutorial_name} = {'On' if enabled else 'Off'}")
        
    except Exception as e:
        print(f"[{session.addr}] Error sending tutorial state update: {e}")

def on_room_enter(session):
    """
    Called when a player enters the tutorial dungeon room
    """
    print(f"[{session.addr}] Player entered tutorial dungeon")
    
    # Set initial camera restrictions (chains are intact)
    send_dynamic_collision_update(session, "am_DynamicCollision_ChainBarrier", True)
    send_tutorial_state_update(session, "ChainsIntact", True)

def on_room_exit(session):
    """
    Called when a player exits the tutorial dungeon room
    """
    print(f"[{session.addr}] Player exited tutorial dungeon")

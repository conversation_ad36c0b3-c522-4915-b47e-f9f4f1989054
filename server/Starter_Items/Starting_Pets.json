[{"typeID": 1, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 2, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 3, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 4, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 5, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 6, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 7, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 8, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 9, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 10, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 11, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 12, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 13, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 14, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 15, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 16, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 17, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 18, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 19, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 20, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 21, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 22, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 23, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 24, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 25, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 26, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 27, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 28, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 29, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 30, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 31, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 32, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 33, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 34, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 35, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 36, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 37, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 38, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 39, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 40, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 41, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 42, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 43, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 44, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 45, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 46, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 47, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 48, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 49, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 50, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 51, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 52, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 53, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 54, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 55, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 56, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 57, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 58, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 59, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 60, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 61, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 62, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 63, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 64, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 65, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 66, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 67, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 68, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 69, "level": 1, "xp": 0, "iteration": 0}, {"typeID": 70, "level": 1, "xp": 0, "iteration": 0}]
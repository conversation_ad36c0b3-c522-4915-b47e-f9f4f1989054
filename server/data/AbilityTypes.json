[{"AbilityName": "StunStrike", "AbilityID": "1", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "219", "IdolCost": "1", "UpgradeTime": "20", "Type": "Attack", "Rank": "1"}, {"AbilityName": "StunStrike", "AbilityID": "1", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "3671", "IdolCost": "2", "UpgradeTime": "43200", "Type": "Attack", "Rank": "2"}, {"AbilityName": "StunStrike", "AbilityID": "1", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "12224", "IdolCost": "7", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "StunStrike", "AbilityID": "1", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "29968", "IdolCost": "15", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "StunStrike", "AbilityID": "1", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "64256", "IdolCost": "33", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "StunStrike", "AbilityID": "1", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "127012", "IdolCost": "44", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "StunStrike", "AbilityID": "1", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "238848", "IdolCost": "59", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "StunStrike", "AbilityID": "1", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "432524", "IdolCost": "85", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "StunStrike", "AbilityID": "1", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "759808", "IdolCost": "119", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "StunStrike", "AbilityID": "1", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "1306458", "IdolCost": "146", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "PoisonStrike", "AbilityID": "2", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "329", "IdolCost": "1", "UpgradeTime": "40", "Type": "Attack", "Rank": "1"}, {"AbilityName": "PoisonStrike", "AbilityID": "2", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "4281", "IdolCost": "3", "UpgradeTime": "43200", "Type": "Attack", "Rank": "2"}, {"AbilityName": "PoisonStrike", "AbilityID": "2", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "13567", "IdolCost": "7", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "PoisonStrike", "AbilityID": "2", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "32563", "IdolCost": "17", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "PoisonStrike", "AbilityID": "2", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "69044", "IdolCost": "35", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "PoisonStrike", "AbilityID": "2", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "136037", "IdolCost": "45", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "PoisonStrike", "AbilityID": "2", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "254251", "IdolCost": "61", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "PoisonStrike", "AbilityID": "2", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "458607", "IdolCost": "88", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "PoisonStrike", "AbilityID": "2", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "804044", "IdolCost": "121", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "PoisonStrike", "AbilityID": "2", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "1379643", "IdolCost": "150", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "QuickStrike", "AbilityID": "3", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "438", "IdolCost": "1", "UpgradeTime": "60", "Type": "Attack", "Rank": "1"}, {"AbilityName": "QuickStrike", "AbilityID": "3", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "4890", "IdolCost": "3", "UpgradeTime": "43200", "Type": "Attack", "Rank": "2"}, {"AbilityName": "QuickStrike", "AbilityID": "3", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "14909", "IdolCost": "8", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "QuickStrike", "AbilityID": "3", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "35158", "IdolCost": "18", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "QuickStrike", "AbilityID": "3", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "73831", "IdolCost": "37", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "QuickStrike", "AbilityID": "3", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "145061", "IdolCost": "46", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "QuickStrike", "AbilityID": "3", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "269653", "IdolCost": "63", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "QuickStrike", "AbilityID": "3", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "484690", "IdolCost": "92", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "QuickStrike", "AbilityID": "3", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "848280", "IdolCost": "123", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "QuickStrike", "AbilityID": "3", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "1452827", "IdolCost": "154", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "Enfeeble", "AbilityID": "4", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "745", "IdolCost": "1", "UpgradeTime": "80", "Type": "Survival", "Rank": "1"}, {"AbilityName": "Enfeeble", "AbilityID": "4", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "5594", "IdolCost": "3", "UpgradeTime": "43200", "Type": "Survival", "Rank": "2"}, {"AbilityName": "Enfeeble", "AbilityID": "4", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "16419", "IdolCost": "9", "UpgradeTime": "108000", "Type": "Survival", "Rank": "3"}, {"AbilityName": "Enfeeble", "AbilityID": "4", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "38176", "IdolCost": "20", "UpgradeTime": "172800", "Type": "Survival", "Rank": "4"}, {"AbilityName": "Enfeeble", "AbilityID": "4", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "79501", "IdolCost": "38", "UpgradeTime": "259200", "Type": "Survival", "Rank": "5"}, {"AbilityName": "Enfeeble", "AbilityID": "4", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "154805", "IdolCost": "48", "UpgradeTime": "343800", "Type": "Survival", "Rank": "6"}, {"AbilityName": "Enfeeble", "AbilityID": "4", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "286973", "IdolCost": "65", "UpgradeTime": "439200", "Type": "Survival", "Rank": "7"}, {"AbilityName": "Enfeeble", "AbilityID": "4", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "514014", "IdolCost": "95", "UpgradeTime": "634800", "Type": "Survival", "Rank": "8"}, {"AbilityName": "Enfeeble", "AbilityID": "4", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "897069", "IdolCost": "126", "UpgradeTime": "715200", "Type": "Survival", "Rank": "9"}, {"AbilityName": "Enfeeble", "AbilityID": "4", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "1533911", "IdolCost": "158", "UpgradeTime": "1024200", "Type": "Survival", "Rank": "10"}, {"AbilityName": "RootStrike", "AbilityID": "5", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "1402", "IdolCost": "1", "UpgradeTime": "100", "Type": "Crowd Control", "Rank": "1"}, {"AbilityName": "RootStrike", "AbilityID": "5", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "6297", "IdolCost": "4", "UpgradeTime": "43200", "Type": "Crowd Control", "Rank": "2"}, {"AbilityName": "RootStrike", "AbilityID": "5", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "17929", "IdolCost": "9", "UpgradeTime": "108000", "Type": "Crowd Control", "Rank": "3"}, {"AbilityName": "RootStrike", "AbilityID": "5", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "41194", "IdolCost": "21", "UpgradeTime": "172800", "Type": "Crowd Control", "Rank": "4"}, {"AbilityName": "RootStrike", "AbilityID": "5", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "85170", "IdolCost": "38", "UpgradeTime": "259200", "Type": "Crowd Control", "Rank": "5"}, {"AbilityName": "RootStrike", "AbilityID": "5", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "164549", "IdolCost": "49", "UpgradeTime": "343800", "Type": "Crowd Control", "Rank": "6"}, {"AbilityName": "RootStrike", "AbilityID": "5", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "304292", "IdolCost": "67", "UpgradeTime": "439200", "Type": "Crowd Control", "Rank": "7"}, {"AbilityName": "RootStrike", "AbilityID": "5", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "543338", "IdolCost": "99", "UpgradeTime": "634800", "Type": "Crowd Control", "Rank": "8"}, {"AbilityName": "RootStrike", "AbilityID": "5", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "945857", "IdolCost": "128", "UpgradeTime": "715200", "Type": "Crowd Control", "Rank": "9"}, {"AbilityName": "RootStrike", "AbilityID": "5", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Duelist", "GoldCost": "1614995", "IdolCost": "162", "UpgradeTime": "1024200", "Type": "Crowd Control", "Rank": "10"}, {"AbilityName": "SteelCyclone", "AbilityID": "6", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "1052", "IdolCost": "1", "UpgradeTime": "120", "Type": "Attack", "Rank": "1"}, {"AbilityName": "SteelCyclone", "AbilityID": "6", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "7158", "IdolCost": "4", "UpgradeTime": "43200", "Type": "Attack", "Rank": "2"}, {"AbilityName": "SteelCyclone", "AbilityID": "6", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "19615", "IdolCost": "10", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "SteelCyclone", "AbilityID": "6", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "44516", "IdolCost": "23", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "SteelCyclone", "AbilityID": "6", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "91339", "IdolCost": "39", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "SteelCyclone", "AbilityID": "6", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "175706", "IdolCost": "50", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "SteelCyclone", "AbilityID": "6", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "323253", "IdolCost": "70", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "SteelCyclone", "AbilityID": "6", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "575613", "IdolCost": "104", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "SteelCyclone", "AbilityID": "6", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "1000164", "IdolCost": "131", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "SteelCyclone", "AbilityID": "6", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "1703688", "IdolCost": "166", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "HawkStrike", "AbilityID": "7", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "1752", "IdolCost": "1", "UpgradeTime": "140", "Type": "Attack", "Rank": "1"}, {"AbilityName": "HawkStrike", "AbilityID": "7", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "8019", "IdolCost": "5", "UpgradeTime": "43200", "Type": "Attack", "Rank": "2"}, {"AbilityName": "HawkStrike", "AbilityID": "7", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "21301", "IdolCost": "11", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "HawkStrike", "AbilityID": "7", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "47838", "IdolCost": "24", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "HawkStrike", "AbilityID": "7", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "97507", "IdolCost": "40", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "HawkStrike", "AbilityID": "7", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "186863", "IdolCost": "52", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "HawkStrike", "AbilityID": "7", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "342213", "IdolCost": "73", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "HawkStrike", "AbilityID": "7", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "607888", "IdolCost": "108", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "HawkStrike", "AbilityID": "7", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "1054470", "IdolCost": "134", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "HawkStrike", "AbilityID": "7", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "1792381", "IdolCost": "171", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "<PERSON><PERSON>", "AbilityID": "8", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "2191", "IdolCost": "2", "UpgradeTime": "160", "Type": "Survival", "Rank": "1"}, {"AbilityName": "<PERSON><PERSON>", "AbilityID": "8", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "9005", "IdolCost": "5", "UpgradeTime": "43200", "Type": "Survival", "Rank": "2"}, {"AbilityName": "<PERSON><PERSON>", "AbilityID": "8", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "23413", "IdolCost": "12", "UpgradeTime": "108000", "Type": "Survival", "Rank": "3"}, {"AbilityName": "<PERSON><PERSON>", "AbilityID": "8", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "51663", "IdolCost": "26", "UpgradeTime": "172800", "Type": "Survival", "Rank": "4"}, {"AbilityName": "<PERSON><PERSON>", "AbilityID": "8", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "104458", "IdolCost": "41", "UpgradeTime": "259200", "Type": "Survival", "Rank": "5"}, {"AbilityName": "<PERSON><PERSON>", "AbilityID": "8", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "199218", "IdolCost": "53", "UpgradeTime": "343800", "Type": "Survival", "Rank": "6"}, {"AbilityName": "<PERSON><PERSON>", "AbilityID": "8", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "363370", "IdolCost": "75", "UpgradeTime": "439200", "Type": "Survival", "Rank": "7"}, {"AbilityName": "<PERSON><PERSON>", "AbilityID": "8", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "643876", "IdolCost": "113", "UpgradeTime": "634800", "Type": "Survival", "Rank": "8"}, {"AbilityName": "<PERSON><PERSON>", "AbilityID": "8", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "1113984", "IdolCost": "137", "UpgradeTime": "715200", "Type": "Survival", "Rank": "9"}, {"AbilityName": "<PERSON><PERSON>", "AbilityID": "8", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "1890793", "IdolCost": "176", "UpgradeTime": "1024200", "Type": "Survival", "Rank": "10"}, {"AbilityName": "ReduceArmor", "AbilityID": "9", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "180", "Type": "Offensive", "Rank": "1"}, {"AbilityName": "ReduceArmor", "AbilityID": "9", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "9990", "IdolCost": "5", "UpgradeTime": "43200", "Type": "Offensive", "Rank": "2"}, {"AbilityName": "ReduceArmor", "AbilityID": "9", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "25525", "IdolCost": "13", "UpgradeTime": "108000", "Type": "Offensive", "Rank": "3"}, {"AbilityName": "ReduceArmor", "AbilityID": "9", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "55488", "IdolCost": "28", "UpgradeTime": "172800", "Type": "Offensive", "Rank": "4"}, {"AbilityName": "ReduceArmor", "AbilityID": "9", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "111408", "IdolCost": "42", "UpgradeTime": "259200", "Type": "Offensive", "Rank": "5"}, {"AbilityName": "ReduceArmor", "AbilityID": "9", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "211572", "IdolCost": "55", "UpgradeTime": "343800", "Type": "Offensive", "Rank": "6"}, {"AbilityName": "ReduceArmor", "AbilityID": "9", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "384526", "IdolCost": "78", "UpgradeTime": "439200", "Type": "Offensive", "Rank": "7"}, {"AbilityName": "ReduceArmor", "AbilityID": "9", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "679864", "IdolCost": "115", "UpgradeTime": "634800", "Type": "Offensive", "Rank": "8"}, {"AbilityName": "ReduceArmor", "AbilityID": "9", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "1173498", "IdolCost": "140", "UpgradeTime": "715200", "Type": "Offensive", "Rank": "9"}, {"AbilityName": "ReduceArmor", "AbilityID": "9", "BaseClass": "Rogue", "Class": "Rogue", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "1989204", "IdolCost": "180", "UpgradeTime": "1024200", "Type": "Offensive", "Rank": "10"}, {"AbilityName": "FireBlast", "AbilityID": "10", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "438", "IdolCost": "1", "UpgradeTime": "20", "Type": "Attack", "Rank": "1"}, {"AbilityName": "FireBlast", "AbilityID": "10", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "3671", "IdolCost": "2", "UpgradeTime": "43200", "Type": "Attack", "Rank": "2"}, {"AbilityName": "FireBlast", "AbilityID": "10", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "12224", "IdolCost": "7", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "FireBlast", "AbilityID": "10", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "29968", "IdolCost": "15", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "FireBlast", "AbilityID": "10", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "64256", "IdolCost": "33", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "FireBlast", "AbilityID": "10", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "127012", "IdolCost": "44", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "FireBlast", "AbilityID": "10", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "238848", "IdolCost": "59", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "FireBlast", "AbilityID": "10", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "432524", "IdolCost": "85", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "FireBlast", "AbilityID": "10", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "759808", "IdolCost": "119", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "FireBlast", "AbilityID": "10", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "1306458", "IdolCost": "146", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "IceSpike", "AbilityID": "11", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "329", "IdolCost": "1", "UpgradeTime": "40", "Type": "Survival", "Rank": "1"}, {"AbilityName": "IceSpike", "AbilityID": "11", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "4281", "IdolCost": "3", "UpgradeTime": "43200", "Type": "Survival", "Rank": "2"}, {"AbilityName": "IceSpike", "AbilityID": "11", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "13567", "IdolCost": "7", "UpgradeTime": "108000", "Type": "Survival", "Rank": "3"}, {"AbilityName": "IceSpike", "AbilityID": "11", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "32563", "IdolCost": "17", "UpgradeTime": "172800", "Type": "Survival", "Rank": "4"}, {"AbilityName": "IceSpike", "AbilityID": "11", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "69044", "IdolCost": "35", "UpgradeTime": "259200", "Type": "Survival", "Rank": "5"}, {"AbilityName": "IceSpike", "AbilityID": "11", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "136037", "IdolCost": "45", "UpgradeTime": "343800", "Type": "Survival", "Rank": "6"}, {"AbilityName": "IceSpike", "AbilityID": "11", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "254251", "IdolCost": "61", "UpgradeTime": "439200", "Type": "Survival", "Rank": "7"}, {"AbilityName": "IceSpike", "AbilityID": "11", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "458607", "IdolCost": "88", "UpgradeTime": "634800", "Type": "Survival", "Rank": "8"}, {"AbilityName": "IceSpike", "AbilityID": "11", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "804044", "IdolCost": "121", "UpgradeTime": "715200", "Type": "Survival", "Rank": "9"}, {"AbilityName": "IceSpike", "AbilityID": "11", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "1379643", "IdolCost": "150", "UpgradeTime": "1024200", "Type": "Survival", "Rank": "10"}, {"AbilityName": "VineLance", "AbilityID": "12", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "219", "IdolCost": "1", "UpgradeTime": "60", "Type": "Crowd Control", "Rank": "1"}, {"AbilityName": "VineLance", "AbilityID": "12", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "4890", "IdolCost": "3", "UpgradeTime": "43200", "Type": "Crowd Control", "Rank": "2"}, {"AbilityName": "VineLance", "AbilityID": "12", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "14909", "IdolCost": "8", "UpgradeTime": "108000", "Type": "Crowd Control", "Rank": "3"}, {"AbilityName": "VineLance", "AbilityID": "12", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "35158", "IdolCost": "18", "UpgradeTime": "172800", "Type": "Crowd Control", "Rank": "4"}, {"AbilityName": "VineLance", "AbilityID": "12", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "73831", "IdolCost": "37", "UpgradeTime": "259200", "Type": "Crowd Control", "Rank": "5"}, {"AbilityName": "VineLance", "AbilityID": "12", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "145061", "IdolCost": "46", "UpgradeTime": "343800", "Type": "Crowd Control", "Rank": "6"}, {"AbilityName": "VineLance", "AbilityID": "12", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "269653", "IdolCost": "63", "UpgradeTime": "439200", "Type": "Crowd Control", "Rank": "7"}, {"AbilityName": "VineLance", "AbilityID": "12", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "484690", "IdolCost": "92", "UpgradeTime": "634800", "Type": "Crowd Control", "Rank": "8"}, {"AbilityName": "VineLance", "AbilityID": "12", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "848280", "IdolCost": "123", "UpgradeTime": "715200", "Type": "Crowd Control", "Rank": "9"}, {"AbilityName": "VineLance", "AbilityID": "12", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "1452827", "IdolCost": "154", "UpgradeTime": "1024200", "Type": "Crowd Control", "Rank": "10"}, {"AbilityName": "FirePillar", "AbilityID": "13", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "745", "IdolCost": "1", "UpgradeTime": "80", "Type": "Attack", "Rank": "1"}, {"AbilityName": "FirePillar", "AbilityID": "13", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "5594", "IdolCost": "3", "UpgradeTime": "43200", "Type": "Attack", "Rank": "2"}, {"AbilityName": "FirePillar", "AbilityID": "13", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "16419", "IdolCost": "9", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "FirePillar", "AbilityID": "13", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "38176", "IdolCost": "20", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "FirePillar", "AbilityID": "13", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "79501", "IdolCost": "38", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "FirePillar", "AbilityID": "13", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "154805", "IdolCost": "48", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "FirePillar", "AbilityID": "13", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "286973", "IdolCost": "65", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "FirePillar", "AbilityID": "13", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "514014", "IdolCost": "95", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "FirePillar", "AbilityID": "13", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "897069", "IdolCost": "126", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "FirePillar", "AbilityID": "13", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "1533911", "IdolCost": "158", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "14", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "1402", "IdolCost": "1", "UpgradeTime": "100", "Type": "Crowd Control", "Rank": "1"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "14", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "6297", "IdolCost": "4", "UpgradeTime": "43200", "Type": "Crowd Control", "Rank": "2"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "14", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "17929", "IdolCost": "9", "UpgradeTime": "108000", "Type": "Crowd Control", "Rank": "3"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "14", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "41194", "IdolCost": "21", "UpgradeTime": "172800", "Type": "Crowd Control", "Rank": "4"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "14", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "85170", "IdolCost": "38", "UpgradeTime": "259200", "Type": "Crowd Control", "Rank": "5"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "14", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "164549", "IdolCost": "49", "UpgradeTime": "343800", "Type": "Crowd Control", "Rank": "6"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "14", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "304292", "IdolCost": "67", "UpgradeTime": "439200", "Type": "Crowd Control", "Rank": "7"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "14", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "543338", "IdolCost": "99", "UpgradeTime": "634800", "Type": "Crowd Control", "Rank": "8"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "14", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "945857", "IdolCost": "128", "UpgradeTime": "715200", "Type": "Crowd Control", "Rank": "9"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "14", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "1614995", "IdolCost": "162", "UpgradeTime": "1024200", "Type": "Crowd Control", "Rank": "10"}, {"AbilityName": "PoisonCloud", "AbilityID": "15", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "1052", "IdolCost": "1", "UpgradeTime": "120", "Type": "Attack", "Rank": "1"}, {"AbilityName": "PoisonCloud", "AbilityID": "15", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "7158", "IdolCost": "4", "UpgradeTime": "43200", "Type": "Attack", "Rank": "2"}, {"AbilityName": "PoisonCloud", "AbilityID": "15", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "19615", "IdolCost": "10", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "PoisonCloud", "AbilityID": "15", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "44516", "IdolCost": "23", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "PoisonCloud", "AbilityID": "15", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "91339", "IdolCost": "39", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "PoisonCloud", "AbilityID": "15", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "175706", "IdolCost": "50", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "PoisonCloud", "AbilityID": "15", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "323253", "IdolCost": "70", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "PoisonCloud", "AbilityID": "15", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "575613", "IdolCost": "104", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "PoisonCloud", "AbilityID": "15", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "1000164", "IdolCost": "131", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "PoisonCloud", "AbilityID": "15", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "1703688", "IdolCost": "166", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "Meteor", "AbilityID": "16", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "1752", "IdolCost": "1", "UpgradeTime": "140", "Type": "Attack", "Rank": "1"}, {"AbilityName": "Meteor", "AbilityID": "16", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "8019", "IdolCost": "5", "UpgradeTime": "43200", "Type": "Attack", "Rank": "2"}, {"AbilityName": "Meteor", "AbilityID": "16", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "21301", "IdolCost": "11", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "Meteor", "AbilityID": "16", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "47838", "IdolCost": "24", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "Meteor", "AbilityID": "16", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "97507", "IdolCost": "40", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "Meteor", "AbilityID": "16", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "186863", "IdolCost": "52", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "Meteor", "AbilityID": "16", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "342213", "IdolCost": "73", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "Meteor", "AbilityID": "16", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "607888", "IdolCost": "108", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "Meteor", "AbilityID": "16", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "1054470", "IdolCost": "134", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "Meteor", "AbilityID": "16", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "1792381", "IdolCost": "171", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "IceStorm", "AbilityID": "17", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "180", "Type": "Offensive", "Rank": "1"}, {"AbilityName": "IceStorm", "AbilityID": "17", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "9005", "IdolCost": "5", "UpgradeTime": "43200", "Type": "Offensive", "Rank": "2"}, {"AbilityName": "IceStorm", "AbilityID": "17", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "23413", "IdolCost": "12", "UpgradeTime": "108000", "Type": "Offensive", "Rank": "3"}, {"AbilityName": "IceStorm", "AbilityID": "17", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "51663", "IdolCost": "26", "UpgradeTime": "172800", "Type": "Offensive", "Rank": "4"}, {"AbilityName": "IceStorm", "AbilityID": "17", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "104458", "IdolCost": "41", "UpgradeTime": "259200", "Type": "Offensive", "Rank": "5"}, {"AbilityName": "IceStorm", "AbilityID": "17", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "199218", "IdolCost": "53", "UpgradeTime": "343800", "Type": "Offensive", "Rank": "6"}, {"AbilityName": "IceStorm", "AbilityID": "17", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "363370", "IdolCost": "75", "UpgradeTime": "439200", "Type": "Offensive", "Rank": "7"}, {"AbilityName": "IceStorm", "AbilityID": "17", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "643876", "IdolCost": "113", "UpgradeTime": "634800", "Type": "Offensive", "Rank": "8"}, {"AbilityName": "IceStorm", "AbilityID": "17", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "1113984", "IdolCost": "137", "UpgradeTime": "715200", "Type": "Offensive", "Rank": "9"}, {"AbilityName": "IceStorm", "AbilityID": "17", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "1890793", "IdolCost": "176", "UpgradeTime": "1024200", "Type": "Offensive", "Rank": "10"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AbilityID": "18", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "2629", "IdolCost": "2", "UpgradeTime": "180", "Type": "Survival", "Rank": "1"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AbilityID": "18", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "9990", "IdolCost": "5", "UpgradeTime": "43200", "Type": "Survival", "Rank": "2"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AbilityID": "18", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "25525", "IdolCost": "13", "UpgradeTime": "108000", "Type": "Survival", "Rank": "3"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AbilityID": "18", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "55488", "IdolCost": "28", "UpgradeTime": "172800", "Type": "Survival", "Rank": "4"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AbilityID": "18", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "111408", "IdolCost": "42", "UpgradeTime": "259200", "Type": "Survival", "Rank": "5"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AbilityID": "18", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "211572", "IdolCost": "55", "UpgradeTime": "343800", "Type": "Survival", "Rank": "6"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AbilityID": "18", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "384526", "IdolCost": "78", "UpgradeTime": "439200", "Type": "Survival", "Rank": "7"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AbilityID": "18", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "679864", "IdolCost": "115", "UpgradeTime": "634800", "Type": "Survival", "Rank": "8"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AbilityID": "18", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "1173498", "IdolCost": "140", "UpgradeTime": "715200", "Type": "Survival", "Rank": "9"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AbilityID": "18", "BaseClass": "Mage", "Class": "Mage", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "1989204", "IdolCost": "180", "UpgradeTime": "1024200", "Type": "Survival", "Rank": "10"}, {"AbilityName": "Smash", "AbilityID": "19", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "219", "IdolCost": "1", "UpgradeTime": "20", "Type": "Attack", "Rank": "1"}, {"AbilityName": "Smash", "AbilityID": "19", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "3671", "IdolCost": "2", "UpgradeTime": "43200", "Type": "Attack", "Rank": "2"}, {"AbilityName": "Smash", "AbilityID": "19", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "12224", "IdolCost": "7", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "Smash", "AbilityID": "19", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "29968", "IdolCost": "15", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "Smash", "AbilityID": "19", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "64256", "IdolCost": "33", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "Smash", "AbilityID": "19", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "127012", "IdolCost": "44", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "Smash", "AbilityID": "19", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "238848", "IdolCost": "59", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "Smash", "AbilityID": "19", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "432524", "IdolCost": "85", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "Smash", "AbilityID": "19", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "759808", "IdolCost": "119", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "Smash", "AbilityID": "19", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "1306458", "IdolCost": "146", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "Skewer", "AbilityID": "20", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "329", "IdolCost": "1", "UpgradeTime": "40", "Type": "Attack", "Rank": "1"}, {"AbilityName": "Skewer", "AbilityID": "20", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "4281", "IdolCost": "3", "UpgradeTime": "43200", "Type": "Attack", "Rank": "2"}, {"AbilityName": "Skewer", "AbilityID": "20", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "13567", "IdolCost": "7", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "Skewer", "AbilityID": "20", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "32563", "IdolCost": "17", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "Skewer", "AbilityID": "20", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "69044", "IdolCost": "35", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "Skewer", "AbilityID": "20", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "136037", "IdolCost": "45", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "Skewer", "AbilityID": "20", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "254251", "IdolCost": "61", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "Skewer", "AbilityID": "20", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "458607", "IdolCost": "88", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "Skewer", "AbilityID": "20", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "804044", "IdolCost": "121", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "Skewer", "AbilityID": "20", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "1379643", "IdolCost": "150", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "Cleave", "AbilityID": "21", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "438", "IdolCost": "1", "UpgradeTime": "60", "Type": "Attack", "Rank": "1"}, {"AbilityName": "Cleave", "AbilityID": "21", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "4890", "IdolCost": "3", "UpgradeTime": "43200", "Type": "Attack", "Rank": "2"}, {"AbilityName": "Cleave", "AbilityID": "21", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "14909", "IdolCost": "8", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "Cleave", "AbilityID": "21", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "35158", "IdolCost": "18", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "Cleave", "AbilityID": "21", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "73831", "IdolCost": "37", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "Cleave", "AbilityID": "21", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "145061", "IdolCost": "46", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "Cleave", "AbilityID": "21", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "269653", "IdolCost": "63", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "Cleave", "AbilityID": "21", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "484690", "IdolCost": "92", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "Cleave", "AbilityID": "21", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "848280", "IdolCost": "123", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "Cleave", "AbilityID": "21", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "1452827", "IdolCost": "154", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "TouchHeal", "AbilityID": "22", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "745", "IdolCost": "1", "UpgradeTime": "80", "Type": "Survival", "Rank": "1"}, {"AbilityName": "TouchHeal", "AbilityID": "22", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "5594", "IdolCost": "3", "UpgradeTime": "43200", "Type": "Survival", "Rank": "2"}, {"AbilityName": "TouchHeal", "AbilityID": "22", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "16419", "IdolCost": "9", "UpgradeTime": "108000", "Type": "Survival", "Rank": "3"}, {"AbilityName": "TouchHeal", "AbilityID": "22", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "38176", "IdolCost": "20", "UpgradeTime": "172800", "Type": "Survival", "Rank": "4"}, {"AbilityName": "TouchHeal", "AbilityID": "22", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "79501", "IdolCost": "38", "UpgradeTime": "259200", "Type": "Survival", "Rank": "5"}, {"AbilityName": "TouchHeal", "AbilityID": "22", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "154805", "IdolCost": "48", "UpgradeTime": "343800", "Type": "Survival", "Rank": "6"}, {"AbilityName": "TouchHeal", "AbilityID": "22", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "286973", "IdolCost": "65", "UpgradeTime": "439200", "Type": "Survival", "Rank": "7"}, {"AbilityName": "TouchHeal", "AbilityID": "22", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "514014", "IdolCost": "95", "UpgradeTime": "634800", "Type": "Survival", "Rank": "8"}, {"AbilityName": "TouchHeal", "AbilityID": "22", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "897069", "IdolCost": "126", "UpgradeTime": "715200", "Type": "Survival", "Rank": "9"}, {"AbilityName": "TouchHeal", "AbilityID": "22", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "1533911", "IdolCost": "158", "UpgradeTime": "1024200", "Type": "Survival", "Rank": "10"}, {"AbilityName": "Warcry", "AbilityID": "23", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "1052", "IdolCost": "1", "UpgradeTime": "100", "Type": "Offensive", "Rank": "1"}, {"AbilityName": "Warcry", "AbilityID": "23", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "6297", "IdolCost": "4", "UpgradeTime": "43200", "Type": "Offensive", "Rank": "2"}, {"AbilityName": "Warcry", "AbilityID": "23", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "17929", "IdolCost": "9", "UpgradeTime": "108000", "Type": "Offensive", "Rank": "3"}, {"AbilityName": "Warcry", "AbilityID": "23", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "41194", "IdolCost": "21", "UpgradeTime": "172800", "Type": "Offensive", "Rank": "4"}, {"AbilityName": "Warcry", "AbilityID": "23", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "85170", "IdolCost": "38", "UpgradeTime": "259200", "Type": "Offensive", "Rank": "5"}, {"AbilityName": "Warcry", "AbilityID": "23", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "164549", "IdolCost": "49", "UpgradeTime": "343800", "Type": "Offensive", "Rank": "6"}, {"AbilityName": "Warcry", "AbilityID": "23", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "304292", "IdolCost": "67", "UpgradeTime": "439200", "Type": "Offensive", "Rank": "7"}, {"AbilityName": "Warcry", "AbilityID": "23", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "543338", "IdolCost": "99", "UpgradeTime": "634800", "Type": "Offensive", "Rank": "8"}, {"AbilityName": "Warcry", "AbilityID": "23", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "945857", "IdolCost": "128", "UpgradeTime": "715200", "Type": "Offensive", "Rank": "9"}, {"AbilityName": "Warcry", "AbilityID": "23", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "1614995", "IdolCost": "162", "UpgradeTime": "1024200", "Type": "Offensive", "Rank": "10"}, {"AbilityName": "ShieldStun", "AbilityID": "24", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "1402", "IdolCost": "1", "UpgradeTime": "120", "Type": "Crowd Control", "Rank": "1"}, {"AbilityName": "ShieldStun", "AbilityID": "24", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "7158", "IdolCost": "4", "UpgradeTime": "43200", "Type": "Crowd Control", "Rank": "2"}, {"AbilityName": "ShieldStun", "AbilityID": "24", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "19615", "IdolCost": "10", "UpgradeTime": "108000", "Type": "Crowd Control", "Rank": "3"}, {"AbilityName": "ShieldStun", "AbilityID": "24", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "44516", "IdolCost": "23", "UpgradeTime": "172800", "Type": "Crowd Control", "Rank": "4"}, {"AbilityName": "ShieldStun", "AbilityID": "24", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "91339", "IdolCost": "39", "UpgradeTime": "259200", "Type": "Crowd Control", "Rank": "5"}, {"AbilityName": "ShieldStun", "AbilityID": "24", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "175706", "IdolCost": "50", "UpgradeTime": "343800", "Type": "Crowd Control", "Rank": "6"}, {"AbilityName": "ShieldStun", "AbilityID": "24", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "323253", "IdolCost": "70", "UpgradeTime": "439200", "Type": "Crowd Control", "Rank": "7"}, {"AbilityName": "ShieldStun", "AbilityID": "24", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "575613", "IdolCost": "104", "UpgradeTime": "634800", "Type": "Crowd Control", "Rank": "8"}, {"AbilityName": "ShieldStun", "AbilityID": "24", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "1000164", "IdolCost": "131", "UpgradeTime": "715200", "Type": "Crowd Control", "Rank": "9"}, {"AbilityName": "ShieldStun", "AbilityID": "24", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "1703688", "IdolCost": "166", "UpgradeTime": "1024200", "Type": "Crowd Control", "Rank": "10"}, {"AbilityName": "GroupHoT", "AbilityID": "25", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "180", "Type": "Survival", "Rank": "1"}, {"AbilityName": "GroupHoT", "AbilityID": "25", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "8019", "IdolCost": "5", "UpgradeTime": "43200", "Type": "Survival", "Rank": "2"}, {"AbilityName": "GroupHoT", "AbilityID": "25", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "21301", "IdolCost": "11", "UpgradeTime": "108000", "Type": "Survival", "Rank": "3"}, {"AbilityName": "GroupHoT", "AbilityID": "25", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "47838", "IdolCost": "24", "UpgradeTime": "172800", "Type": "Survival", "Rank": "4"}, {"AbilityName": "GroupHoT", "AbilityID": "25", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "97507", "IdolCost": "40", "UpgradeTime": "259200", "Type": "Survival", "Rank": "5"}, {"AbilityName": "GroupHoT", "AbilityID": "25", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "186863", "IdolCost": "52", "UpgradeTime": "343800", "Type": "Survival", "Rank": "6"}, {"AbilityName": "GroupHoT", "AbilityID": "25", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "342213", "IdolCost": "73", "UpgradeTime": "439200", "Type": "Survival", "Rank": "7"}, {"AbilityName": "GroupHoT", "AbilityID": "25", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "607888", "IdolCost": "108", "UpgradeTime": "634800", "Type": "Survival", "Rank": "8"}, {"AbilityName": "GroupHoT", "AbilityID": "25", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "1054470", "IdolCost": "134", "UpgradeTime": "715200", "Type": "Survival", "Rank": "9"}, {"AbilityName": "GroupHoT", "AbilityID": "25", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "1792381", "IdolCost": "171", "UpgradeTime": "1024200", "Type": "Survival", "Rank": "10"}, {"AbilityName": "ToughShout", "AbilityID": "26", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Protection", "GoldCost": "2191", "IdolCost": "2", "UpgradeTime": "160", "Type": "Survival", "Rank": "1"}, {"AbilityName": "ToughShout", "AbilityID": "26", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Protection", "GoldCost": "9005", "IdolCost": "5", "UpgradeTime": "43200", "Type": "Survival", "Rank": "2"}, {"AbilityName": "ToughShout", "AbilityID": "26", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Protection", "GoldCost": "23413", "IdolCost": "12", "UpgradeTime": "108000", "Type": "Survival", "Rank": "3"}, {"AbilityName": "ToughShout", "AbilityID": "26", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Protection", "GoldCost": "51663", "IdolCost": "26", "UpgradeTime": "172800", "Type": "Survival", "Rank": "4"}, {"AbilityName": "ToughShout", "AbilityID": "26", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Protection", "GoldCost": "104458", "IdolCost": "41", "UpgradeTime": "259200", "Type": "Survival", "Rank": "5"}, {"AbilityName": "ToughShout", "AbilityID": "26", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Protection", "GoldCost": "199218", "IdolCost": "53", "UpgradeTime": "343800", "Type": "Survival", "Rank": "6"}, {"AbilityName": "ToughShout", "AbilityID": "26", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Protection", "GoldCost": "363370", "IdolCost": "75", "UpgradeTime": "439200", "Type": "Survival", "Rank": "7"}, {"AbilityName": "ToughShout", "AbilityID": "26", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Protection", "GoldCost": "643876", "IdolCost": "113", "UpgradeTime": "634800", "Type": "Survival", "Rank": "8"}, {"AbilityName": "ToughShout", "AbilityID": "26", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Protection", "GoldCost": "1113984", "IdolCost": "137", "UpgradeTime": "715200", "Type": "Survival", "Rank": "9"}, {"AbilityName": "ToughShout", "AbilityID": "26", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Protection", "GoldCost": "1890793", "IdolCost": "176", "UpgradeTime": "1024200", "Type": "Survival", "Rank": "10"}, {"AbilityName": "JumpSlam", "AbilityID": "27", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "2629", "IdolCost": "2", "UpgradeTime": "180", "Type": "Offensive", "Rank": "1"}, {"AbilityName": "JumpSlam", "AbilityID": "27", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "9990", "IdolCost": "5", "UpgradeTime": "43200", "Type": "Offensive", "Rank": "2"}, {"AbilityName": "JumpSlam", "AbilityID": "27", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "25525", "IdolCost": "13", "UpgradeTime": "108000", "Type": "Offensive", "Rank": "3"}, {"AbilityName": "JumpSlam", "AbilityID": "27", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "55488", "IdolCost": "28", "UpgradeTime": "172800", "Type": "Offensive", "Rank": "4"}, {"AbilityName": "JumpSlam", "AbilityID": "27", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "111408", "IdolCost": "42", "UpgradeTime": "259200", "Type": "Offensive", "Rank": "5"}, {"AbilityName": "JumpSlam", "AbilityID": "27", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "211572", "IdolCost": "55", "UpgradeTime": "343800", "Type": "Offensive", "Rank": "6"}, {"AbilityName": "JumpSlam", "AbilityID": "27", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "384526", "IdolCost": "78", "UpgradeTime": "439200", "Type": "Offensive", "Rank": "7"}, {"AbilityName": "JumpSlam", "AbilityID": "27", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "679864", "IdolCost": "115", "UpgradeTime": "634800", "Type": "Offensive", "Rank": "8"}, {"AbilityName": "JumpSlam", "AbilityID": "27", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "1173498", "IdolCost": "140", "UpgradeTime": "715200", "Type": "Offensive", "Rank": "9"}, {"AbilityName": "JumpSlam", "AbilityID": "27", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "<PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "1989204", "IdolCost": "180", "UpgradeTime": "1024200", "Type": "Offensive", "Rank": "10"}, {"AbilityName": "FrostBlast", "AbilityID": "28", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "9990", "IdolCost": "5", "UpgradeTime": "45", "Type": "Offensive", "Rank": "1"}, {"AbilityName": "FrostBlast", "AbilityID": "28", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "25525", "IdolCost": "13", "UpgradeTime": "68400", "Type": "Offensive", "Rank": "2"}, {"AbilityName": "FrostBlast", "AbilityID": "28", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "55488", "IdolCost": "28", "UpgradeTime": "108000", "Type": "Offensive", "Rank": "3"}, {"AbilityName": "FrostBlast", "AbilityID": "28", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "111408", "IdolCost": "42", "UpgradeTime": "172800", "Type": "Offensive", "Rank": "4"}, {"AbilityName": "FrostBlast", "AbilityID": "28", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "211572", "IdolCost": "55", "UpgradeTime": "259200", "Type": "Offensive", "Rank": "5"}, {"AbilityName": "FrostBlast", "AbilityID": "28", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "384526", "IdolCost": "78", "UpgradeTime": "343800", "Type": "Offensive", "Rank": "6"}, {"AbilityName": "FrostBlast", "AbilityID": "28", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "679864", "IdolCost": "115", "UpgradeTime": "439200", "Type": "Offensive", "Rank": "7"}, {"AbilityName": "FrostBlast", "AbilityID": "28", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "1173498", "IdolCost": "140", "UpgradeTime": "634800", "Type": "Offensive", "Rank": "8"}, {"AbilityName": "FrostBlast", "AbilityID": "28", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "1989204", "IdolCost": "180", "UpgradeTime": "715200", "Type": "Offensive", "Rank": "9"}, {"AbilityName": "FrostBlast", "AbilityID": "28", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "2922793", "IdolCost": "202", "UpgradeTime": "1024200", "Type": "Offensive", "Rank": "10"}, {"AbilityName": "FrozenWard", "AbilityID": "29", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "11107", "IdolCost": "6", "UpgradeTime": "60", "Type": "Crowd Control", "Rank": "1"}, {"AbilityName": "FrozenWard", "AbilityID": "29", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "27747", "IdolCost": "14", "UpgradeTime": "68400", "Type": "Crowd Control", "Rank": "2"}, {"AbilityName": "FrozenWard", "AbilityID": "29", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "59872", "IdolCost": "30", "UpgradeTime": "108000", "Type": "Crowd Control", "Rank": "3"}, {"AbilityName": "FrozenWard", "AbilityID": "29", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "119210", "IdolCost": "43", "UpgradeTime": "172800", "Type": "Crowd Control", "Rank": "4"}, {"AbilityName": "FrozenWard", "AbilityID": "29", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "225210", "IdolCost": "57", "UpgradeTime": "259200", "Type": "Crowd Control", "Rank": "5"}, {"AbilityName": "FrozenWard", "AbilityID": "29", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "408525", "IdolCost": "81", "UpgradeTime": "343800", "Type": "Crowd Control", "Rank": "6"}, {"AbilityName": "FrozenWard", "AbilityID": "29", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "719836", "IdolCost": "117", "UpgradeTime": "439200", "Type": "Crowd Control", "Rank": "7"}, {"AbilityName": "FrozenWard", "AbilityID": "29", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "1239978", "IdolCost": "143", "UpgradeTime": "634800", "Type": "Crowd Control", "Rank": "8"}, {"AbilityName": "FrozenWard", "AbilityID": "29", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "2068772", "IdolCost": "184", "UpgradeTime": "715200", "Type": "Crowd Control", "Rank": "9"}, {"AbilityName": "FrozenWard", "AbilityID": "29", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "1", "Category": "<PERSON>", "GoldCost": "3039705", "IdolCost": "204", "UpgradeTime": "1024200", "Type": "Crowd Control", "Rank": "10"}, {"AbilityName": "FrigidComet", "AbilityID": "30", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "12224", "IdolCost": "7", "UpgradeTime": "75", "Type": "Offensive", "Rank": "1"}, {"AbilityName": "FrigidComet", "AbilityID": "30", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "29968", "IdolCost": "15", "UpgradeTime": "68400", "Type": "Offensive", "Rank": "2"}, {"AbilityName": "FrigidComet", "AbilityID": "30", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "64256", "IdolCost": "33", "UpgradeTime": "108000", "Type": "Offensive", "Rank": "3"}, {"AbilityName": "FrigidComet", "AbilityID": "30", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "127012", "IdolCost": "44", "UpgradeTime": "172800", "Type": "Offensive", "Rank": "4"}, {"AbilityName": "FrigidComet", "AbilityID": "30", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "238848", "IdolCost": "59", "UpgradeTime": "259200", "Type": "Offensive", "Rank": "5"}, {"AbilityName": "FrigidComet", "AbilityID": "30", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "432524", "IdolCost": "85", "UpgradeTime": "343800", "Type": "Offensive", "Rank": "6"}, {"AbilityName": "FrigidComet", "AbilityID": "30", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "759808", "IdolCost": "119", "UpgradeTime": "439200", "Type": "Offensive", "Rank": "7"}, {"AbilityName": "FrigidComet", "AbilityID": "30", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "1306458", "IdolCost": "146", "UpgradeTime": "634800", "Type": "Offensive", "Rank": "8"}, {"AbilityName": "FrigidComet", "AbilityID": "30", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "2148340", "IdolCost": "186", "UpgradeTime": "715200", "Type": "Offensive", "Rank": "9"}, {"AbilityName": "FrigidComet", "AbilityID": "30", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "3156616", "IdolCost": "206", "UpgradeTime": "1024200", "Type": "Offensive", "Rank": "10"}, {"AbilityName": "BitterBlade", "AbilityID": "31", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "13567", "IdolCost": "7", "UpgradeTime": "90", "Type": "Offensive", "Rank": "1"}, {"AbilityName": "BitterBlade", "AbilityID": "31", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "32563", "IdolCost": "17", "UpgradeTime": "68400", "Type": "Offensive", "Rank": "2"}, {"AbilityName": "BitterBlade", "AbilityID": "31", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "69044", "IdolCost": "35", "UpgradeTime": "108000", "Type": "Offensive", "Rank": "3"}, {"AbilityName": "BitterBlade", "AbilityID": "31", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "136037", "IdolCost": "45", "UpgradeTime": "172800", "Type": "Offensive", "Rank": "4"}, {"AbilityName": "BitterBlade", "AbilityID": "31", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "254251", "IdolCost": "61", "UpgradeTime": "259200", "Type": "Offensive", "Rank": "5"}, {"AbilityName": "BitterBlade", "AbilityID": "31", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "458607", "IdolCost": "88", "UpgradeTime": "343800", "Type": "Offensive", "Rank": "6"}, {"AbilityName": "BitterBlade", "AbilityID": "31", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "804044", "IdolCost": "121", "UpgradeTime": "439200", "Type": "Offensive", "Rank": "7"}, {"AbilityName": "BitterBlade", "AbilityID": "31", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "1379643", "IdolCost": "150", "UpgradeTime": "634800", "Type": "Offensive", "Rank": "8"}, {"AbilityName": "BitterBlade", "AbilityID": "31", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "2234274", "IdolCost": "188", "UpgradeTime": "715200", "Type": "Offensive", "Rank": "9"}, {"AbilityName": "BitterBlade", "AbilityID": "31", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "2", "Category": "<PERSON>", "GoldCost": "3282881", "IdolCost": "209", "UpgradeTime": "1024200", "Type": "Offensive", "Rank": "10"}, {"AbilityName": "Avalanche", "AbilityID": "32", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "14909", "IdolCost": "8", "UpgradeTime": "105", "Type": "Crowd Control", "Rank": "1"}, {"AbilityName": "Avalanche", "AbilityID": "32", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "35158", "IdolCost": "18", "UpgradeTime": "68400", "Type": "Crowd Control", "Rank": "2"}, {"AbilityName": "Avalanche", "AbilityID": "32", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "73831", "IdolCost": "37", "UpgradeTime": "108000", "Type": "Crowd Control", "Rank": "3"}, {"AbilityName": "Avalanche", "AbilityID": "32", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "145061", "IdolCost": "46", "UpgradeTime": "172800", "Type": "Crowd Control", "Rank": "4"}, {"AbilityName": "Avalanche", "AbilityID": "32", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "269653", "IdolCost": "63", "UpgradeTime": "259200", "Type": "Crowd Control", "Rank": "5"}, {"AbilityName": "Avalanche", "AbilityID": "32", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "484690", "IdolCost": "92", "UpgradeTime": "343800", "Type": "Crowd Control", "Rank": "6"}, {"AbilityName": "Avalanche", "AbilityID": "32", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "848280", "IdolCost": "123", "UpgradeTime": "439200", "Type": "Crowd Control", "Rank": "7"}, {"AbilityName": "Avalanche", "AbilityID": "32", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "1452827", "IdolCost": "154", "UpgradeTime": "634800", "Type": "Crowd Control", "Rank": "8"}, {"AbilityName": "Avalanche", "AbilityID": "32", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "2320207", "IdolCost": "190", "UpgradeTime": "715200", "Type": "Crowd Control", "Rank": "9"}, {"AbilityName": "Avalanche", "AbilityID": "32", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "3409145", "IdolCost": "211", "UpgradeTime": "1024200", "Type": "Crowd Control", "Rank": "10"}, {"AbilityName": "GlacialSpear", "AbilityID": "33", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "16419", "IdolCost": "9", "UpgradeTime": "120", "Type": "Offensive", "Rank": "1"}, {"AbilityName": "GlacialSpear", "AbilityID": "33", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "38176", "IdolCost": "20", "UpgradeTime": "68400", "Type": "Offensive", "Rank": "2"}, {"AbilityName": "GlacialSpear", "AbilityID": "33", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "79501", "IdolCost": "38", "UpgradeTime": "108000", "Type": "Offensive", "Rank": "3"}, {"AbilityName": "GlacialSpear", "AbilityID": "33", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "154805", "IdolCost": "48", "UpgradeTime": "172800", "Type": "Offensive", "Rank": "4"}, {"AbilityName": "GlacialSpear", "AbilityID": "33", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "286973", "IdolCost": "65", "UpgradeTime": "259200", "Type": "Offensive", "Rank": "5"}, {"AbilityName": "GlacialSpear", "AbilityID": "33", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "514014", "IdolCost": "95", "UpgradeTime": "343800", "Type": "Offensive", "Rank": "6"}, {"AbilityName": "GlacialSpear", "AbilityID": "33", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "897069", "IdolCost": "126", "UpgradeTime": "439200", "Type": "Offensive", "Rank": "7"}, {"AbilityName": "GlacialSpear", "AbilityID": "33", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "1533911", "IdolCost": "158", "UpgradeTime": "634800", "Type": "Offensive", "Rank": "8"}, {"AbilityName": "GlacialSpear", "AbilityID": "33", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "2413016", "IdolCost": "191", "UpgradeTime": "715200", "Type": "Offensive", "Rank": "9"}, {"AbilityName": "GlacialSpear", "AbilityID": "33", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "3", "Category": "<PERSON>", "GoldCost": "3545511", "IdolCost": "214", "UpgradeTime": "1024200", "Type": "Offensive", "Rank": "10"}, {"AbilityName": "PermafrostClone", "AbilityID": "34", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "4", "Category": "<PERSON>", "GoldCost": "17929", "IdolCost": "9", "UpgradeTime": "135", "Type": "<PERSON>", "Rank": "1"}, {"AbilityName": "PermafrostClone", "AbilityID": "34", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "4", "Category": "<PERSON>", "GoldCost": "41194", "IdolCost": "21", "UpgradeTime": "75600", "Type": "<PERSON>", "Rank": "2"}, {"AbilityName": "PermafrostClone", "AbilityID": "34", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "4", "Category": "<PERSON>", "GoldCost": "85170", "IdolCost": "38", "UpgradeTime": "108000", "Type": "<PERSON>", "Rank": "3"}, {"AbilityName": "PermafrostClone", "AbilityID": "34", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "4", "Category": "<PERSON>", "GoldCost": "164549", "IdolCost": "49", "UpgradeTime": "172800", "Type": "<PERSON>", "Rank": "4"}, {"AbilityName": "PermafrostClone", "AbilityID": "34", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "4", "Category": "<PERSON>", "GoldCost": "304292", "IdolCost": "67", "UpgradeTime": "259200", "Type": "<PERSON>", "Rank": "5"}, {"AbilityName": "PermafrostClone", "AbilityID": "34", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "4", "Category": "<PERSON>", "GoldCost": "543338", "IdolCost": "99", "UpgradeTime": "343800", "Type": "<PERSON>", "Rank": "6"}, {"AbilityName": "PermafrostClone", "AbilityID": "34", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "4", "Category": "<PERSON>", "GoldCost": "945857", "IdolCost": "128", "UpgradeTime": "439200", "Type": "<PERSON>", "Rank": "7"}, {"AbilityName": "PermafrostClone", "AbilityID": "34", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "4", "Category": "<PERSON>", "GoldCost": "1614995", "IdolCost": "162", "UpgradeTime": "634800", "Type": "<PERSON>", "Rank": "8"}, {"AbilityName": "PermafrostClone", "AbilityID": "34", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "4", "Category": "<PERSON>", "GoldCost": "2505824", "IdolCost": "193", "UpgradeTime": "715200", "Type": "<PERSON>", "Rank": "9"}, {"AbilityName": "PermafrostClone", "AbilityID": "34", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "4", "Category": "<PERSON>", "GoldCost": "3681877", "IdolCost": "217", "UpgradeTime": "1024200", "Type": "<PERSON>", "Rank": "10"}, {"AbilityName": "PolarSentry", "AbilityID": "35", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "5", "Category": "<PERSON>", "GoldCost": "19615", "IdolCost": "10", "UpgradeTime": "150", "Type": "<PERSON>", "Rank": "1"}, {"AbilityName": "PolarSentry", "AbilityID": "35", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "5", "Category": "<PERSON>", "GoldCost": "44516", "IdolCost": "23", "UpgradeTime": "75600", "Type": "<PERSON>", "Rank": "2"}, {"AbilityName": "PolarSentry", "AbilityID": "35", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "5", "Category": "<PERSON>", "GoldCost": "91339", "IdolCost": "39", "UpgradeTime": "108000", "Type": "<PERSON>", "Rank": "3"}, {"AbilityName": "PolarSentry", "AbilityID": "35", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "5", "Category": "<PERSON>", "GoldCost": "175706", "IdolCost": "50", "UpgradeTime": "172800", "Type": "<PERSON>", "Rank": "4"}, {"AbilityName": "PolarSentry", "AbilityID": "35", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "5", "Category": "<PERSON>", "GoldCost": "323253", "IdolCost": "70", "UpgradeTime": "259200", "Type": "<PERSON>", "Rank": "5"}, {"AbilityName": "PolarSentry", "AbilityID": "35", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "5", "Category": "<PERSON>", "GoldCost": "575613", "IdolCost": "104", "UpgradeTime": "343800", "Type": "<PERSON>", "Rank": "6"}, {"AbilityName": "PolarSentry", "AbilityID": "35", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "5", "Category": "<PERSON>", "GoldCost": "1000164", "IdolCost": "131", "UpgradeTime": "439200", "Type": "<PERSON>", "Rank": "7"}, {"AbilityName": "PolarSentry", "AbilityID": "35", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "5", "Category": "<PERSON>", "GoldCost": "1703688", "IdolCost": "166", "UpgradeTime": "634800", "Type": "<PERSON>", "Rank": "8"}, {"AbilityName": "PolarSentry", "AbilityID": "35", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "5", "Category": "<PERSON>", "GoldCost": "2606057", "IdolCost": "195", "UpgradeTime": "715200", "Type": "<PERSON>", "Rank": "9"}, {"AbilityName": "PolarSentry", "AbilityID": "35", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "5", "Category": "<PERSON>", "GoldCost": "3829152", "IdolCost": "220", "UpgradeTime": "1024200", "Type": "<PERSON>", "Rank": "10"}, {"AbilityName": "HailstoneEmbrace", "AbilityID": "36", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "6", "Category": "<PERSON>", "GoldCost": "21301", "IdolCost": "11", "UpgradeTime": "165", "Type": "<PERSON>", "Rank": "1"}, {"AbilityName": "HailstoneEmbrace", "AbilityID": "36", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "6", "Category": "<PERSON>", "GoldCost": "47838", "IdolCost": "24", "UpgradeTime": "75600", "Type": "<PERSON>", "Rank": "2"}, {"AbilityName": "HailstoneEmbrace", "AbilityID": "36", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "6", "Category": "<PERSON>", "GoldCost": "97507", "IdolCost": "40", "UpgradeTime": "108000", "Type": "<PERSON>", "Rank": "3"}, {"AbilityName": "HailstoneEmbrace", "AbilityID": "36", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "6", "Category": "<PERSON>", "GoldCost": "186863", "IdolCost": "52", "UpgradeTime": "172800", "Type": "<PERSON>", "Rank": "4"}, {"AbilityName": "HailstoneEmbrace", "AbilityID": "36", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "6", "Category": "<PERSON>", "GoldCost": "342213", "IdolCost": "73", "UpgradeTime": "259200", "Type": "<PERSON>", "Rank": "5"}, {"AbilityName": "HailstoneEmbrace", "AbilityID": "36", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "6", "Category": "<PERSON>", "GoldCost": "607888", "IdolCost": "108", "UpgradeTime": "343800", "Type": "<PERSON>", "Rank": "6"}, {"AbilityName": "HailstoneEmbrace", "AbilityID": "36", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "6", "Category": "<PERSON>", "GoldCost": "1054470", "IdolCost": "134", "UpgradeTime": "439200", "Type": "<PERSON>", "Rank": "7"}, {"AbilityName": "HailstoneEmbrace", "AbilityID": "36", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "6", "Category": "<PERSON>", "GoldCost": "1792381", "IdolCost": "171", "UpgradeTime": "634800", "Type": "<PERSON>", "Rank": "8"}, {"AbilityName": "HailstoneEmbrace", "AbilityID": "36", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "6", "Category": "<PERSON>", "GoldCost": "2706290", "IdolCost": "197", "UpgradeTime": "715200", "Type": "<PERSON>", "Rank": "9"}, {"AbilityName": "HailstoneEmbrace", "AbilityID": "36", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "6", "Category": "<PERSON>", "GoldCost": "3976427", "IdolCost": "223", "UpgradeTime": "1024200", "Type": "<PERSON>", "Rank": "10"}, {"AbilityName": "FrostBolt", "AbilityID": "37", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "0", "Category": "<PERSON>", "GoldCost": "15229", "IdolCost": "8", "UpgradeTime": "180", "Type": "<PERSON>", "Rank": "1"}, {"AbilityName": "FrostBolt", "AbilityID": "37", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "0", "Category": "<PERSON>", "GoldCost": "35854", "IdolCost": "18", "UpgradeTime": "72000", "Type": "<PERSON>", "Rank": "2"}, {"AbilityName": "FrostBolt", "AbilityID": "37", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "0", "Category": "<PERSON>", "GoldCost": "75112", "IdolCost": "37", "UpgradeTime": "108000", "Type": "<PERSON>", "Rank": "3"}, {"AbilityName": "FrostBolt", "AbilityID": "37", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "0", "Category": "<PERSON>", "GoldCost": "146739", "IdolCost": "46", "UpgradeTime": "172800", "Type": "<PERSON>", "Rank": "4"}, {"AbilityName": "FrostBolt", "AbilityID": "37", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "0", "Category": "<PERSON>", "GoldCost": "272918", "IdolCost": "63", "UpgradeTime": "259200", "Type": "<PERSON>", "Rank": "5"}, {"AbilityName": "FrostBolt", "AbilityID": "37", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "0", "Category": "<PERSON>", "GoldCost": "489969", "IdolCost": "92", "UpgradeTime": "343800", "Type": "<PERSON>", "Rank": "6"}, {"AbilityName": "FrostBolt", "AbilityID": "37", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "0", "Category": "<PERSON>", "GoldCost": "856599", "IdolCost": "124", "UpgradeTime": "439200", "Type": "<PERSON>", "Rank": "7"}, {"AbilityName": "FrostBolt", "AbilityID": "37", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "0", "Category": "<PERSON>", "GoldCost": "1466375", "IdolCost": "154", "UpgradeTime": "634800", "Type": "<PERSON>", "Rank": "8"}, {"AbilityName": "FrostBolt", "AbilityID": "37", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "0", "Category": "<PERSON>", "GoldCost": "2332443", "IdolCost": "190", "UpgradeTime": "715200", "Type": "<PERSON>", "Rank": "9"}, {"AbilityName": "FrostBolt", "AbilityID": "37", "BaseClass": "Mage", "Class": "Frostwarden", "HotbarLocation": "0", "Category": "<PERSON>", "GoldCost": "3427123", "IdolCost": "212", "UpgradeTime": "1024200", "Type": "<PERSON>", "Rank": "10"}, {"AbilityName": "SeverStrike", "AbilityID": "38", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "9990", "IdolCost": "5", "UpgradeTime": "45", "Type": "Attack", "Rank": "1"}, {"AbilityName": "SeverStrike", "AbilityID": "38", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "25525", "IdolCost": "13", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "SeverStrike", "AbilityID": "38", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "55488", "IdolCost": "28", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "SeverStrike", "AbilityID": "38", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "111408", "IdolCost": "42", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "SeverStrike", "AbilityID": "38", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "211572", "IdolCost": "55", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "SeverStrike", "AbilityID": "38", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "384526", "IdolCost": "78", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "SeverStrike", "AbilityID": "38", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "679864", "IdolCost": "115", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "SeverStrike", "AbilityID": "38", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "1173498", "IdolCost": "140", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "SeverStrike", "AbilityID": "38", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "1989204", "IdolCost": "180", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "SeverStrike", "AbilityID": "38", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "2922793", "IdolCost": "202", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "WitherStrike", "AbilityID": "39", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "11107", "IdolCost": "6", "UpgradeTime": "60", "Type": "Attack", "Rank": "1"}, {"AbilityName": "WitherStrike", "AbilityID": "39", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "27747", "IdolCost": "14", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "WitherStrike", "AbilityID": "39", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "59872", "IdolCost": "30", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "WitherStrike", "AbilityID": "39", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "119210", "IdolCost": "43", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "WitherStrike", "AbilityID": "39", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "225210", "IdolCost": "57", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "WitherStrike", "AbilityID": "39", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "408525", "IdolCost": "81", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "WitherStrike", "AbilityID": "39", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "719836", "IdolCost": "117", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "WitherStrike", "AbilityID": "39", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "1239978", "IdolCost": "143", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "WitherStrike", "AbilityID": "39", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "2068772", "IdolCost": "184", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "WitherStrike", "AbilityID": "39", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "3039705", "IdolCost": "204", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "Dagger<PERSON><PERSON>rry", "AbilityID": "40", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "12224", "IdolCost": "7", "UpgradeTime": "75", "Type": "Attack", "Rank": "1"}, {"AbilityName": "Dagger<PERSON><PERSON>rry", "AbilityID": "40", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "29968", "IdolCost": "15", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "Dagger<PERSON><PERSON>rry", "AbilityID": "40", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "64256", "IdolCost": "33", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "Dagger<PERSON><PERSON>rry", "AbilityID": "40", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "127012", "IdolCost": "44", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "Dagger<PERSON><PERSON>rry", "AbilityID": "40", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "238848", "IdolCost": "59", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "Dagger<PERSON><PERSON>rry", "AbilityID": "40", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "432524", "IdolCost": "85", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "Dagger<PERSON><PERSON>rry", "AbilityID": "40", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "759808", "IdolCost": "119", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "Dagger<PERSON><PERSON>rry", "AbilityID": "40", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "1306458", "IdolCost": "146", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "Dagger<PERSON><PERSON>rry", "AbilityID": "40", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "2148340", "IdolCost": "186", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "Dagger<PERSON><PERSON>rry", "AbilityID": "40", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "3156616", "IdolCost": "206", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "VitalStrike", "AbilityID": "41", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "13567", "IdolCost": "7", "UpgradeTime": "90", "Type": "Attack", "Rank": "1"}, {"AbilityName": "VitalStrike", "AbilityID": "41", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "32563", "IdolCost": "17", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "VitalStrike", "AbilityID": "41", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "69044", "IdolCost": "35", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "VitalStrike", "AbilityID": "41", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "136037", "IdolCost": "45", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "VitalStrike", "AbilityID": "41", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "254251", "IdolCost": "61", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "VitalStrike", "AbilityID": "41", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "458607", "IdolCost": "88", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "VitalStrike", "AbilityID": "41", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "804044", "IdolCost": "121", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "VitalStrike", "AbilityID": "41", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "1379643", "IdolCost": "150", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "VitalStrike", "AbilityID": "41", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "2234274", "IdolCost": "188", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "VitalStrike", "AbilityID": "41", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "3282881", "IdolCost": "209", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "Assassinate", "AbilityID": "42", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "14909", "IdolCost": "8", "UpgradeTime": "105", "Type": "Attack", "Rank": "1"}, {"AbilityName": "Assassinate", "AbilityID": "42", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "35158", "IdolCost": "18", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "Assassinate", "AbilityID": "42", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "73831", "IdolCost": "37", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "Assassinate", "AbilityID": "42", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "145061", "IdolCost": "46", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "Assassinate", "AbilityID": "42", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "269653", "IdolCost": "63", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "Assassinate", "AbilityID": "42", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "484690", "IdolCost": "92", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "Assassinate", "AbilityID": "42", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "848280", "IdolCost": "123", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "Assassinate", "AbilityID": "42", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "1452827", "IdolCost": "154", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "Assassinate", "AbilityID": "42", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "2320207", "IdolCost": "190", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "Assassinate", "AbilityID": "42", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "3409145", "IdolCost": "211", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "DeathBlowOld", "AbilityID": "43", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "16419", "IdolCost": "9", "UpgradeTime": "120", "Type": "Attack", "Rank": "1"}, {"AbilityName": "DeathBlowOld", "AbilityID": "43", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "38176", "IdolCost": "20", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "DeathBlowOld", "AbilityID": "43", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "79501", "IdolCost": "38", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "DeathBlowOld", "AbilityID": "43", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "154805", "IdolCost": "48", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "DeathBlowOld", "AbilityID": "43", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "286973", "IdolCost": "65", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "DeathBlowOld", "AbilityID": "43", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "514014", "IdolCost": "95", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "DeathBlowOld", "AbilityID": "43", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "897069", "IdolCost": "126", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "DeathBlowOld", "AbilityID": "43", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "1533911", "IdolCost": "158", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "DeathBlowOld", "AbilityID": "43", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "2413016", "IdolCost": "191", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "DeathBlowOld", "AbilityID": "43", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "3", "Category": "Assault", "GoldCost": "3545511", "IdolCost": "214", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "MistWalk", "AbilityID": "44", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "4", "Category": "Assault", "GoldCost": "17929", "IdolCost": "9", "UpgradeTime": "135", "Type": "Execution", "Rank": "1"}, {"AbilityName": "MistWalk", "AbilityID": "44", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "4", "Category": "Assault", "GoldCost": "41194", "IdolCost": "21", "UpgradeTime": "75600", "Type": "Execution", "Rank": "2"}, {"AbilityName": "MistWalk", "AbilityID": "44", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "4", "Category": "Assault", "GoldCost": "85170", "IdolCost": "38", "UpgradeTime": "108000", "Type": "Execution", "Rank": "3"}, {"AbilityName": "MistWalk", "AbilityID": "44", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "4", "Category": "Assault", "GoldCost": "164549", "IdolCost": "49", "UpgradeTime": "172800", "Type": "Execution", "Rank": "4"}, {"AbilityName": "MistWalk", "AbilityID": "44", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "4", "Category": "Assault", "GoldCost": "304292", "IdolCost": "67", "UpgradeTime": "259200", "Type": "Execution", "Rank": "5"}, {"AbilityName": "MistWalk", "AbilityID": "44", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "4", "Category": "Assault", "GoldCost": "543338", "IdolCost": "99", "UpgradeTime": "343800", "Type": "Execution", "Rank": "6"}, {"AbilityName": "MistWalk", "AbilityID": "44", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "4", "Category": "Assault", "GoldCost": "945857", "IdolCost": "128", "UpgradeTime": "439200", "Type": "Execution", "Rank": "7"}, {"AbilityName": "MistWalk", "AbilityID": "44", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "4", "Category": "Assault", "GoldCost": "1614995", "IdolCost": "162", "UpgradeTime": "634800", "Type": "Execution", "Rank": "8"}, {"AbilityName": "MistWalk", "AbilityID": "44", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "4", "Category": "Assault", "GoldCost": "2505824", "IdolCost": "193", "UpgradeTime": "715200", "Type": "Execution", "Rank": "9"}, {"AbilityName": "MistWalk", "AbilityID": "44", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "4", "Category": "Assault", "GoldCost": "3681877", "IdolCost": "217", "UpgradeTime": "1024200", "Type": "Execution", "Rank": "10"}, {"AbilityName": "ShadowBlade", "AbilityID": "45", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "5", "Category": "Assault", "GoldCost": "19615", "IdolCost": "10", "UpgradeTime": "150", "Type": "Execution", "Rank": "1"}, {"AbilityName": "ShadowBlade", "AbilityID": "45", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "5", "Category": "Assault", "GoldCost": "44516", "IdolCost": "23", "UpgradeTime": "75600", "Type": "Execution", "Rank": "2"}, {"AbilityName": "ShadowBlade", "AbilityID": "45", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "5", "Category": "Assault", "GoldCost": "91339", "IdolCost": "39", "UpgradeTime": "108000", "Type": "Execution", "Rank": "3"}, {"AbilityName": "ShadowBlade", "AbilityID": "45", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "5", "Category": "Assault", "GoldCost": "175706", "IdolCost": "50", "UpgradeTime": "172800", "Type": "Execution", "Rank": "4"}, {"AbilityName": "ShadowBlade", "AbilityID": "45", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "5", "Category": "Assault", "GoldCost": "323253", "IdolCost": "70", "UpgradeTime": "259200", "Type": "Execution", "Rank": "5"}, {"AbilityName": "ShadowBlade", "AbilityID": "45", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "5", "Category": "Assault", "GoldCost": "575613", "IdolCost": "104", "UpgradeTime": "343800", "Type": "Execution", "Rank": "6"}, {"AbilityName": "ShadowBlade", "AbilityID": "45", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "5", "Category": "Assault", "GoldCost": "1000164", "IdolCost": "131", "UpgradeTime": "439200", "Type": "Execution", "Rank": "7"}, {"AbilityName": "ShadowBlade", "AbilityID": "45", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "5", "Category": "Assault", "GoldCost": "1703688", "IdolCost": "166", "UpgradeTime": "634800", "Type": "Execution", "Rank": "8"}, {"AbilityName": "ShadowBlade", "AbilityID": "45", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "5", "Category": "Assault", "GoldCost": "2606057", "IdolCost": "195", "UpgradeTime": "715200", "Type": "Execution", "Rank": "9"}, {"AbilityName": "ShadowBlade", "AbilityID": "45", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "5", "Category": "Assault", "GoldCost": "3829152", "IdolCost": "220", "UpgradeTime": "1024200", "Type": "Execution", "Rank": "10"}, {"AbilityName": "SeekingBlades", "AbilityID": "46", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "6", "Category": "Assault", "GoldCost": "21301", "IdolCost": "11", "UpgradeTime": "165", "Type": "Execution", "Rank": "1"}, {"AbilityName": "SeekingBlades", "AbilityID": "46", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "6", "Category": "Assault", "GoldCost": "47838", "IdolCost": "24", "UpgradeTime": "75600", "Type": "Execution", "Rank": "2"}, {"AbilityName": "SeekingBlades", "AbilityID": "46", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "6", "Category": "Assault", "GoldCost": "97507", "IdolCost": "40", "UpgradeTime": "108000", "Type": "Execution", "Rank": "3"}, {"AbilityName": "SeekingBlades", "AbilityID": "46", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "6", "Category": "Assault", "GoldCost": "186863", "IdolCost": "52", "UpgradeTime": "172800", "Type": "Execution", "Rank": "4"}, {"AbilityName": "SeekingBlades", "AbilityID": "46", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "6", "Category": "Assault", "GoldCost": "342213", "IdolCost": "73", "UpgradeTime": "259200", "Type": "Execution", "Rank": "5"}, {"AbilityName": "SeekingBlades", "AbilityID": "46", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "6", "Category": "Assault", "GoldCost": "607888", "IdolCost": "108", "UpgradeTime": "343800", "Type": "Execution", "Rank": "6"}, {"AbilityName": "SeekingBlades", "AbilityID": "46", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "6", "Category": "Assault", "GoldCost": "1054470", "IdolCost": "134", "UpgradeTime": "439200", "Type": "Execution", "Rank": "7"}, {"AbilityName": "SeekingBlades", "AbilityID": "46", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "6", "Category": "Assault", "GoldCost": "1792381", "IdolCost": "171", "UpgradeTime": "634800", "Type": "Execution", "Rank": "8"}, {"AbilityName": "SeekingBlades", "AbilityID": "46", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "6", "Category": "Assault", "GoldCost": "2706290", "IdolCost": "197", "UpgradeTime": "715200", "Type": "Execution", "Rank": "9"}, {"AbilityName": "SeekingBlades", "AbilityID": "46", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "6", "Category": "Assault", "GoldCost": "3976427", "IdolCost": "223", "UpgradeTime": "1024200", "Type": "Execution", "Rank": "10"}, {"AbilityName": "PoisonDagger", "AbilityID": "47", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "0", "Category": "Assault", "GoldCost": "15229", "IdolCost": "8", "UpgradeTime": "180", "Type": "Execution", "Rank": "1"}, {"AbilityName": "PoisonDagger", "AbilityID": "47", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "0", "Category": "Assault", "GoldCost": "35854", "IdolCost": "18", "UpgradeTime": "72000", "Type": "Execution", "Rank": "2"}, {"AbilityName": "PoisonDagger", "AbilityID": "47", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "0", "Category": "Assault", "GoldCost": "75112", "IdolCost": "37", "UpgradeTime": "108000", "Type": "Execution", "Rank": "3"}, {"AbilityName": "PoisonDagger", "AbilityID": "47", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "0", "Category": "Assault", "GoldCost": "146739", "IdolCost": "46", "UpgradeTime": "172800", "Type": "Execution", "Rank": "4"}, {"AbilityName": "PoisonDagger", "AbilityID": "47", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "0", "Category": "Assault", "GoldCost": "272918", "IdolCost": "63", "UpgradeTime": "259200", "Type": "Execution", "Rank": "5"}, {"AbilityName": "PoisonDagger", "AbilityID": "47", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "0", "Category": "Assault", "GoldCost": "489969", "IdolCost": "92", "UpgradeTime": "343800", "Type": "Execution", "Rank": "6"}, {"AbilityName": "PoisonDagger", "AbilityID": "47", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "0", "Category": "Assault", "GoldCost": "856599", "IdolCost": "124", "UpgradeTime": "439200", "Type": "Execution", "Rank": "7"}, {"AbilityName": "PoisonDagger", "AbilityID": "47", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "0", "Category": "Assault", "GoldCost": "1466375", "IdolCost": "154", "UpgradeTime": "634800", "Type": "Execution", "Rank": "8"}, {"AbilityName": "PoisonDagger", "AbilityID": "47", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "0", "Category": "Assault", "GoldCost": "2332443", "IdolCost": "190", "UpgradeTime": "715200", "Type": "Execution", "Rank": "9"}, {"AbilityName": "PoisonDagger", "AbilityID": "47", "BaseClass": "Rogue", "Class": "Executioner", "HotbarLocation": "0", "Category": "Assault", "GoldCost": "3427123", "IdolCost": "212", "UpgradeTime": "1024200", "Type": "Execution", "Rank": "10"}, {"AbilityName": "RollingSmash", "AbilityID": "48", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "9990", "IdolCost": "5", "UpgradeTime": "45", "Type": "Attack", "Rank": "1"}, {"AbilityName": "RollingSmash", "AbilityID": "48", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "25525", "IdolCost": "13", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "RollingSmash", "AbilityID": "48", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "55488", "IdolCost": "28", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "RollingSmash", "AbilityID": "48", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "111408", "IdolCost": "42", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "RollingSmash", "AbilityID": "48", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "211572", "IdolCost": "55", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "RollingSmash", "AbilityID": "48", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "384526", "IdolCost": "78", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "RollingSmash", "AbilityID": "48", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "679864", "IdolCost": "115", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "RollingSmash", "AbilityID": "48", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "1173498", "IdolCost": "140", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "RollingSmash", "AbilityID": "48", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "1989204", "IdolCost": "180", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "RollingSmash", "AbilityID": "48", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "2922793", "IdolCost": "202", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "ShieldFlurry", "AbilityID": "49", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Protection", "GoldCost": "11107", "IdolCost": "6", "UpgradeTime": "60", "Type": "Attack", "Rank": "1"}, {"AbilityName": "ShieldFlurry", "AbilityID": "49", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Protection", "GoldCost": "27747", "IdolCost": "14", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "ShieldFlurry", "AbilityID": "49", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Protection", "GoldCost": "59872", "IdolCost": "30", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "ShieldFlurry", "AbilityID": "49", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Protection", "GoldCost": "119210", "IdolCost": "43", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "ShieldFlurry", "AbilityID": "49", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Protection", "GoldCost": "225210", "IdolCost": "57", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "ShieldFlurry", "AbilityID": "49", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Protection", "GoldCost": "408525", "IdolCost": "81", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "ShieldFlurry", "AbilityID": "49", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Protection", "GoldCost": "719836", "IdolCost": "117", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "ShieldFlurry", "AbilityID": "49", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Protection", "GoldCost": "1239978", "IdolCost": "143", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "ShieldFlurry", "AbilityID": "49", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Protection", "GoldCost": "2068772", "IdolCost": "184", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "ShieldFlurry", "AbilityID": "49", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "1", "Category": "Protection", "GoldCost": "3039705", "IdolCost": "204", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "Juggernaut", "AbilityID": "50", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "12224", "IdolCost": "7", "UpgradeTime": "75", "Type": "Attack", "Rank": "1"}, {"AbilityName": "Juggernaut", "AbilityID": "50", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "29968", "IdolCost": "15", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "Juggernaut", "AbilityID": "50", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "64256", "IdolCost": "33", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "Juggernaut", "AbilityID": "50", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "127012", "IdolCost": "44", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "Juggernaut", "AbilityID": "50", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "238848", "IdolCost": "59", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "Juggernaut", "AbilityID": "50", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "432524", "IdolCost": "85", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "Juggernaut", "AbilityID": "50", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "759808", "IdolCost": "119", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "Juggernaut", "AbilityID": "50", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "1306458", "IdolCost": "146", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "Juggernaut", "AbilityID": "50", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "2148340", "IdolCost": "186", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "Juggernaut", "AbilityID": "50", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "3156616", "IdolCost": "206", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "SecondWind", "AbilityID": "51", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "13567", "IdolCost": "7", "UpgradeTime": "90", "Type": "Survival", "Rank": "1"}, {"AbilityName": "SecondWind", "AbilityID": "51", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "32563", "IdolCost": "17", "UpgradeTime": "68400", "Type": "Survival", "Rank": "2"}, {"AbilityName": "SecondWind", "AbilityID": "51", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "69044", "IdolCost": "35", "UpgradeTime": "108000", "Type": "Survival", "Rank": "3"}, {"AbilityName": "SecondWind", "AbilityID": "51", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "136037", "IdolCost": "45", "UpgradeTime": "172800", "Type": "Survival", "Rank": "4"}, {"AbilityName": "SecondWind", "AbilityID": "51", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "254251", "IdolCost": "61", "UpgradeTime": "259200", "Type": "Survival", "Rank": "5"}, {"AbilityName": "SecondWind", "AbilityID": "51", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "458607", "IdolCost": "88", "UpgradeTime": "343800", "Type": "Survival", "Rank": "6"}, {"AbilityName": "SecondWind", "AbilityID": "51", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "804044", "IdolCost": "121", "UpgradeTime": "439200", "Type": "Survival", "Rank": "7"}, {"AbilityName": "SecondWind", "AbilityID": "51", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "1379643", "IdolCost": "150", "UpgradeTime": "634800", "Type": "Survival", "Rank": "8"}, {"AbilityName": "SecondWind", "AbilityID": "51", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "2234274", "IdolCost": "188", "UpgradeTime": "715200", "Type": "Survival", "Rank": "9"}, {"AbilityName": "SecondWind", "AbilityID": "51", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "2", "Category": "Protection", "GoldCost": "3282881", "IdolCost": "209", "UpgradeTime": "1024200", "Type": "Survival", "Rank": "10"}, {"AbilityName": "Shockwave", "AbilityID": "52", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "14909", "IdolCost": "8", "UpgradeTime": "105", "Type": "Attack", "Rank": "1"}, {"AbilityName": "Shockwave", "AbilityID": "52", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "35158", "IdolCost": "18", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "Shockwave", "AbilityID": "52", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "73831", "IdolCost": "37", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "Shockwave", "AbilityID": "52", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "145061", "IdolCost": "46", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "Shockwave", "AbilityID": "52", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "269653", "IdolCost": "63", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "Shockwave", "AbilityID": "52", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "484690", "IdolCost": "92", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "Shockwave", "AbilityID": "52", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "848280", "IdolCost": "123", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "Shockwave", "AbilityID": "52", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "1452827", "IdolCost": "154", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "Shockwave", "AbilityID": "52", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "2320207", "IdolCost": "190", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "Shockwave", "AbilityID": "52", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "3409145", "IdolCost": "211", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "Retribution", "AbilityID": "53", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "16419", "IdolCost": "9", "UpgradeTime": "120", "Type": "Attack", "Rank": "1"}, {"AbilityName": "Retribution", "AbilityID": "53", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "38176", "IdolCost": "20", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "Retribution", "AbilityID": "53", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "79501", "IdolCost": "38", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "Retribution", "AbilityID": "53", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "154805", "IdolCost": "48", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "Retribution", "AbilityID": "53", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "286973", "IdolCost": "65", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "Retribution", "AbilityID": "53", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "514014", "IdolCost": "95", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "Retribution", "AbilityID": "53", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "897069", "IdolCost": "126", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "Retribution", "AbilityID": "53", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "1533911", "IdolCost": "158", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "Retribution", "AbilityID": "53", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "2413016", "IdolCost": "191", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "Retribution", "AbilityID": "53", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "3545511", "IdolCost": "214", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "Defiance", "AbilityID": "54", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "4", "Category": "Protection", "GoldCost": "17929", "IdolCost": "9", "UpgradeTime": "135", "Type": "Sentry", "Rank": "1"}, {"AbilityName": "Defiance", "AbilityID": "54", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "4", "Category": "Protection", "GoldCost": "41194", "IdolCost": "21", "UpgradeTime": "75600", "Type": "Sentry", "Rank": "2"}, {"AbilityName": "Defiance", "AbilityID": "54", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "4", "Category": "Protection", "GoldCost": "85170", "IdolCost": "38", "UpgradeTime": "108000", "Type": "Sentry", "Rank": "3"}, {"AbilityName": "Defiance", "AbilityID": "54", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "4", "Category": "Protection", "GoldCost": "164549", "IdolCost": "49", "UpgradeTime": "172800", "Type": "Sentry", "Rank": "4"}, {"AbilityName": "Defiance", "AbilityID": "54", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "4", "Category": "Protection", "GoldCost": "304292", "IdolCost": "67", "UpgradeTime": "259200", "Type": "Sentry", "Rank": "5"}, {"AbilityName": "Defiance", "AbilityID": "54", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "4", "Category": "Protection", "GoldCost": "543338", "IdolCost": "99", "UpgradeTime": "343800", "Type": "Sentry", "Rank": "6"}, {"AbilityName": "Defiance", "AbilityID": "54", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "4", "Category": "Protection", "GoldCost": "945857", "IdolCost": "128", "UpgradeTime": "439200", "Type": "Sentry", "Rank": "7"}, {"AbilityName": "Defiance", "AbilityID": "54", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "4", "Category": "Protection", "GoldCost": "1614995", "IdolCost": "162", "UpgradeTime": "634800", "Type": "Sentry", "Rank": "8"}, {"AbilityName": "Defiance", "AbilityID": "54", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "4", "Category": "Protection", "GoldCost": "2505824", "IdolCost": "193", "UpgradeTime": "715200", "Type": "Sentry", "Rank": "9"}, {"AbilityName": "Defiance", "AbilityID": "54", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "4", "Category": "Protection", "GoldCost": "3681877", "IdolCost": "217", "UpgradeTime": "1024200", "Type": "Sentry", "Rank": "10"}, {"AbilityName": "Barrier", "AbilityID": "55", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "5", "Category": "Protection", "GoldCost": "19615", "IdolCost": "10", "UpgradeTime": "150", "Type": "Sentry", "Rank": "1"}, {"AbilityName": "Barrier", "AbilityID": "55", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "5", "Category": "Protection", "GoldCost": "44516", "IdolCost": "23", "UpgradeTime": "75600", "Type": "Sentry", "Rank": "2"}, {"AbilityName": "Barrier", "AbilityID": "55", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "5", "Category": "Protection", "GoldCost": "91339", "IdolCost": "39", "UpgradeTime": "108000", "Type": "Sentry", "Rank": "3"}, {"AbilityName": "Barrier", "AbilityID": "55", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "5", "Category": "Protection", "GoldCost": "175706", "IdolCost": "50", "UpgradeTime": "172800", "Type": "Sentry", "Rank": "4"}, {"AbilityName": "Barrier", "AbilityID": "55", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "5", "Category": "Protection", "GoldCost": "323253", "IdolCost": "70", "UpgradeTime": "259200", "Type": "Sentry", "Rank": "5"}, {"AbilityName": "Barrier", "AbilityID": "55", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "5", "Category": "Protection", "GoldCost": "575613", "IdolCost": "104", "UpgradeTime": "343800", "Type": "Sentry", "Rank": "6"}, {"AbilityName": "Barrier", "AbilityID": "55", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "5", "Category": "Protection", "GoldCost": "1000164", "IdolCost": "131", "UpgradeTime": "439200", "Type": "Sentry", "Rank": "7"}, {"AbilityName": "Barrier", "AbilityID": "55", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "5", "Category": "Protection", "GoldCost": "1703688", "IdolCost": "166", "UpgradeTime": "634800", "Type": "Sentry", "Rank": "8"}, {"AbilityName": "Barrier", "AbilityID": "55", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "5", "Category": "Protection", "GoldCost": "2606057", "IdolCost": "195", "UpgradeTime": "715200", "Type": "Sentry", "Rank": "9"}, {"AbilityName": "Barrier", "AbilityID": "55", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "5", "Category": "Protection", "GoldCost": "3829152", "IdolCost": "220", "UpgradeTime": "1024200", "Type": "Sentry", "Rank": "10"}, {"AbilityName": "SentinelForm", "AbilityID": "56", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "6", "Category": "Protection", "GoldCost": "21301", "IdolCost": "11", "UpgradeTime": "165", "Type": "Sentry", "Rank": "1"}, {"AbilityName": "SentinelForm", "AbilityID": "56", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "6", "Category": "Protection", "GoldCost": "47838", "IdolCost": "24", "UpgradeTime": "75600", "Type": "Sentry", "Rank": "2"}, {"AbilityName": "SentinelForm", "AbilityID": "56", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "6", "Category": "Protection", "GoldCost": "97507", "IdolCost": "40", "UpgradeTime": "108000", "Type": "Sentry", "Rank": "3"}, {"AbilityName": "SentinelForm", "AbilityID": "56", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "6", "Category": "Protection", "GoldCost": "186863", "IdolCost": "52", "UpgradeTime": "172800", "Type": "Sentry", "Rank": "4"}, {"AbilityName": "SentinelForm", "AbilityID": "56", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "6", "Category": "Protection", "GoldCost": "342213", "IdolCost": "73", "UpgradeTime": "259200", "Type": "Sentry", "Rank": "5"}, {"AbilityName": "SentinelForm", "AbilityID": "56", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "6", "Category": "Protection", "GoldCost": "607888", "IdolCost": "108", "UpgradeTime": "343800", "Type": "Sentry", "Rank": "6"}, {"AbilityName": "SentinelForm", "AbilityID": "56", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "6", "Category": "Protection", "GoldCost": "1054470", "IdolCost": "134", "UpgradeTime": "439200", "Type": "Sentry", "Rank": "7"}, {"AbilityName": "SentinelForm", "AbilityID": "56", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "6", "Category": "Protection", "GoldCost": "1792381", "IdolCost": "171", "UpgradeTime": "634800", "Type": "Sentry", "Rank": "8"}, {"AbilityName": "SentinelForm", "AbilityID": "56", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "6", "Category": "Protection", "GoldCost": "2706290", "IdolCost": "197", "UpgradeTime": "715200", "Type": "Sentry", "Rank": "9"}, {"AbilityName": "SentinelForm", "AbilityID": "56", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "6", "Category": "Protection", "GoldCost": "3976427", "IdolCost": "223", "UpgradeTime": "1024200", "Type": "Sentry", "Rank": "10"}, {"AbilityName": "ConcussionBolt", "AbilityID": "57", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "0", "Category": "Protection", "GoldCost": "15229", "IdolCost": "8", "UpgradeTime": "180", "Type": "Sentry", "Rank": "1"}, {"AbilityName": "ConcussionBolt", "AbilityID": "57", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "0", "Category": "Protection", "GoldCost": "35854", "IdolCost": "18", "UpgradeTime": "72000", "Type": "Sentry", "Rank": "2"}, {"AbilityName": "ConcussionBolt", "AbilityID": "57", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "0", "Category": "Protection", "GoldCost": "75112", "IdolCost": "37", "UpgradeTime": "108000", "Type": "Sentry", "Rank": "3"}, {"AbilityName": "ConcussionBolt", "AbilityID": "57", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "0", "Category": "Protection", "GoldCost": "146739", "IdolCost": "46", "UpgradeTime": "172800", "Type": "Sentry", "Rank": "4"}, {"AbilityName": "ConcussionBolt", "AbilityID": "57", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "0", "Category": "Protection", "GoldCost": "272918", "IdolCost": "63", "UpgradeTime": "259200", "Type": "Sentry", "Rank": "5"}, {"AbilityName": "ConcussionBolt", "AbilityID": "57", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "0", "Category": "Protection", "GoldCost": "489969", "IdolCost": "92", "UpgradeTime": "343800", "Type": "Sentry", "Rank": "6"}, {"AbilityName": "ConcussionBolt", "AbilityID": "57", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "0", "Category": "Protection", "GoldCost": "856599", "IdolCost": "124", "UpgradeTime": "439200", "Type": "Sentry", "Rank": "7"}, {"AbilityName": "ConcussionBolt", "AbilityID": "57", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "0", "Category": "Protection", "GoldCost": "1466375", "IdolCost": "154", "UpgradeTime": "634800", "Type": "Sentry", "Rank": "8"}, {"AbilityName": "ConcussionBolt", "AbilityID": "57", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "0", "Category": "Protection", "GoldCost": "2332443", "IdolCost": "190", "UpgradeTime": "715200", "Type": "Sentry", "Rank": "9"}, {"AbilityName": "ConcussionBolt", "AbilityID": "57", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Sentinel", "HotbarLocation": "0", "Category": "Protection", "GoldCost": "3427123", "IdolCost": "212", "UpgradeTime": "1024200", "Type": "Sentry", "Rank": "10"}, {"AbilityName": "FlameSpout", "AbilityID": "58", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "9990", "IdolCost": "5", "UpgradeTime": "45", "Type": "Attack", "Rank": "1"}, {"AbilityName": "FlameSpout", "AbilityID": "58", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "25525", "IdolCost": "13", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "FlameSpout", "AbilityID": "58", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "55488", "IdolCost": "28", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "FlameSpout", "AbilityID": "58", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "111408", "IdolCost": "42", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "FlameSpout", "AbilityID": "58", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "211572", "IdolCost": "55", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "FlameSpout", "AbilityID": "58", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "384526", "IdolCost": "78", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "FlameSpout", "AbilityID": "58", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "679864", "IdolCost": "115", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "FlameSpout", "AbilityID": "58", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "1173498", "IdolCost": "140", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "FlameSpout", "AbilityID": "58", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "1989204", "IdolCost": "180", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "FlameSpout", "AbilityID": "58", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "2922793", "IdolCost": "202", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "IridescentBurst", "AbilityID": "59", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "11107", "IdolCost": "6", "UpgradeTime": "60", "Type": "Attack", "Rank": "1"}, {"AbilityName": "IridescentBurst", "AbilityID": "59", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "27747", "IdolCost": "14", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "IridescentBurst", "AbilityID": "59", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "59872", "IdolCost": "30", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "IridescentBurst", "AbilityID": "59", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "119210", "IdolCost": "43", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "IridescentBurst", "AbilityID": "59", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "225210", "IdolCost": "57", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "IridescentBurst", "AbilityID": "59", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "408525", "IdolCost": "81", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "IridescentBurst", "AbilityID": "59", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "719836", "IdolCost": "117", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "IridescentBurst", "AbilityID": "59", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "1239978", "IdolCost": "143", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "IridescentBurst", "AbilityID": "59", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "2068772", "IdolCost": "184", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "IridescentBurst", "AbilityID": "59", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "1", "Category": "Fire", "GoldCost": "3039705", "IdolCost": "204", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "FireStorm", "AbilityID": "60", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "12224", "IdolCost": "7", "UpgradeTime": "75", "Type": "Attack", "Rank": "1"}, {"AbilityName": "FireStorm", "AbilityID": "60", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "29968", "IdolCost": "15", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "FireStorm", "AbilityID": "60", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "64256", "IdolCost": "33", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "FireStorm", "AbilityID": "60", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "127012", "IdolCost": "44", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "FireStorm", "AbilityID": "60", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "238848", "IdolCost": "59", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "FireStorm", "AbilityID": "60", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "432524", "IdolCost": "85", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "FireStorm", "AbilityID": "60", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "759808", "IdolCost": "119", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "FireStorm", "AbilityID": "60", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "1306458", "IdolCost": "146", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "FireStorm", "AbilityID": "60", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "2148340", "IdolCost": "186", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "FireStorm", "AbilityID": "60", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "3156616", "IdolCost": "206", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "FlameStrike", "AbilityID": "61", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "13567", "IdolCost": "7", "UpgradeTime": "90", "Type": "Attack", "Rank": "1"}, {"AbilityName": "FlameStrike", "AbilityID": "61", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "32563", "IdolCost": "17", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "FlameStrike", "AbilityID": "61", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "69044", "IdolCost": "35", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "FlameStrike", "AbilityID": "61", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "136037", "IdolCost": "45", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "FlameStrike", "AbilityID": "61", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "254251", "IdolCost": "61", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "FlameStrike", "AbilityID": "61", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "458607", "IdolCost": "88", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "FlameStrike", "AbilityID": "61", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "804044", "IdolCost": "121", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "FlameStrike", "AbilityID": "61", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "1379643", "IdolCost": "150", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "FlameStrike", "AbilityID": "61", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "2234274", "IdolCost": "188", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "FlameStrike", "AbilityID": "61", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "2", "Category": "Fire", "GoldCost": "3282881", "IdolCost": "209", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "MoltenFist", "AbilityID": "62", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "14909", "IdolCost": "8", "UpgradeTime": "105", "Type": "Attack", "Rank": "1"}, {"AbilityName": "MoltenFist", "AbilityID": "62", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "35158", "IdolCost": "18", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "MoltenFist", "AbilityID": "62", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "73831", "IdolCost": "37", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "MoltenFist", "AbilityID": "62", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "145061", "IdolCost": "46", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "MoltenFist", "AbilityID": "62", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "269653", "IdolCost": "63", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "MoltenFist", "AbilityID": "62", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "484690", "IdolCost": "92", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "MoltenFist", "AbilityID": "62", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "848280", "IdolCost": "123", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "MoltenFist", "AbilityID": "62", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "1452827", "IdolCost": "154", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "MoltenFist", "AbilityID": "62", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "2320207", "IdolCost": "190", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "MoltenFist", "AbilityID": "62", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "3409145", "IdolCost": "211", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "FireBrand", "AbilityID": "63", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "16419", "IdolCost": "9", "UpgradeTime": "120", "Type": "Offensive", "Rank": "1"}, {"AbilityName": "FireBrand", "AbilityID": "63", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "38176", "IdolCost": "20", "UpgradeTime": "68400", "Type": "Offensive", "Rank": "2"}, {"AbilityName": "FireBrand", "AbilityID": "63", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "79501", "IdolCost": "38", "UpgradeTime": "108000", "Type": "Offensive", "Rank": "3"}, {"AbilityName": "FireBrand", "AbilityID": "63", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "154805", "IdolCost": "48", "UpgradeTime": "172800", "Type": "Offensive", "Rank": "4"}, {"AbilityName": "FireBrand", "AbilityID": "63", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "286973", "IdolCost": "65", "UpgradeTime": "259200", "Type": "Offensive", "Rank": "5"}, {"AbilityName": "FireBrand", "AbilityID": "63", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "514014", "IdolCost": "95", "UpgradeTime": "343800", "Type": "Offensive", "Rank": "6"}, {"AbilityName": "FireBrand", "AbilityID": "63", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "897069", "IdolCost": "126", "UpgradeTime": "439200", "Type": "Offensive", "Rank": "7"}, {"AbilityName": "FireBrand", "AbilityID": "63", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "1533911", "IdolCost": "158", "UpgradeTime": "634800", "Type": "Offensive", "Rank": "8"}, {"AbilityName": "FireBrand", "AbilityID": "63", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "2413016", "IdolCost": "191", "UpgradeTime": "715200", "Type": "Offensive", "Rank": "9"}, {"AbilityName": "FireBrand", "AbilityID": "63", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "3", "Category": "Fire", "GoldCost": "3545511", "IdolCost": "214", "UpgradeTime": "1024200", "Type": "Offensive", "Rank": "10"}, {"AbilityName": "WildFire", "AbilityID": "64", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "4", "Category": "Fire", "GoldCost": "17929", "IdolCost": "9", "UpgradeTime": "135", "Type": "Flame", "Rank": "1"}, {"AbilityName": "WildFire", "AbilityID": "64", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "4", "Category": "Fire", "GoldCost": "41194", "IdolCost": "21", "UpgradeTime": "75600", "Type": "Flame", "Rank": "2"}, {"AbilityName": "WildFire", "AbilityID": "64", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "4", "Category": "Fire", "GoldCost": "85170", "IdolCost": "38", "UpgradeTime": "108000", "Type": "Flame", "Rank": "3"}, {"AbilityName": "WildFire", "AbilityID": "64", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "4", "Category": "Fire", "GoldCost": "164549", "IdolCost": "49", "UpgradeTime": "172800", "Type": "Flame", "Rank": "4"}, {"AbilityName": "WildFire", "AbilityID": "64", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "4", "Category": "Fire", "GoldCost": "304292", "IdolCost": "67", "UpgradeTime": "259200", "Type": "Flame", "Rank": "5"}, {"AbilityName": "WildFire", "AbilityID": "64", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "4", "Category": "Fire", "GoldCost": "543338", "IdolCost": "99", "UpgradeTime": "343800", "Type": "Flame", "Rank": "6"}, {"AbilityName": "WildFire", "AbilityID": "64", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "4", "Category": "Fire", "GoldCost": "945857", "IdolCost": "128", "UpgradeTime": "439200", "Type": "Flame", "Rank": "7"}, {"AbilityName": "WildFire", "AbilityID": "64", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "4", "Category": "Fire", "GoldCost": "1614995", "IdolCost": "162", "UpgradeTime": "634800", "Type": "Flame", "Rank": "8"}, {"AbilityName": "WildFire", "AbilityID": "64", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "4", "Category": "Fire", "GoldCost": "2505824", "IdolCost": "193", "UpgradeTime": "715200", "Type": "Flame", "Rank": "9"}, {"AbilityName": "WildFire", "AbilityID": "64", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "4", "Category": "Fire", "GoldCost": "3681877", "IdolCost": "217", "UpgradeTime": "1024200", "Type": "Flame", "Rank": "10"}, {"AbilityName": "Pyromania", "AbilityID": "65", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "5", "Category": "Fire", "GoldCost": "19615", "IdolCost": "10", "UpgradeTime": "150", "Type": "Flame", "Rank": "1"}, {"AbilityName": "Pyromania", "AbilityID": "65", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "5", "Category": "Fire", "GoldCost": "44516", "IdolCost": "23", "UpgradeTime": "75600", "Type": "Flame", "Rank": "2"}, {"AbilityName": "Pyromania", "AbilityID": "65", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "5", "Category": "Fire", "GoldCost": "91339", "IdolCost": "39", "UpgradeTime": "108000", "Type": "Flame", "Rank": "3"}, {"AbilityName": "Pyromania", "AbilityID": "65", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "5", "Category": "Fire", "GoldCost": "175706", "IdolCost": "50", "UpgradeTime": "172800", "Type": "Flame", "Rank": "4"}, {"AbilityName": "Pyromania", "AbilityID": "65", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "5", "Category": "Fire", "GoldCost": "323253", "IdolCost": "70", "UpgradeTime": "259200", "Type": "Flame", "Rank": "5"}, {"AbilityName": "Pyromania", "AbilityID": "65", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "5", "Category": "Fire", "GoldCost": "575613", "IdolCost": "104", "UpgradeTime": "343800", "Type": "Flame", "Rank": "6"}, {"AbilityName": "Pyromania", "AbilityID": "65", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "5", "Category": "Fire", "GoldCost": "1000164", "IdolCost": "131", "UpgradeTime": "439200", "Type": "Flame", "Rank": "7"}, {"AbilityName": "Pyromania", "AbilityID": "65", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "5", "Category": "Fire", "GoldCost": "1703688", "IdolCost": "166", "UpgradeTime": "634800", "Type": "Flame", "Rank": "8"}, {"AbilityName": "Pyromania", "AbilityID": "65", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "5", "Category": "Fire", "GoldCost": "2606057", "IdolCost": "195", "UpgradeTime": "715200", "Type": "Flame", "Rank": "9"}, {"AbilityName": "Pyromania", "AbilityID": "65", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "5", "Category": "Fire", "GoldCost": "3829152", "IdolCost": "220", "UpgradeTime": "1024200", "Type": "Flame", "Rank": "10"}, {"AbilityName": "SummonDragonSoul", "AbilityID": "66", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "6", "Category": "Fire", "GoldCost": "21301", "IdolCost": "11", "UpgradeTime": "165", "Type": "Flame", "Rank": "1"}, {"AbilityName": "SummonDragonSoul", "AbilityID": "66", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "6", "Category": "Fire", "GoldCost": "47838", "IdolCost": "24", "UpgradeTime": "75600", "Type": "Flame", "Rank": "2"}, {"AbilityName": "SummonDragonSoul", "AbilityID": "66", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "6", "Category": "Fire", "GoldCost": "97507", "IdolCost": "40", "UpgradeTime": "108000", "Type": "Flame", "Rank": "3"}, {"AbilityName": "SummonDragonSoul", "AbilityID": "66", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "6", "Category": "Fire", "GoldCost": "186863", "IdolCost": "52", "UpgradeTime": "172800", "Type": "Flame", "Rank": "4"}, {"AbilityName": "SummonDragonSoul", "AbilityID": "66", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "6", "Category": "Fire", "GoldCost": "342213", "IdolCost": "73", "UpgradeTime": "259200", "Type": "Flame", "Rank": "5"}, {"AbilityName": "SummonDragonSoul", "AbilityID": "66", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "6", "Category": "Fire", "GoldCost": "607888", "IdolCost": "108", "UpgradeTime": "343800", "Type": "Flame", "Rank": "6"}, {"AbilityName": "SummonDragonSoul", "AbilityID": "66", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "6", "Category": "Fire", "GoldCost": "1054470", "IdolCost": "134", "UpgradeTime": "439200", "Type": "Flame", "Rank": "7"}, {"AbilityName": "SummonDragonSoul", "AbilityID": "66", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "6", "Category": "Fire", "GoldCost": "1792381", "IdolCost": "171", "UpgradeTime": "634800", "Type": "Flame", "Rank": "8"}, {"AbilityName": "SummonDragonSoul", "AbilityID": "66", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "6", "Category": "Fire", "GoldCost": "2706290", "IdolCost": "197", "UpgradeTime": "715200", "Type": "Flame", "Rank": "9"}, {"AbilityName": "SummonDragonSoul", "AbilityID": "66", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "6", "Category": "Fire", "GoldCost": "3976427", "IdolCost": "223", "UpgradeTime": "1024200", "Type": "Flame", "Rank": "10"}, {"AbilityName": "CrimsonShot", "AbilityID": "67", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "0", "Category": "Fire", "GoldCost": "15229", "IdolCost": "8", "UpgradeTime": "180", "Type": "Flame", "Rank": "1"}, {"AbilityName": "CrimsonShot", "AbilityID": "67", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "0", "Category": "Fire", "GoldCost": "35854", "IdolCost": "18", "UpgradeTime": "72000", "Type": "Flame", "Rank": "2"}, {"AbilityName": "CrimsonShot", "AbilityID": "67", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "0", "Category": "Fire", "GoldCost": "75112", "IdolCost": "37", "UpgradeTime": "108000", "Type": "Flame", "Rank": "3"}, {"AbilityName": "CrimsonShot", "AbilityID": "67", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "0", "Category": "Fire", "GoldCost": "146739", "IdolCost": "46", "UpgradeTime": "172800", "Type": "Flame", "Rank": "4"}, {"AbilityName": "CrimsonShot", "AbilityID": "67", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "0", "Category": "Fire", "GoldCost": "272918", "IdolCost": "63", "UpgradeTime": "259200", "Type": "Flame", "Rank": "5"}, {"AbilityName": "CrimsonShot", "AbilityID": "67", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "0", "Category": "Fire", "GoldCost": "489969", "IdolCost": "92", "UpgradeTime": "343800", "Type": "Flame", "Rank": "6"}, {"AbilityName": "CrimsonShot", "AbilityID": "67", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "0", "Category": "Fire", "GoldCost": "856599", "IdolCost": "124", "UpgradeTime": "439200", "Type": "Flame", "Rank": "7"}, {"AbilityName": "CrimsonShot", "AbilityID": "67", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "0", "Category": "Fire", "GoldCost": "1466375", "IdolCost": "154", "UpgradeTime": "634800", "Type": "Flame", "Rank": "8"}, {"AbilityName": "CrimsonShot", "AbilityID": "67", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "0", "Category": "Fire", "GoldCost": "2332443", "IdolCost": "190", "UpgradeTime": "715200", "Type": "Flame", "Rank": "9"}, {"AbilityName": "CrimsonShot", "AbilityID": "67", "BaseClass": "Mage", "Class": "Flameseer", "HotbarLocation": "0", "Category": "Fire", "GoldCost": "3427123", "IdolCost": "212", "UpgradeTime": "1024200", "Type": "Flame", "Rank": "10"}, {"AbilityName": "CrippleStrike", "AbilityID": "68", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "9990", "IdolCost": "5", "UpgradeTime": "45", "Type": "Crowd Control", "Rank": "1"}, {"AbilityName": "CrippleStrike", "AbilityID": "68", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "25525", "IdolCost": "13", "UpgradeTime": "68400", "Type": "Crowd Control", "Rank": "2"}, {"AbilityName": "CrippleStrike", "AbilityID": "68", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "55488", "IdolCost": "28", "UpgradeTime": "108000", "Type": "Crowd Control", "Rank": "3"}, {"AbilityName": "CrippleStrike", "AbilityID": "68", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "111408", "IdolCost": "42", "UpgradeTime": "172800", "Type": "Crowd Control", "Rank": "4"}, {"AbilityName": "CrippleStrike", "AbilityID": "68", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "211572", "IdolCost": "55", "UpgradeTime": "259200", "Type": "Crowd Control", "Rank": "5"}, {"AbilityName": "CrippleStrike", "AbilityID": "68", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "384526", "IdolCost": "78", "UpgradeTime": "343800", "Type": "Crowd Control", "Rank": "6"}, {"AbilityName": "CrippleStrike", "AbilityID": "68", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "679864", "IdolCost": "115", "UpgradeTime": "439200", "Type": "Crowd Control", "Rank": "7"}, {"AbilityName": "CrippleStrike", "AbilityID": "68", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "1173498", "IdolCost": "140", "UpgradeTime": "634800", "Type": "Crowd Control", "Rank": "8"}, {"AbilityName": "CrippleStrike", "AbilityID": "68", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "1989204", "IdolCost": "180", "UpgradeTime": "715200", "Type": "Crowd Control", "Rank": "9"}, {"AbilityName": "CrippleStrike", "AbilityID": "68", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "2922793", "IdolCost": "202", "UpgradeTime": "1024200", "Type": "Crowd Control", "Rank": "10"}, {"AbilityName": "HeartSeeker", "AbilityID": "69", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "11107", "IdolCost": "6", "UpgradeTime": "60", "Type": "Attack", "Rank": "1"}, {"AbilityName": "HeartSeeker", "AbilityID": "69", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "27747", "IdolCost": "14", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "HeartSeeker", "AbilityID": "69", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "59872", "IdolCost": "30", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "HeartSeeker", "AbilityID": "69", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "119210", "IdolCost": "43", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "HeartSeeker", "AbilityID": "69", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "225210", "IdolCost": "57", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "HeartSeeker", "AbilityID": "69", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "408525", "IdolCost": "81", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "HeartSeeker", "AbilityID": "69", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "719836", "IdolCost": "117", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "HeartSeeker", "AbilityID": "69", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "1239978", "IdolCost": "143", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "HeartSeeker", "AbilityID": "69", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "2068772", "IdolCost": "184", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "HeartSeeker", "AbilityID": "69", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "1", "Category": "Shadow", "GoldCost": "3039705", "IdolCost": "204", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "ShadowTendrilDash", "AbilityID": "70", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "12224", "IdolCost": "7", "UpgradeTime": "75", "Type": "Offensive", "Rank": "1"}, {"AbilityName": "ShadowTendrilDash", "AbilityID": "70", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "29968", "IdolCost": "15", "UpgradeTime": "68400", "Type": "Offensive", "Rank": "2"}, {"AbilityName": "ShadowTendrilDash", "AbilityID": "70", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "64256", "IdolCost": "33", "UpgradeTime": "108000", "Type": "Offensive", "Rank": "3"}, {"AbilityName": "ShadowTendrilDash", "AbilityID": "70", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "127012", "IdolCost": "44", "UpgradeTime": "172800", "Type": "Offensive", "Rank": "4"}, {"AbilityName": "ShadowTendrilDash", "AbilityID": "70", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "238848", "IdolCost": "59", "UpgradeTime": "259200", "Type": "Offensive", "Rank": "5"}, {"AbilityName": "ShadowTendrilDash", "AbilityID": "70", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "432524", "IdolCost": "85", "UpgradeTime": "343800", "Type": "Offensive", "Rank": "6"}, {"AbilityName": "ShadowTendrilDash", "AbilityID": "70", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "759808", "IdolCost": "119", "UpgradeTime": "439200", "Type": "Offensive", "Rank": "7"}, {"AbilityName": "ShadowTendrilDash", "AbilityID": "70", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "1306458", "IdolCost": "146", "UpgradeTime": "634800", "Type": "Offensive", "Rank": "8"}, {"AbilityName": "ShadowTendrilDash", "AbilityID": "70", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "2148340", "IdolCost": "186", "UpgradeTime": "715200", "Type": "Offensive", "Rank": "9"}, {"AbilityName": "ShadowTendrilDash", "AbilityID": "70", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "3156616", "IdolCost": "206", "UpgradeTime": "1024200", "Type": "Offensive", "Rank": "10"}, {"AbilityName": "WhitheringMist", "AbilityID": "71", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "13567", "IdolCost": "7", "UpgradeTime": "90", "Type": "Crowd Control", "Rank": "1"}, {"AbilityName": "WhitheringMist", "AbilityID": "71", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "32563", "IdolCost": "17", "UpgradeTime": "68400", "Type": "Crowd Control", "Rank": "2"}, {"AbilityName": "WhitheringMist", "AbilityID": "71", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "69044", "IdolCost": "35", "UpgradeTime": "108000", "Type": "Crowd Control", "Rank": "3"}, {"AbilityName": "WhitheringMist", "AbilityID": "71", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "136037", "IdolCost": "45", "UpgradeTime": "172800", "Type": "Crowd Control", "Rank": "4"}, {"AbilityName": "WhitheringMist", "AbilityID": "71", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "254251", "IdolCost": "61", "UpgradeTime": "259200", "Type": "Crowd Control", "Rank": "5"}, {"AbilityName": "WhitheringMist", "AbilityID": "71", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "458607", "IdolCost": "88", "UpgradeTime": "343800", "Type": "Crowd Control", "Rank": "6"}, {"AbilityName": "WhitheringMist", "AbilityID": "71", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "804044", "IdolCost": "121", "UpgradeTime": "439200", "Type": "Crowd Control", "Rank": "7"}, {"AbilityName": "WhitheringMist", "AbilityID": "71", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "1379643", "IdolCost": "150", "UpgradeTime": "634800", "Type": "Crowd Control", "Rank": "8"}, {"AbilityName": "WhitheringMist", "AbilityID": "71", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "2234274", "IdolCost": "188", "UpgradeTime": "715200", "Type": "Crowd Control", "Rank": "9"}, {"AbilityName": "WhitheringMist", "AbilityID": "71", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "3282881", "IdolCost": "209", "UpgradeTime": "1024200", "Type": "Crowd Control", "Rank": "10"}, {"AbilityName": "BlackStorm", "AbilityID": "72", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "14909", "IdolCost": "8", "UpgradeTime": "105", "Type": "Attack", "Rank": "1"}, {"AbilityName": "BlackStorm", "AbilityID": "72", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "35158", "IdolCost": "18", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "BlackStorm", "AbilityID": "72", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "73831", "IdolCost": "37", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "BlackStorm", "AbilityID": "72", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "145061", "IdolCost": "46", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "BlackStorm", "AbilityID": "72", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "269653", "IdolCost": "63", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "BlackStorm", "AbilityID": "72", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "484690", "IdolCost": "92", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "BlackStorm", "AbilityID": "72", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "848280", "IdolCost": "123", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "BlackStorm", "AbilityID": "72", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "1452827", "IdolCost": "154", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "BlackStorm", "AbilityID": "72", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "2320207", "IdolCost": "190", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "BlackStorm", "AbilityID": "72", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "3409145", "IdolCost": "211", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "DarkChi", "AbilityID": "73", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "16419", "IdolCost": "9", "UpgradeTime": "120", "Type": "Attack", "Rank": "1"}, {"AbilityName": "DarkChi", "AbilityID": "73", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "38176", "IdolCost": "20", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "DarkChi", "AbilityID": "73", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "79501", "IdolCost": "38", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "DarkChi", "AbilityID": "73", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "154805", "IdolCost": "48", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "DarkChi", "AbilityID": "73", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "286973", "IdolCost": "65", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "DarkChi", "AbilityID": "73", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "514014", "IdolCost": "95", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "DarkChi", "AbilityID": "73", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "897069", "IdolCost": "126", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "DarkChi", "AbilityID": "73", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "1533911", "IdolCost": "158", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "DarkChi", "AbilityID": "73", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "2413016", "IdolCost": "191", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "DarkChi", "AbilityID": "73", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "3545511", "IdolCost": "214", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "ShadowArmor", "AbilityID": "74", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "4", "Category": "Shadow", "GoldCost": "17929", "IdolCost": "9", "UpgradeTime": "135", "Type": "Shadow", "Rank": "1"}, {"AbilityName": "ShadowArmor", "AbilityID": "74", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "4", "Category": "Shadow", "GoldCost": "41194", "IdolCost": "21", "UpgradeTime": "75600", "Type": "Shadow", "Rank": "2"}, {"AbilityName": "ShadowArmor", "AbilityID": "74", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "4", "Category": "Shadow", "GoldCost": "85170", "IdolCost": "38", "UpgradeTime": "108000", "Type": "Shadow", "Rank": "3"}, {"AbilityName": "ShadowArmor", "AbilityID": "74", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "4", "Category": "Shadow", "GoldCost": "164549", "IdolCost": "49", "UpgradeTime": "172800", "Type": "Shadow", "Rank": "4"}, {"AbilityName": "ShadowArmor", "AbilityID": "74", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "4", "Category": "Shadow", "GoldCost": "304292", "IdolCost": "67", "UpgradeTime": "259200", "Type": "Shadow", "Rank": "5"}, {"AbilityName": "ShadowArmor", "AbilityID": "74", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "4", "Category": "Shadow", "GoldCost": "543338", "IdolCost": "99", "UpgradeTime": "343800", "Type": "Shadow", "Rank": "6"}, {"AbilityName": "ShadowArmor", "AbilityID": "74", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "4", "Category": "Shadow", "GoldCost": "945857", "IdolCost": "128", "UpgradeTime": "439200", "Type": "Shadow", "Rank": "7"}, {"AbilityName": "ShadowArmor", "AbilityID": "74", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "4", "Category": "Shadow", "GoldCost": "1614995", "IdolCost": "162", "UpgradeTime": "634800", "Type": "Shadow", "Rank": "8"}, {"AbilityName": "ShadowArmor", "AbilityID": "74", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "4", "Category": "Shadow", "GoldCost": "2505824", "IdolCost": "193", "UpgradeTime": "715200", "Type": "Shadow", "Rank": "9"}, {"AbilityName": "ShadowArmor", "AbilityID": "74", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "4", "Category": "Shadow", "GoldCost": "3681877", "IdolCost": "217", "UpgradeTime": "1024200", "Type": "Shadow", "Rank": "10"}, {"AbilityName": "ShadowLegion", "AbilityID": "75", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "5", "Category": "Shadow", "GoldCost": "19615", "IdolCost": "10", "UpgradeTime": "150", "Type": "Shadow", "Rank": "1"}, {"AbilityName": "ShadowLegion", "AbilityID": "75", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "5", "Category": "Shadow", "GoldCost": "44516", "IdolCost": "23", "UpgradeTime": "75600", "Type": "Shadow", "Rank": "2"}, {"AbilityName": "ShadowLegion", "AbilityID": "75", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "5", "Category": "Shadow", "GoldCost": "91339", "IdolCost": "39", "UpgradeTime": "108000", "Type": "Shadow", "Rank": "3"}, {"AbilityName": "ShadowLegion", "AbilityID": "75", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "5", "Category": "Shadow", "GoldCost": "175706", "IdolCost": "50", "UpgradeTime": "172800", "Type": "Shadow", "Rank": "4"}, {"AbilityName": "ShadowLegion", "AbilityID": "75", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "5", "Category": "Shadow", "GoldCost": "323253", "IdolCost": "70", "UpgradeTime": "259200", "Type": "Shadow", "Rank": "5"}, {"AbilityName": "ShadowLegion", "AbilityID": "75", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "5", "Category": "Shadow", "GoldCost": "575613", "IdolCost": "104", "UpgradeTime": "343800", "Type": "Shadow", "Rank": "6"}, {"AbilityName": "ShadowLegion", "AbilityID": "75", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "5", "Category": "Shadow", "GoldCost": "1000164", "IdolCost": "131", "UpgradeTime": "439200", "Type": "Shadow", "Rank": "7"}, {"AbilityName": "ShadowLegion", "AbilityID": "75", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "5", "Category": "Shadow", "GoldCost": "1703688", "IdolCost": "166", "UpgradeTime": "634800", "Type": "Shadow", "Rank": "8"}, {"AbilityName": "ShadowLegion", "AbilityID": "75", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "5", "Category": "Shadow", "GoldCost": "2606057", "IdolCost": "195", "UpgradeTime": "715200", "Type": "Shadow", "Rank": "9"}, {"AbilityName": "ShadowLegion", "AbilityID": "75", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "5", "Category": "Shadow", "GoldCost": "3829152", "IdolCost": "220", "UpgradeTime": "1024200", "Type": "Shadow", "Rank": "10"}, {"AbilityName": "ShadowStep", "AbilityID": "76", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "6", "Category": "Shadow", "GoldCost": "21301", "IdolCost": "11", "UpgradeTime": "165", "Type": "Shadow", "Rank": "1"}, {"AbilityName": "ShadowStep", "AbilityID": "76", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "6", "Category": "Shadow", "GoldCost": "47838", "IdolCost": "24", "UpgradeTime": "75600", "Type": "Shadow", "Rank": "2"}, {"AbilityName": "ShadowStep", "AbilityID": "76", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "6", "Category": "Shadow", "GoldCost": "97507", "IdolCost": "40", "UpgradeTime": "108000", "Type": "Shadow", "Rank": "3"}, {"AbilityName": "ShadowStep", "AbilityID": "76", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "6", "Category": "Shadow", "GoldCost": "186863", "IdolCost": "52", "UpgradeTime": "172800", "Type": "Shadow", "Rank": "4"}, {"AbilityName": "ShadowStep", "AbilityID": "76", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "6", "Category": "Shadow", "GoldCost": "342213", "IdolCost": "73", "UpgradeTime": "259200", "Type": "Shadow", "Rank": "5"}, {"AbilityName": "ShadowStep", "AbilityID": "76", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "6", "Category": "Shadow", "GoldCost": "607888", "IdolCost": "108", "UpgradeTime": "343800", "Type": "Shadow", "Rank": "6"}, {"AbilityName": "ShadowStep", "AbilityID": "76", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "6", "Category": "Shadow", "GoldCost": "1054470", "IdolCost": "134", "UpgradeTime": "439200", "Type": "Shadow", "Rank": "7"}, {"AbilityName": "ShadowStep", "AbilityID": "76", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "6", "Category": "Shadow", "GoldCost": "1792381", "IdolCost": "171", "UpgradeTime": "634800", "Type": "Shadow", "Rank": "8"}, {"AbilityName": "ShadowStep", "AbilityID": "76", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "6", "Category": "Shadow", "GoldCost": "2706290", "IdolCost": "197", "UpgradeTime": "715200", "Type": "Shadow", "Rank": "9"}, {"AbilityName": "ShadowStep", "AbilityID": "76", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "6", "Category": "Shadow", "GoldCost": "3976427", "IdolCost": "223", "UpgradeTime": "1024200", "Type": "Shadow", "Rank": "10"}, {"AbilityName": "CorrosiveDagger", "AbilityID": "77", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "0", "Category": "Shadow", "GoldCost": "15229", "IdolCost": "8", "UpgradeTime": "180", "Type": "Shadow", "Rank": "1"}, {"AbilityName": "CorrosiveDagger", "AbilityID": "77", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "0", "Category": "Shadow", "GoldCost": "35854", "IdolCost": "18", "UpgradeTime": "72000", "Type": "Shadow", "Rank": "2"}, {"AbilityName": "CorrosiveDagger", "AbilityID": "77", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "0", "Category": "Shadow", "GoldCost": "75112", "IdolCost": "37", "UpgradeTime": "108000", "Type": "Shadow", "Rank": "3"}, {"AbilityName": "CorrosiveDagger", "AbilityID": "77", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "0", "Category": "Shadow", "GoldCost": "146739", "IdolCost": "46", "UpgradeTime": "172800", "Type": "Shadow", "Rank": "4"}, {"AbilityName": "CorrosiveDagger", "AbilityID": "77", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "0", "Category": "Shadow", "GoldCost": "272918", "IdolCost": "63", "UpgradeTime": "259200", "Type": "Shadow", "Rank": "5"}, {"AbilityName": "CorrosiveDagger", "AbilityID": "77", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "0", "Category": "Shadow", "GoldCost": "489969", "IdolCost": "92", "UpgradeTime": "343800", "Type": "Shadow", "Rank": "6"}, {"AbilityName": "CorrosiveDagger", "AbilityID": "77", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "0", "Category": "Shadow", "GoldCost": "856599", "IdolCost": "124", "UpgradeTime": "439200", "Type": "Shadow", "Rank": "7"}, {"AbilityName": "CorrosiveDagger", "AbilityID": "77", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "0", "Category": "Shadow", "GoldCost": "1466375", "IdolCost": "154", "UpgradeTime": "634800", "Type": "Shadow", "Rank": "8"}, {"AbilityName": "CorrosiveDagger", "AbilityID": "77", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "0", "Category": "Shadow", "GoldCost": "2332443", "IdolCost": "190", "UpgradeTime": "715200", "Type": "Shadow", "Rank": "9"}, {"AbilityName": "CorrosiveDagger", "AbilityID": "77", "BaseClass": "Rogue", "Class": "<PERSON><PERSON><PERSON><PERSON>", "HotbarLocation": "0", "Category": "Shadow", "GoldCost": "3427123", "IdolCost": "212", "UpgradeTime": "1024200", "Type": "Shadow", "Rank": "10"}, {"AbilityName": "FlameAxe", "AbilityID": "78", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "9990", "IdolCost": "5", "UpgradeTime": "45", "Type": "Attack", "Rank": "1"}, {"AbilityName": "FlameAxe", "AbilityID": "78", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "25525", "IdolCost": "13", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "FlameAxe", "AbilityID": "78", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "55488", "IdolCost": "28", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "FlameAxe", "AbilityID": "78", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "111408", "IdolCost": "42", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "FlameAxe", "AbilityID": "78", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "211572", "IdolCost": "55", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "FlameAxe", "AbilityID": "78", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "384526", "IdolCost": "78", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "FlameAxe", "AbilityID": "78", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "679864", "IdolCost": "115", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "FlameAxe", "AbilityID": "78", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "1173498", "IdolCost": "140", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "FlameAxe", "AbilityID": "78", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "1989204", "IdolCost": "180", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "FlameAxe", "AbilityID": "78", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "2922793", "IdolCost": "202", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "79", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "11107", "IdolCost": "6", "UpgradeTime": "60", "Type": "Attack", "Rank": "1"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "79", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "27747", "IdolCost": "14", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "79", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "59872", "IdolCost": "30", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "79", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "119210", "IdolCost": "43", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "79", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "225210", "IdolCost": "57", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "79", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "408525", "IdolCost": "81", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "79", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "719836", "IdolCost": "117", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "79", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "1239978", "IdolCost": "143", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "79", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "2068772", "IdolCost": "184", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "79", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "1", "Category": "Vindication", "GoldCost": "3039705", "IdolCost": "204", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "<PERSON>rm", "AbilityID": "80", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "12224", "IdolCost": "7", "UpgradeTime": "75", "Type": "Attack", "Rank": "1"}, {"AbilityName": "<PERSON>rm", "AbilityID": "80", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "29968", "IdolCost": "15", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "<PERSON>rm", "AbilityID": "80", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "64256", "IdolCost": "33", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "<PERSON>rm", "AbilityID": "80", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "127012", "IdolCost": "44", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "<PERSON>rm", "AbilityID": "80", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "238848", "IdolCost": "59", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "<PERSON>rm", "AbilityID": "80", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "432524", "IdolCost": "85", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "<PERSON>rm", "AbilityID": "80", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "759808", "IdolCost": "119", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "<PERSON>rm", "AbilityID": "80", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "1306458", "IdolCost": "146", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "<PERSON>rm", "AbilityID": "80", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "2148340", "IdolCost": "186", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "<PERSON>rm", "AbilityID": "80", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "3156616", "IdolCost": "206", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "JusticeFist", "AbilityID": "81", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "13567", "IdolCost": "7", "UpgradeTime": "90", "Type": "Attack", "Rank": "1"}, {"AbilityName": "JusticeFist", "AbilityID": "81", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "32563", "IdolCost": "17", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "JusticeFist", "AbilityID": "81", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "69044", "IdolCost": "35", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "JusticeFist", "AbilityID": "81", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "136037", "IdolCost": "45", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "JusticeFist", "AbilityID": "81", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "254251", "IdolCost": "61", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "JusticeFist", "AbilityID": "81", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "458607", "IdolCost": "88", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "JusticeFist", "AbilityID": "81", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "804044", "IdolCost": "121", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "JusticeFist", "AbilityID": "81", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "1379643", "IdolCost": "150", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "JusticeFist", "AbilityID": "81", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "2234274", "IdolCost": "188", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "JusticeFist", "AbilityID": "81", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "2", "Category": "Vindication", "GoldCost": "3282881", "IdolCost": "209", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "LightningStorm", "AbilityID": "82", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "14909", "IdolCost": "8", "UpgradeTime": "105", "Type": "Offensive", "Rank": "1"}, {"AbilityName": "LightningStorm", "AbilityID": "82", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "35158", "IdolCost": "18", "UpgradeTime": "68400", "Type": "Offensive", "Rank": "2"}, {"AbilityName": "LightningStorm", "AbilityID": "82", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "73831", "IdolCost": "37", "UpgradeTime": "108000", "Type": "Offensive", "Rank": "3"}, {"AbilityName": "LightningStorm", "AbilityID": "82", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "145061", "IdolCost": "46", "UpgradeTime": "172800", "Type": "Offensive", "Rank": "4"}, {"AbilityName": "LightningStorm", "AbilityID": "82", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "269653", "IdolCost": "63", "UpgradeTime": "259200", "Type": "Offensive", "Rank": "5"}, {"AbilityName": "LightningStorm", "AbilityID": "82", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "484690", "IdolCost": "92", "UpgradeTime": "343800", "Type": "Offensive", "Rank": "6"}, {"AbilityName": "LightningStorm", "AbilityID": "82", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "848280", "IdolCost": "123", "UpgradeTime": "439200", "Type": "Offensive", "Rank": "7"}, {"AbilityName": "LightningStorm", "AbilityID": "82", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "1452827", "IdolCost": "154", "UpgradeTime": "634800", "Type": "Offensive", "Rank": "8"}, {"AbilityName": "LightningStorm", "AbilityID": "82", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "2320207", "IdolCost": "190", "UpgradeTime": "715200", "Type": "Offensive", "Rank": "9"}, {"AbilityName": "LightningStorm", "AbilityID": "82", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "3409145", "IdolCost": "211", "UpgradeTime": "1024200", "Type": "Offensive", "Rank": "10"}, {"AbilityName": "CleavingBlows", "AbilityID": "83", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "16419", "IdolCost": "9", "UpgradeTime": "120", "Type": "Attack", "Rank": "1"}, {"AbilityName": "CleavingBlows", "AbilityID": "83", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "38176", "IdolCost": "20", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "CleavingBlows", "AbilityID": "83", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "79501", "IdolCost": "38", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "CleavingBlows", "AbilityID": "83", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "154805", "IdolCost": "48", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "CleavingBlows", "AbilityID": "83", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "286973", "IdolCost": "65", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "CleavingBlows", "AbilityID": "83", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "514014", "IdolCost": "95", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "CleavingBlows", "AbilityID": "83", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "897069", "IdolCost": "126", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "CleavingBlows", "AbilityID": "83", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "1533911", "IdolCost": "158", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "CleavingBlows", "AbilityID": "83", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "2413016", "IdolCost": "191", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "CleavingBlows", "AbilityID": "83", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "3", "Category": "Vindication", "GoldCost": "3545511", "IdolCost": "214", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "LeapStrike", "AbilityID": "84", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "4", "Category": "Vindication", "GoldCost": "17929", "IdolCost": "9", "UpgradeTime": "135", "Type": "Justice", "Rank": "1"}, {"AbilityName": "LeapStrike", "AbilityID": "84", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "4", "Category": "Vindication", "GoldCost": "41194", "IdolCost": "21", "UpgradeTime": "75600", "Type": "Justice", "Rank": "2"}, {"AbilityName": "LeapStrike", "AbilityID": "84", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "4", "Category": "Vindication", "GoldCost": "85170", "IdolCost": "38", "UpgradeTime": "108000", "Type": "Justice", "Rank": "3"}, {"AbilityName": "LeapStrike", "AbilityID": "84", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "4", "Category": "Vindication", "GoldCost": "164549", "IdolCost": "49", "UpgradeTime": "172800", "Type": "Justice", "Rank": "4"}, {"AbilityName": "LeapStrike", "AbilityID": "84", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "4", "Category": "Vindication", "GoldCost": "304292", "IdolCost": "67", "UpgradeTime": "259200", "Type": "Justice", "Rank": "5"}, {"AbilityName": "LeapStrike", "AbilityID": "84", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "4", "Category": "Vindication", "GoldCost": "543338", "IdolCost": "99", "UpgradeTime": "343800", "Type": "Justice", "Rank": "6"}, {"AbilityName": "LeapStrike", "AbilityID": "84", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "4", "Category": "Vindication", "GoldCost": "945857", "IdolCost": "128", "UpgradeTime": "439200", "Type": "Justice", "Rank": "7"}, {"AbilityName": "LeapStrike", "AbilityID": "84", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "4", "Category": "Vindication", "GoldCost": "1614995", "IdolCost": "162", "UpgradeTime": "634800", "Type": "Justice", "Rank": "8"}, {"AbilityName": "LeapStrike", "AbilityID": "84", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "4", "Category": "Vindication", "GoldCost": "2505824", "IdolCost": "193", "UpgradeTime": "715200", "Type": "Justice", "Rank": "9"}, {"AbilityName": "LeapStrike", "AbilityID": "84", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "4", "Category": "Vindication", "GoldCost": "3681877", "IdolCost": "217", "UpgradeTime": "1024200", "Type": "Justice", "Rank": "10"}, {"AbilityName": "LightningBomb", "AbilityID": "85", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "5", "Category": "Vindication", "GoldCost": "19615", "IdolCost": "10", "UpgradeTime": "150", "Type": "Justice", "Rank": "1"}, {"AbilityName": "LightningBomb", "AbilityID": "85", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "5", "Category": "Vindication", "GoldCost": "44516", "IdolCost": "23", "UpgradeTime": "75600", "Type": "Justice", "Rank": "2"}, {"AbilityName": "LightningBomb", "AbilityID": "85", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "5", "Category": "Vindication", "GoldCost": "91339", "IdolCost": "39", "UpgradeTime": "108000", "Type": "Justice", "Rank": "3"}, {"AbilityName": "LightningBomb", "AbilityID": "85", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "5", "Category": "Vindication", "GoldCost": "175706", "IdolCost": "50", "UpgradeTime": "172800", "Type": "Justice", "Rank": "4"}, {"AbilityName": "LightningBomb", "AbilityID": "85", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "5", "Category": "Vindication", "GoldCost": "323253", "IdolCost": "70", "UpgradeTime": "259200", "Type": "Justice", "Rank": "5"}, {"AbilityName": "LightningBomb", "AbilityID": "85", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "5", "Category": "Vindication", "GoldCost": "575613", "IdolCost": "104", "UpgradeTime": "343800", "Type": "Justice", "Rank": "6"}, {"AbilityName": "LightningBomb", "AbilityID": "85", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "5", "Category": "Vindication", "GoldCost": "1000164", "IdolCost": "131", "UpgradeTime": "439200", "Type": "Justice", "Rank": "7"}, {"AbilityName": "LightningBomb", "AbilityID": "85", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "5", "Category": "Vindication", "GoldCost": "1703688", "IdolCost": "166", "UpgradeTime": "634800", "Type": "Justice", "Rank": "8"}, {"AbilityName": "LightningBomb", "AbilityID": "85", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "5", "Category": "Vindication", "GoldCost": "2606057", "IdolCost": "195", "UpgradeTime": "715200", "Type": "Justice", "Rank": "9"}, {"AbilityName": "LightningBomb", "AbilityID": "85", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "5", "Category": "Vindication", "GoldCost": "3829152", "IdolCost": "220", "UpgradeTime": "1024200", "Type": "Justice", "Rank": "10"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON>", "AbilityID": "86", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "6", "Category": "Vindication", "GoldCost": "21301", "IdolCost": "11", "UpgradeTime": "165", "Type": "Justice", "Rank": "1"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON>", "AbilityID": "86", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "6", "Category": "Vindication", "GoldCost": "47838", "IdolCost": "24", "UpgradeTime": "75600", "Type": "Justice", "Rank": "2"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON>", "AbilityID": "86", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "6", "Category": "Vindication", "GoldCost": "97507", "IdolCost": "40", "UpgradeTime": "108000", "Type": "Justice", "Rank": "3"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON>", "AbilityID": "86", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "6", "Category": "Vindication", "GoldCost": "186863", "IdolCost": "52", "UpgradeTime": "172800", "Type": "Justice", "Rank": "4"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON>", "AbilityID": "86", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "6", "Category": "Vindication", "GoldCost": "342213", "IdolCost": "73", "UpgradeTime": "259200", "Type": "Justice", "Rank": "5"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON>", "AbilityID": "86", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "6", "Category": "Vindication", "GoldCost": "607888", "IdolCost": "108", "UpgradeTime": "343800", "Type": "Justice", "Rank": "6"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON>", "AbilityID": "86", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "6", "Category": "Vindication", "GoldCost": "1054470", "IdolCost": "134", "UpgradeTime": "439200", "Type": "Justice", "Rank": "7"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON>", "AbilityID": "86", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "6", "Category": "Vindication", "GoldCost": "1792381", "IdolCost": "171", "UpgradeTime": "634800", "Type": "Justice", "Rank": "8"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON>", "AbilityID": "86", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "6", "Category": "Vindication", "GoldCost": "2706290", "IdolCost": "197", "UpgradeTime": "715200", "Type": "Justice", "Rank": "9"}, {"AbilityName": "<PERSON><PERSON><PERSON><PERSON>", "AbilityID": "86", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "6", "Category": "Vindication", "GoldCost": "3976427", "IdolCost": "223", "UpgradeTime": "1024200", "Type": "Justice", "Rank": "10"}, {"AbilityName": "AxeFlurry", "AbilityID": "87", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "0", "Category": "Vindication", "GoldCost": "15229", "IdolCost": "8", "UpgradeTime": "180", "Type": "Justice", "Rank": "1"}, {"AbilityName": "AxeFlurry", "AbilityID": "87", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "0", "Category": "Vindication", "GoldCost": "35854", "IdolCost": "18", "UpgradeTime": "72000", "Type": "Justice", "Rank": "2"}, {"AbilityName": "AxeFlurry", "AbilityID": "87", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "0", "Category": "Vindication", "GoldCost": "75112", "IdolCost": "37", "UpgradeTime": "108000", "Type": "Justice", "Rank": "3"}, {"AbilityName": "AxeFlurry", "AbilityID": "87", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "0", "Category": "Vindication", "GoldCost": "146739", "IdolCost": "46", "UpgradeTime": "172800", "Type": "Justice", "Rank": "4"}, {"AbilityName": "AxeFlurry", "AbilityID": "87", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "0", "Category": "Vindication", "GoldCost": "272918", "IdolCost": "63", "UpgradeTime": "259200", "Type": "Justice", "Rank": "5"}, {"AbilityName": "AxeFlurry", "AbilityID": "87", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "0", "Category": "Vindication", "GoldCost": "489969", "IdolCost": "92", "UpgradeTime": "343800", "Type": "Justice", "Rank": "6"}, {"AbilityName": "AxeFlurry", "AbilityID": "87", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "0", "Category": "Vindication", "GoldCost": "856599", "IdolCost": "124", "UpgradeTime": "439200", "Type": "Justice", "Rank": "7"}, {"AbilityName": "AxeFlurry", "AbilityID": "87", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "0", "Category": "Vindication", "GoldCost": "1466375", "IdolCost": "154", "UpgradeTime": "634800", "Type": "Justice", "Rank": "8"}, {"AbilityName": "AxeFlurry", "AbilityID": "87", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "0", "Category": "Vindication", "GoldCost": "2332443", "IdolCost": "190", "UpgradeTime": "715200", "Type": "Justice", "Rank": "9"}, {"AbilityName": "AxeFlurry", "AbilityID": "87", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Justicar", "HotbarLocation": "0", "Category": "Vindication", "GoldCost": "3427123", "IdolCost": "212", "UpgradeTime": "1024200", "Type": "Justice", "Rank": "10"}, {"AbilityName": "Devour", "AbilityID": "88", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Duelist", "GoldCost": "9990", "IdolCost": "5", "UpgradeTime": "45", "Type": "Attack", "Rank": "1"}, {"AbilityName": "Devour", "AbilityID": "88", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Duelist", "GoldCost": "25525", "IdolCost": "13", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "Devour", "AbilityID": "88", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Duelist", "GoldCost": "55488", "IdolCost": "28", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "Devour", "AbilityID": "88", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Duelist", "GoldCost": "111408", "IdolCost": "42", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "Devour", "AbilityID": "88", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Duelist", "GoldCost": "211572", "IdolCost": "55", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "Devour", "AbilityID": "88", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Duelist", "GoldCost": "384526", "IdolCost": "78", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "Devour", "AbilityID": "88", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Duelist", "GoldCost": "679864", "IdolCost": "115", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "Devour", "AbilityID": "88", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Duelist", "GoldCost": "1173498", "IdolCost": "140", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "Devour", "AbilityID": "88", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Duelist", "GoldCost": "1989204", "IdolCost": "180", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "Devour", "AbilityID": "88", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Duelist", "GoldCost": "2922793", "IdolCost": "202", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "FatiguingStrike", "AbilityID": "89", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "11107", "IdolCost": "6", "UpgradeTime": "60", "Type": "Attack", "Rank": "1"}, {"AbilityName": "FatiguingStrike", "AbilityID": "89", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "27747", "IdolCost": "14", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "FatiguingStrike", "AbilityID": "89", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "59872", "IdolCost": "30", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "FatiguingStrike", "AbilityID": "89", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "119210", "IdolCost": "43", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "FatiguingStrike", "AbilityID": "89", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "225210", "IdolCost": "57", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "FatiguingStrike", "AbilityID": "89", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "408525", "IdolCost": "81", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "FatiguingStrike", "AbilityID": "89", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "719836", "IdolCost": "117", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "FatiguingStrike", "AbilityID": "89", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "1239978", "IdolCost": "143", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "FatiguingStrike", "AbilityID": "89", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "2068772", "IdolCost": "184", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "FatiguingStrike", "AbilityID": "89", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "1", "Category": "Assault", "GoldCost": "3039705", "IdolCost": "204", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "90", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "12224", "IdolCost": "7", "UpgradeTime": "75", "Type": "Crowd Control", "Rank": "1"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "90", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "29968", "IdolCost": "15", "UpgradeTime": "68400", "Type": "Crowd Control", "Rank": "2"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "90", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "64256", "IdolCost": "33", "UpgradeTime": "108000", "Type": "Crowd Control", "Rank": "3"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "90", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "127012", "IdolCost": "44", "UpgradeTime": "172800", "Type": "Crowd Control", "Rank": "4"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "90", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "238848", "IdolCost": "59", "UpgradeTime": "259200", "Type": "Crowd Control", "Rank": "5"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "90", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "432524", "IdolCost": "85", "UpgradeTime": "343800", "Type": "Crowd Control", "Rank": "6"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "90", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "759808", "IdolCost": "119", "UpgradeTime": "439200", "Type": "Crowd Control", "Rank": "7"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "90", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "1306458", "IdolCost": "146", "UpgradeTime": "634800", "Type": "Crowd Control", "Rank": "8"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "90", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "2148340", "IdolCost": "186", "UpgradeTime": "715200", "Type": "Crowd Control", "Rank": "9"}, {"AbilityName": "<PERSON><PERSON><PERSON>", "AbilityID": "90", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Shadow", "GoldCost": "3156616", "IdolCost": "206", "UpgradeTime": "1024200", "Type": "Crowd Control", "Rank": "10"}, {"AbilityName": "ChaosArmor", "AbilityID": "91", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "13567", "IdolCost": "7", "UpgradeTime": "90", "Type": "Attack", "Rank": "1"}, {"AbilityName": "ChaosArmor", "AbilityID": "91", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "32563", "IdolCost": "17", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "ChaosArmor", "AbilityID": "91", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "69044", "IdolCost": "35", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "ChaosArmor", "AbilityID": "91", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "136037", "IdolCost": "45", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "ChaosArmor", "AbilityID": "91", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "254251", "IdolCost": "61", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "ChaosArmor", "AbilityID": "91", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "458607", "IdolCost": "88", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "ChaosArmor", "AbilityID": "91", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "804044", "IdolCost": "121", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "ChaosArmor", "AbilityID": "91", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "1379643", "IdolCost": "150", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "ChaosArmor", "AbilityID": "91", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "2234274", "IdolCost": "188", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "ChaosArmor", "AbilityID": "91", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "2", "Category": "Assault", "GoldCost": "3282881", "IdolCost": "209", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "PoisonLance", "AbilityID": "92", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "14909", "IdolCost": "8", "UpgradeTime": "105", "Type": "Crowd Control", "Rank": "1"}, {"AbilityName": "PoisonLance", "AbilityID": "92", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "35158", "IdolCost": "18", "UpgradeTime": "68400", "Type": "Crowd Control", "Rank": "2"}, {"AbilityName": "PoisonLance", "AbilityID": "92", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "73831", "IdolCost": "37", "UpgradeTime": "108000", "Type": "Crowd Control", "Rank": "3"}, {"AbilityName": "PoisonLance", "AbilityID": "92", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "145061", "IdolCost": "46", "UpgradeTime": "172800", "Type": "Crowd Control", "Rank": "4"}, {"AbilityName": "PoisonLance", "AbilityID": "92", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "269653", "IdolCost": "63", "UpgradeTime": "259200", "Type": "Crowd Control", "Rank": "5"}, {"AbilityName": "PoisonLance", "AbilityID": "92", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "484690", "IdolCost": "92", "UpgradeTime": "343800", "Type": "Crowd Control", "Rank": "6"}, {"AbilityName": "PoisonLance", "AbilityID": "92", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "848280", "IdolCost": "123", "UpgradeTime": "439200", "Type": "Crowd Control", "Rank": "7"}, {"AbilityName": "PoisonLance", "AbilityID": "92", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "1452827", "IdolCost": "154", "UpgradeTime": "634800", "Type": "Crowd Control", "Rank": "8"}, {"AbilityName": "PoisonLance", "AbilityID": "92", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "2320207", "IdolCost": "190", "UpgradeTime": "715200", "Type": "Crowd Control", "Rank": "9"}, {"AbilityName": "PoisonLance", "AbilityID": "92", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Duelist", "GoldCost": "3409145", "IdolCost": "211", "UpgradeTime": "1024200", "Type": "Crowd Control", "Rank": "10"}, {"AbilityName": "Reaper", "AbilityID": "93", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "16419", "IdolCost": "9", "UpgradeTime": "120", "Type": "Attack", "Rank": "1"}, {"AbilityName": "Reaper", "AbilityID": "93", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "38176", "IdolCost": "20", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "Reaper", "AbilityID": "93", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "79501", "IdolCost": "38", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "Reaper", "AbilityID": "93", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "154805", "IdolCost": "48", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "Reaper", "AbilityID": "93", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "286973", "IdolCost": "65", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "Reaper", "AbilityID": "93", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "514014", "IdolCost": "95", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "Reaper", "AbilityID": "93", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "897069", "IdolCost": "126", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "Reaper", "AbilityID": "93", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "1533911", "IdolCost": "158", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "Reaper", "AbilityID": "93", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "2413016", "IdolCost": "191", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "Reaper", "AbilityID": "93", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "3", "Category": "Shadow", "GoldCost": "3545511", "IdolCost": "214", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "SoulShatter", "AbilityID": "94", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "4", "Category": "Duelist", "GoldCost": "17929", "IdolCost": "9", "UpgradeTime": "135", "Type": "Blade", "Rank": "1"}, {"AbilityName": "SoulShatter", "AbilityID": "94", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "4", "Category": "Duelist", "GoldCost": "41194", "IdolCost": "21", "UpgradeTime": "75600", "Type": "Blade", "Rank": "2"}, {"AbilityName": "SoulShatter", "AbilityID": "94", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "4", "Category": "Duelist", "GoldCost": "85170", "IdolCost": "38", "UpgradeTime": "108000", "Type": "Blade", "Rank": "3"}, {"AbilityName": "SoulShatter", "AbilityID": "94", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "4", "Category": "Duelist", "GoldCost": "164549", "IdolCost": "49", "UpgradeTime": "172800", "Type": "Blade", "Rank": "4"}, {"AbilityName": "SoulShatter", "AbilityID": "94", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "4", "Category": "Duelist", "GoldCost": "304292", "IdolCost": "67", "UpgradeTime": "259200", "Type": "Blade", "Rank": "5"}, {"AbilityName": "SoulShatter", "AbilityID": "94", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "4", "Category": "Duelist", "GoldCost": "543338", "IdolCost": "99", "UpgradeTime": "343800", "Type": "Blade", "Rank": "6"}, {"AbilityName": "SoulShatter", "AbilityID": "94", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "4", "Category": "Duelist", "GoldCost": "945857", "IdolCost": "128", "UpgradeTime": "439200", "Type": "Blade", "Rank": "7"}, {"AbilityName": "SoulShatter", "AbilityID": "94", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "4", "Category": "Duelist", "GoldCost": "1614995", "IdolCost": "162", "UpgradeTime": "634800", "Type": "Blade", "Rank": "8"}, {"AbilityName": "SoulShatter", "AbilityID": "94", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "4", "Category": "Duelist", "GoldCost": "2505824", "IdolCost": "193", "UpgradeTime": "715200", "Type": "Blade", "Rank": "9"}, {"AbilityName": "SoulShatter", "AbilityID": "94", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "4", "Category": "Duelist", "GoldCost": "3681877", "IdolCost": "217", "UpgradeTime": "1024200", "Type": "Blade", "Rank": "10"}, {"AbilityName": "SoulReaver", "AbilityID": "95", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "5", "Category": "Duelist", "GoldCost": "19615", "IdolCost": "10", "UpgradeTime": "150", "Type": "Blade", "Rank": "1"}, {"AbilityName": "SoulReaver", "AbilityID": "95", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "5", "Category": "Duelist", "GoldCost": "44516", "IdolCost": "23", "UpgradeTime": "75600", "Type": "Blade", "Rank": "2"}, {"AbilityName": "SoulReaver", "AbilityID": "95", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "5", "Category": "Duelist", "GoldCost": "91339", "IdolCost": "39", "UpgradeTime": "108000", "Type": "Blade", "Rank": "3"}, {"AbilityName": "SoulReaver", "AbilityID": "95", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "5", "Category": "Duelist", "GoldCost": "175706", "IdolCost": "50", "UpgradeTime": "172800", "Type": "Blade", "Rank": "4"}, {"AbilityName": "SoulReaver", "AbilityID": "95", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "5", "Category": "Duelist", "GoldCost": "323253", "IdolCost": "70", "UpgradeTime": "259200", "Type": "Blade", "Rank": "5"}, {"AbilityName": "SoulReaver", "AbilityID": "95", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "5", "Category": "Duelist", "GoldCost": "575613", "IdolCost": "104", "UpgradeTime": "343800", "Type": "Blade", "Rank": "6"}, {"AbilityName": "SoulReaver", "AbilityID": "95", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "5", "Category": "Duelist", "GoldCost": "1000164", "IdolCost": "131", "UpgradeTime": "439200", "Type": "Blade", "Rank": "7"}, {"AbilityName": "SoulReaver", "AbilityID": "95", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "5", "Category": "Duelist", "GoldCost": "1703688", "IdolCost": "166", "UpgradeTime": "634800", "Type": "Blade", "Rank": "8"}, {"AbilityName": "SoulReaver", "AbilityID": "95", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "5", "Category": "Duelist", "GoldCost": "2606057", "IdolCost": "195", "UpgradeTime": "715200", "Type": "Blade", "Rank": "9"}, {"AbilityName": "SoulReaver", "AbilityID": "95", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "5", "Category": "Duelist", "GoldCost": "3829152", "IdolCost": "220", "UpgradeTime": "1024200", "Type": "Blade", "Rank": "10"}, {"AbilityName": "GhostBlade", "AbilityID": "96", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "6", "Category": "Duelist", "GoldCost": "21301", "IdolCost": "11", "UpgradeTime": "165", "Type": "Blade", "Rank": "1"}, {"AbilityName": "GhostBlade", "AbilityID": "96", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "6", "Category": "Duelist", "GoldCost": "47838", "IdolCost": "24", "UpgradeTime": "75600", "Type": "Blade", "Rank": "2"}, {"AbilityName": "GhostBlade", "AbilityID": "96", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "6", "Category": "Duelist", "GoldCost": "97507", "IdolCost": "40", "UpgradeTime": "108000", "Type": "Blade", "Rank": "3"}, {"AbilityName": "GhostBlade", "AbilityID": "96", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "6", "Category": "Duelist", "GoldCost": "186863", "IdolCost": "52", "UpgradeTime": "172800", "Type": "Blade", "Rank": "4"}, {"AbilityName": "GhostBlade", "AbilityID": "96", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "6", "Category": "Duelist", "GoldCost": "342213", "IdolCost": "73", "UpgradeTime": "259200", "Type": "Blade", "Rank": "5"}, {"AbilityName": "GhostBlade", "AbilityID": "96", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "6", "Category": "Duelist", "GoldCost": "607888", "IdolCost": "108", "UpgradeTime": "343800", "Type": "Blade", "Rank": "6"}, {"AbilityName": "GhostBlade", "AbilityID": "96", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "6", "Category": "Duelist", "GoldCost": "1054470", "IdolCost": "134", "UpgradeTime": "439200", "Type": "Blade", "Rank": "7"}, {"AbilityName": "GhostBlade", "AbilityID": "96", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "6", "Category": "Duelist", "GoldCost": "1792381", "IdolCost": "171", "UpgradeTime": "634800", "Type": "Blade", "Rank": "8"}, {"AbilityName": "GhostBlade", "AbilityID": "96", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "6", "Category": "Duelist", "GoldCost": "2706290", "IdolCost": "197", "UpgradeTime": "715200", "Type": "Blade", "Rank": "9"}, {"AbilityName": "GhostBlade", "AbilityID": "96", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "6", "Category": "Duelist", "GoldCost": "3976427", "IdolCost": "223", "UpgradeTime": "1024200", "Type": "Blade", "Rank": "10"}, {"AbilityName": "HeavyDagger", "AbilityID": "97", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "0", "Category": "Duelist", "GoldCost": "15229", "IdolCost": "8", "UpgradeTime": "180", "Type": "Blade", "Rank": "1"}, {"AbilityName": "HeavyDagger", "AbilityID": "97", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "0", "Category": "Duelist", "GoldCost": "35854", "IdolCost": "18", "UpgradeTime": "72000", "Type": "Blade", "Rank": "2"}, {"AbilityName": "HeavyDagger", "AbilityID": "97", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "0", "Category": "Duelist", "GoldCost": "75112", "IdolCost": "37", "UpgradeTime": "108000", "Type": "Blade", "Rank": "3"}, {"AbilityName": "HeavyDagger", "AbilityID": "97", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "0", "Category": "Duelist", "GoldCost": "146739", "IdolCost": "46", "UpgradeTime": "172800", "Type": "Blade", "Rank": "4"}, {"AbilityName": "HeavyDagger", "AbilityID": "97", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "0", "Category": "Duelist", "GoldCost": "272918", "IdolCost": "63", "UpgradeTime": "259200", "Type": "Blade", "Rank": "5"}, {"AbilityName": "HeavyDagger", "AbilityID": "97", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "0", "Category": "Duelist", "GoldCost": "489969", "IdolCost": "92", "UpgradeTime": "343800", "Type": "Blade", "Rank": "6"}, {"AbilityName": "HeavyDagger", "AbilityID": "97", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "0", "Category": "Duelist", "GoldCost": "856599", "IdolCost": "124", "UpgradeTime": "439200", "Type": "Blade", "Rank": "7"}, {"AbilityName": "HeavyDagger", "AbilityID": "97", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "0", "Category": "Duelist", "GoldCost": "1466375", "IdolCost": "154", "UpgradeTime": "634800", "Type": "Blade", "Rank": "8"}, {"AbilityName": "HeavyDagger", "AbilityID": "97", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "0", "Category": "Duelist", "GoldCost": "2332443", "IdolCost": "190", "UpgradeTime": "715200", "Type": "Blade", "Rank": "9"}, {"AbilityName": "HeavyDagger", "AbilityID": "97", "BaseClass": "Rogue", "Class": "Soulthief", "HotbarLocation": "0", "Category": "Duelist", "GoldCost": "3427123", "IdolCost": "212", "UpgradeTime": "1024200", "Type": "Blade", "Rank": "10"}, {"AbilityName": "Lifethirst", "AbilityID": "98", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "9990", "IdolCost": "5", "UpgradeTime": "45", "Type": "Survival", "Rank": "1"}, {"AbilityName": "Lifethirst", "AbilityID": "98", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "25525", "IdolCost": "13", "UpgradeTime": "68400", "Type": "Survival", "Rank": "2"}, {"AbilityName": "Lifethirst", "AbilityID": "98", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "55488", "IdolCost": "28", "UpgradeTime": "108000", "Type": "Survival", "Rank": "3"}, {"AbilityName": "Lifethirst", "AbilityID": "98", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "111408", "IdolCost": "42", "UpgradeTime": "172800", "Type": "Survival", "Rank": "4"}, {"AbilityName": "Lifethirst", "AbilityID": "98", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "211572", "IdolCost": "55", "UpgradeTime": "259200", "Type": "Survival", "Rank": "5"}, {"AbilityName": "Lifethirst", "AbilityID": "98", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "384526", "IdolCost": "78", "UpgradeTime": "343800", "Type": "Survival", "Rank": "6"}, {"AbilityName": "Lifethirst", "AbilityID": "98", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "679864", "IdolCost": "115", "UpgradeTime": "439200", "Type": "Survival", "Rank": "7"}, {"AbilityName": "Lifethirst", "AbilityID": "98", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "1173498", "IdolCost": "140", "UpgradeTime": "634800", "Type": "Survival", "Rank": "8"}, {"AbilityName": "Lifethirst", "AbilityID": "98", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "1989204", "IdolCost": "180", "UpgradeTime": "715200", "Type": "Survival", "Rank": "9"}, {"AbilityName": "Lifethirst", "AbilityID": "98", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "2922793", "IdolCost": "202", "UpgradeTime": "1024200", "Type": "Survival", "Rank": "10"}, {"AbilityName": "Desecrate", "AbilityID": "99", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "11107", "IdolCost": "6", "UpgradeTime": "60", "Type": "Survival", "Rank": "1"}, {"AbilityName": "Desecrate", "AbilityID": "99", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "27747", "IdolCost": "14", "UpgradeTime": "68400", "Type": "Survival", "Rank": "2"}, {"AbilityName": "Desecrate", "AbilityID": "99", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "59872", "IdolCost": "30", "UpgradeTime": "108000", "Type": "Survival", "Rank": "3"}, {"AbilityName": "Desecrate", "AbilityID": "99", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "119210", "IdolCost": "43", "UpgradeTime": "172800", "Type": "Survival", "Rank": "4"}, {"AbilityName": "Desecrate", "AbilityID": "99", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "225210", "IdolCost": "57", "UpgradeTime": "259200", "Type": "Survival", "Rank": "5"}, {"AbilityName": "Desecrate", "AbilityID": "99", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "408525", "IdolCost": "81", "UpgradeTime": "343800", "Type": "Survival", "Rank": "6"}, {"AbilityName": "Desecrate", "AbilityID": "99", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "719836", "IdolCost": "117", "UpgradeTime": "439200", "Type": "Survival", "Rank": "7"}, {"AbilityName": "Desecrate", "AbilityID": "99", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "1239978", "IdolCost": "143", "UpgradeTime": "634800", "Type": "Survival", "Rank": "8"}, {"AbilityName": "Desecrate", "AbilityID": "99", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "2068772", "IdolCost": "184", "UpgradeTime": "715200", "Type": "Survival", "Rank": "9"}, {"AbilityName": "Desecrate", "AbilityID": "99", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "1", "Category": "Darkness", "GoldCost": "3039705", "IdolCost": "204", "UpgradeTime": "1024200", "Type": "Survival", "Rank": "10"}, {"AbilityName": "Infestation", "AbilityID": "100", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "12224", "IdolCost": "7", "UpgradeTime": "75", "Type": "Survival", "Rank": "1"}, {"AbilityName": "Infestation", "AbilityID": "100", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "29968", "IdolCost": "15", "UpgradeTime": "68400", "Type": "Survival", "Rank": "2"}, {"AbilityName": "Infestation", "AbilityID": "100", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "64256", "IdolCost": "33", "UpgradeTime": "108000", "Type": "Survival", "Rank": "3"}, {"AbilityName": "Infestation", "AbilityID": "100", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "127012", "IdolCost": "44", "UpgradeTime": "172800", "Type": "Survival", "Rank": "4"}, {"AbilityName": "Infestation", "AbilityID": "100", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "238848", "IdolCost": "59", "UpgradeTime": "259200", "Type": "Survival", "Rank": "5"}, {"AbilityName": "Infestation", "AbilityID": "100", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "432524", "IdolCost": "85", "UpgradeTime": "343800", "Type": "Survival", "Rank": "6"}, {"AbilityName": "Infestation", "AbilityID": "100", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "759808", "IdolCost": "119", "UpgradeTime": "439200", "Type": "Survival", "Rank": "7"}, {"AbilityName": "Infestation", "AbilityID": "100", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "1306458", "IdolCost": "146", "UpgradeTime": "634800", "Type": "Survival", "Rank": "8"}, {"AbilityName": "Infestation", "AbilityID": "100", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "2148340", "IdolCost": "186", "UpgradeTime": "715200", "Type": "Survival", "Rank": "9"}, {"AbilityName": "Infestation", "AbilityID": "100", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "3156616", "IdolCost": "206", "UpgradeTime": "1024200", "Type": "Survival", "Rank": "10"}, {"AbilityName": "SpectralGrasp", "AbilityID": "101", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "13567", "IdolCost": "7", "UpgradeTime": "90", "Type": "Attack", "Rank": "1"}, {"AbilityName": "SpectralGrasp", "AbilityID": "101", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "32563", "IdolCost": "17", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "SpectralGrasp", "AbilityID": "101", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "69044", "IdolCost": "35", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "SpectralGrasp", "AbilityID": "101", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "136037", "IdolCost": "45", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "SpectralGrasp", "AbilityID": "101", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "254251", "IdolCost": "61", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "SpectralGrasp", "AbilityID": "101", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "458607", "IdolCost": "88", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "SpectralGrasp", "AbilityID": "101", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "804044", "IdolCost": "121", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "SpectralGrasp", "AbilityID": "101", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "1379643", "IdolCost": "150", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "SpectralGrasp", "AbilityID": "101", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "2234274", "IdolCost": "188", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "SpectralGrasp", "AbilityID": "101", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "2", "Category": "Darkness", "GoldCost": "3282881", "IdolCost": "209", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "BansheeWail", "AbilityID": "102", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "14909", "IdolCost": "8", "UpgradeTime": "105", "Type": "Attack", "Rank": "1"}, {"AbilityName": "BansheeWail", "AbilityID": "102", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "35158", "IdolCost": "18", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "BansheeWail", "AbilityID": "102", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "73831", "IdolCost": "37", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "BansheeWail", "AbilityID": "102", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "145061", "IdolCost": "46", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "BansheeWail", "AbilityID": "102", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "269653", "IdolCost": "63", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "BansheeWail", "AbilityID": "102", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "484690", "IdolCost": "92", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "BansheeWail", "AbilityID": "102", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "848280", "IdolCost": "123", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "BansheeWail", "AbilityID": "102", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "1452827", "IdolCost": "154", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "BansheeWail", "AbilityID": "102", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "2320207", "IdolCost": "190", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "BansheeWail", "AbilityID": "102", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "3409145", "IdolCost": "211", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "DeathMark", "AbilityID": "103", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "16419", "IdolCost": "9", "UpgradeTime": "120", "Type": "Attack", "Rank": "1"}, {"AbilityName": "DeathMark", "AbilityID": "103", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "38176", "IdolCost": "20", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "DeathMark", "AbilityID": "103", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "79501", "IdolCost": "38", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "DeathMark", "AbilityID": "103", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "154805", "IdolCost": "48", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "DeathMark", "AbilityID": "103", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "286973", "IdolCost": "65", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "DeathMark", "AbilityID": "103", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "514014", "IdolCost": "95", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "DeathMark", "AbilityID": "103", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "897069", "IdolCost": "126", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "DeathMark", "AbilityID": "103", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "1533911", "IdolCost": "158", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "DeathMark", "AbilityID": "103", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "2413016", "IdolCost": "191", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "DeathMark", "AbilityID": "103", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "3", "Category": "Darkness", "GoldCost": "3545511", "IdolCost": "214", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "<PERSON>mmonGhoul", "AbilityID": "104", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "4", "Category": "Darkness", "GoldCost": "17929", "IdolCost": "9", "UpgradeTime": "135", "Type": "Death", "Rank": "1"}, {"AbilityName": "<PERSON>mmonGhoul", "AbilityID": "104", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "4", "Category": "Darkness", "GoldCost": "41194", "IdolCost": "21", "UpgradeTime": "75600", "Type": "Death", "Rank": "2"}, {"AbilityName": "<PERSON>mmonGhoul", "AbilityID": "104", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "4", "Category": "Darkness", "GoldCost": "85170", "IdolCost": "38", "UpgradeTime": "108000", "Type": "Death", "Rank": "3"}, {"AbilityName": "<PERSON>mmonGhoul", "AbilityID": "104", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "4", "Category": "Darkness", "GoldCost": "164549", "IdolCost": "49", "UpgradeTime": "172800", "Type": "Death", "Rank": "4"}, {"AbilityName": "<PERSON>mmonGhoul", "AbilityID": "104", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "4", "Category": "Darkness", "GoldCost": "304292", "IdolCost": "67", "UpgradeTime": "259200", "Type": "Death", "Rank": "5"}, {"AbilityName": "<PERSON>mmonGhoul", "AbilityID": "104", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "4", "Category": "Darkness", "GoldCost": "543338", "IdolCost": "99", "UpgradeTime": "343800", "Type": "Death", "Rank": "6"}, {"AbilityName": "<PERSON>mmonGhoul", "AbilityID": "104", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "4", "Category": "Darkness", "GoldCost": "945857", "IdolCost": "128", "UpgradeTime": "439200", "Type": "Death", "Rank": "7"}, {"AbilityName": "<PERSON>mmonGhoul", "AbilityID": "104", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "4", "Category": "Darkness", "GoldCost": "1614995", "IdolCost": "162", "UpgradeTime": "634800", "Type": "Death", "Rank": "8"}, {"AbilityName": "<PERSON>mmonGhoul", "AbilityID": "104", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "4", "Category": "Darkness", "GoldCost": "2505824", "IdolCost": "193", "UpgradeTime": "715200", "Type": "Death", "Rank": "9"}, {"AbilityName": "<PERSON>mmonGhoul", "AbilityID": "104", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "4", "Category": "Darkness", "GoldCost": "3681877", "IdolCost": "217", "UpgradeTime": "1024200", "Type": "Death", "Rank": "10"}, {"AbilityName": "SummonRangedGhoul", "AbilityID": "105", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "5", "Category": "Darkness", "GoldCost": "19615", "IdolCost": "10", "UpgradeTime": "150", "Type": "Death", "Rank": "1"}, {"AbilityName": "SummonRangedGhoul", "AbilityID": "105", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "5", "Category": "Darkness", "GoldCost": "44516", "IdolCost": "23", "UpgradeTime": "75600", "Type": "Death", "Rank": "2"}, {"AbilityName": "SummonRangedGhoul", "AbilityID": "105", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "5", "Category": "Darkness", "GoldCost": "91339", "IdolCost": "39", "UpgradeTime": "108000", "Type": "Death", "Rank": "3"}, {"AbilityName": "SummonRangedGhoul", "AbilityID": "105", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "5", "Category": "Darkness", "GoldCost": "175706", "IdolCost": "50", "UpgradeTime": "172800", "Type": "Death", "Rank": "4"}, {"AbilityName": "SummonRangedGhoul", "AbilityID": "105", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "5", "Category": "Darkness", "GoldCost": "323253", "IdolCost": "70", "UpgradeTime": "259200", "Type": "Death", "Rank": "5"}, {"AbilityName": "SummonRangedGhoul", "AbilityID": "105", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "5", "Category": "Darkness", "GoldCost": "575613", "IdolCost": "104", "UpgradeTime": "343800", "Type": "Death", "Rank": "6"}, {"AbilityName": "SummonRangedGhoul", "AbilityID": "105", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "5", "Category": "Darkness", "GoldCost": "1000164", "IdolCost": "131", "UpgradeTime": "439200", "Type": "Death", "Rank": "7"}, {"AbilityName": "SummonRangedGhoul", "AbilityID": "105", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "5", "Category": "Darkness", "GoldCost": "1703688", "IdolCost": "166", "UpgradeTime": "634800", "Type": "Death", "Rank": "8"}, {"AbilityName": "SummonRangedGhoul", "AbilityID": "105", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "5", "Category": "Darkness", "GoldCost": "2606057", "IdolCost": "195", "UpgradeTime": "715200", "Type": "Death", "Rank": "9"}, {"AbilityName": "SummonRangedGhoul", "AbilityID": "105", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "5", "Category": "Darkness", "GoldCost": "3829152", "IdolCost": "220", "UpgradeTime": "1024200", "Type": "Death", "Rank": "10"}, {"AbilityName": "PlagueBattalion", "AbilityID": "106", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "6", "Category": "Darkness", "GoldCost": "21301", "IdolCost": "11", "UpgradeTime": "165", "Type": "Death", "Rank": "1"}, {"AbilityName": "PlagueBattalion", "AbilityID": "106", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "6", "Category": "Darkness", "GoldCost": "47838", "IdolCost": "24", "UpgradeTime": "75600", "Type": "Death", "Rank": "2"}, {"AbilityName": "PlagueBattalion", "AbilityID": "106", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "6", "Category": "Darkness", "GoldCost": "97507", "IdolCost": "40", "UpgradeTime": "108000", "Type": "Death", "Rank": "3"}, {"AbilityName": "PlagueBattalion", "AbilityID": "106", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "6", "Category": "Darkness", "GoldCost": "186863", "IdolCost": "52", "UpgradeTime": "172800", "Type": "Death", "Rank": "4"}, {"AbilityName": "PlagueBattalion", "AbilityID": "106", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "6", "Category": "Darkness", "GoldCost": "342213", "IdolCost": "73", "UpgradeTime": "259200", "Type": "Death", "Rank": "5"}, {"AbilityName": "PlagueBattalion", "AbilityID": "106", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "6", "Category": "Darkness", "GoldCost": "607888", "IdolCost": "108", "UpgradeTime": "343800", "Type": "Death", "Rank": "6"}, {"AbilityName": "PlagueBattalion", "AbilityID": "106", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "6", "Category": "Darkness", "GoldCost": "1054470", "IdolCost": "134", "UpgradeTime": "439200", "Type": "Death", "Rank": "7"}, {"AbilityName": "PlagueBattalion", "AbilityID": "106", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "6", "Category": "Darkness", "GoldCost": "1792381", "IdolCost": "171", "UpgradeTime": "634800", "Type": "Death", "Rank": "8"}, {"AbilityName": "PlagueBattalion", "AbilityID": "106", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "6", "Category": "Darkness", "GoldCost": "2706290", "IdolCost": "197", "UpgradeTime": "715200", "Type": "Death", "Rank": "9"}, {"AbilityName": "PlagueBattalion", "AbilityID": "106", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "6", "Category": "Darkness", "GoldCost": "3976427", "IdolCost": "223", "UpgradeTime": "1024200", "Type": "Death", "Rank": "10"}, {"AbilityName": "LichShot", "AbilityID": "107", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "0", "Category": "Darkness", "GoldCost": "15229", "IdolCost": "8", "UpgradeTime": "180", "Type": "Death", "Rank": "1"}, {"AbilityName": "LichShot", "AbilityID": "107", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "0", "Category": "Darkness", "GoldCost": "35854", "IdolCost": "18", "UpgradeTime": "72000", "Type": "Death", "Rank": "2"}, {"AbilityName": "LichShot", "AbilityID": "107", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "0", "Category": "Darkness", "GoldCost": "75112", "IdolCost": "37", "UpgradeTime": "108000", "Type": "Death", "Rank": "3"}, {"AbilityName": "LichShot", "AbilityID": "107", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "0", "Category": "Darkness", "GoldCost": "146739", "IdolCost": "46", "UpgradeTime": "172800", "Type": "Death", "Rank": "4"}, {"AbilityName": "LichShot", "AbilityID": "107", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "0", "Category": "Darkness", "GoldCost": "272918", "IdolCost": "63", "UpgradeTime": "259200", "Type": "Death", "Rank": "5"}, {"AbilityName": "LichShot", "AbilityID": "107", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "0", "Category": "Darkness", "GoldCost": "489969", "IdolCost": "92", "UpgradeTime": "343800", "Type": "Death", "Rank": "6"}, {"AbilityName": "LichShot", "AbilityID": "107", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "0", "Category": "Darkness", "GoldCost": "856599", "IdolCost": "124", "UpgradeTime": "439200", "Type": "Death", "Rank": "7"}, {"AbilityName": "LichShot", "AbilityID": "107", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "0", "Category": "Darkness", "GoldCost": "1466375", "IdolCost": "154", "UpgradeTime": "634800", "Type": "Death", "Rank": "8"}, {"AbilityName": "LichShot", "AbilityID": "107", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "0", "Category": "Darkness", "GoldCost": "2332443", "IdolCost": "190", "UpgradeTime": "715200", "Type": "Death", "Rank": "9"}, {"AbilityName": "LichShot", "AbilityID": "107", "BaseClass": "Mage", "Class": "Necromancer", "HotbarLocation": "0", "Category": "Darkness", "GoldCost": "3427123", "IdolCost": "212", "UpgradeTime": "1024200", "Type": "Death", "Rank": "10"}, {"AbilityName": "DivineWord", "AbilityID": "108", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "9990", "IdolCost": "5", "UpgradeTime": "45", "Type": "Survival", "Rank": "1"}, {"AbilityName": "DivineWord", "AbilityID": "108", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "25525", "IdolCost": "13", "UpgradeTime": "68400", "Type": "Survival", "Rank": "2"}, {"AbilityName": "DivineWord", "AbilityID": "108", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "55488", "IdolCost": "28", "UpgradeTime": "108000", "Type": "Survival", "Rank": "3"}, {"AbilityName": "DivineWord", "AbilityID": "108", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "111408", "IdolCost": "42", "UpgradeTime": "172800", "Type": "Survival", "Rank": "4"}, {"AbilityName": "DivineWord", "AbilityID": "108", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "211572", "IdolCost": "55", "UpgradeTime": "259200", "Type": "Survival", "Rank": "5"}, {"AbilityName": "DivineWord", "AbilityID": "108", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "384526", "IdolCost": "78", "UpgradeTime": "343800", "Type": "Survival", "Rank": "6"}, {"AbilityName": "DivineWord", "AbilityID": "108", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "679864", "IdolCost": "115", "UpgradeTime": "439200", "Type": "Survival", "Rank": "7"}, {"AbilityName": "DivineWord", "AbilityID": "108", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "1173498", "IdolCost": "140", "UpgradeTime": "634800", "Type": "Survival", "Rank": "8"}, {"AbilityName": "DivineWord", "AbilityID": "108", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "1989204", "IdolCost": "180", "UpgradeTime": "715200", "Type": "Survival", "Rank": "9"}, {"AbilityName": "DivineWord", "AbilityID": "108", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "2922793", "IdolCost": "202", "UpgradeTime": "1024200", "Type": "Survival", "Rank": "10"}, {"AbilityName": "Subjugate", "AbilityID": "109", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "11107", "IdolCost": "6", "UpgradeTime": "60", "Type": "Survival", "Rank": "1"}, {"AbilityName": "Subjugate", "AbilityID": "109", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "27747", "IdolCost": "14", "UpgradeTime": "68400", "Type": "Survival", "Rank": "2"}, {"AbilityName": "Subjugate", "AbilityID": "109", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "59872", "IdolCost": "30", "UpgradeTime": "108000", "Type": "Survival", "Rank": "3"}, {"AbilityName": "Subjugate", "AbilityID": "109", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "119210", "IdolCost": "43", "UpgradeTime": "172800", "Type": "Survival", "Rank": "4"}, {"AbilityName": "Subjugate", "AbilityID": "109", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "225210", "IdolCost": "57", "UpgradeTime": "259200", "Type": "Survival", "Rank": "5"}, {"AbilityName": "Subjugate", "AbilityID": "109", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "408525", "IdolCost": "81", "UpgradeTime": "343800", "Type": "Survival", "Rank": "6"}, {"AbilityName": "Subjugate", "AbilityID": "109", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "719836", "IdolCost": "117", "UpgradeTime": "439200", "Type": "Survival", "Rank": "7"}, {"AbilityName": "Subjugate", "AbilityID": "109", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "1239978", "IdolCost": "143", "UpgradeTime": "634800", "Type": "Survival", "Rank": "8"}, {"AbilityName": "Subjugate", "AbilityID": "109", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "2068772", "IdolCost": "184", "UpgradeTime": "715200", "Type": "Survival", "Rank": "9"}, {"AbilityName": "Subjugate", "AbilityID": "109", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "1", "Category": "Holy", "GoldCost": "3039705", "IdolCost": "204", "UpgradeTime": "1024200", "Type": "Survival", "Rank": "10"}, {"AbilityName": "FountainOfLife", "AbilityID": "110", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "12224", "IdolCost": "7", "UpgradeTime": "75", "Type": "Survival", "Rank": "1"}, {"AbilityName": "FountainOfLife", "AbilityID": "110", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "29968", "IdolCost": "15", "UpgradeTime": "68400", "Type": "Survival", "Rank": "2"}, {"AbilityName": "FountainOfLife", "AbilityID": "110", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "64256", "IdolCost": "33", "UpgradeTime": "108000", "Type": "Survival", "Rank": "3"}, {"AbilityName": "FountainOfLife", "AbilityID": "110", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "127012", "IdolCost": "44", "UpgradeTime": "172800", "Type": "Survival", "Rank": "4"}, {"AbilityName": "FountainOfLife", "AbilityID": "110", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "238848", "IdolCost": "59", "UpgradeTime": "259200", "Type": "Survival", "Rank": "5"}, {"AbilityName": "FountainOfLife", "AbilityID": "110", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "432524", "IdolCost": "85", "UpgradeTime": "343800", "Type": "Survival", "Rank": "6"}, {"AbilityName": "FountainOfLife", "AbilityID": "110", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "759808", "IdolCost": "119", "UpgradeTime": "439200", "Type": "Survival", "Rank": "7"}, {"AbilityName": "FountainOfLife", "AbilityID": "110", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "1306458", "IdolCost": "146", "UpgradeTime": "634800", "Type": "Survival", "Rank": "8"}, {"AbilityName": "FountainOfLife", "AbilityID": "110", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "2148340", "IdolCost": "186", "UpgradeTime": "715200", "Type": "Survival", "Rank": "9"}, {"AbilityName": "FountainOfLife", "AbilityID": "110", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "3156616", "IdolCost": "206", "UpgradeTime": "1024200", "Type": "Survival", "Rank": "10"}, {"AbilityName": "Penance", "AbilityID": "111", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "13567", "IdolCost": "7", "UpgradeTime": "90", "Type": "Attack", "Rank": "1"}, {"AbilityName": "Penance", "AbilityID": "111", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "32563", "IdolCost": "17", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "Penance", "AbilityID": "111", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "69044", "IdolCost": "35", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "Penance", "AbilityID": "111", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "136037", "IdolCost": "45", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "Penance", "AbilityID": "111", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "254251", "IdolCost": "61", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "Penance", "AbilityID": "111", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "458607", "IdolCost": "88", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "Penance", "AbilityID": "111", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "804044", "IdolCost": "121", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "Penance", "AbilityID": "111", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "1379643", "IdolCost": "150", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "Penance", "AbilityID": "111", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "2234274", "IdolCost": "188", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "Penance", "AbilityID": "111", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "2", "Category": "Holy", "GoldCost": "3282881", "IdolCost": "209", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "CelestialLance", "AbilityID": "112", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "14909", "IdolCost": "8", "UpgradeTime": "105", "Type": "Attack", "Rank": "1"}, {"AbilityName": "CelestialLance", "AbilityID": "112", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "35158", "IdolCost": "18", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "CelestialLance", "AbilityID": "112", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "73831", "IdolCost": "37", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "CelestialLance", "AbilityID": "112", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "145061", "IdolCost": "46", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "CelestialLance", "AbilityID": "112", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "269653", "IdolCost": "63", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "CelestialLance", "AbilityID": "112", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "484690", "IdolCost": "92", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "CelestialLance", "AbilityID": "112", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "848280", "IdolCost": "123", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "CelestialLance", "AbilityID": "112", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "1452827", "IdolCost": "154", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "CelestialLance", "AbilityID": "112", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "2320207", "IdolCost": "190", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "CelestialLance", "AbilityID": "112", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "3409145", "IdolCost": "211", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "Verdict", "AbilityID": "113", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "16419", "IdolCost": "9", "UpgradeTime": "120", "Type": "Attack", "Rank": "1"}, {"AbilityName": "Verdict", "AbilityID": "113", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "38176", "IdolCost": "20", "UpgradeTime": "68400", "Type": "Attack", "Rank": "2"}, {"AbilityName": "Verdict", "AbilityID": "113", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "79501", "IdolCost": "38", "UpgradeTime": "108000", "Type": "Attack", "Rank": "3"}, {"AbilityName": "Verdict", "AbilityID": "113", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "154805", "IdolCost": "48", "UpgradeTime": "172800", "Type": "Attack", "Rank": "4"}, {"AbilityName": "Verdict", "AbilityID": "113", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "286973", "IdolCost": "65", "UpgradeTime": "259200", "Type": "Attack", "Rank": "5"}, {"AbilityName": "Verdict", "AbilityID": "113", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "514014", "IdolCost": "95", "UpgradeTime": "343800", "Type": "Attack", "Rank": "6"}, {"AbilityName": "Verdict", "AbilityID": "113", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "897069", "IdolCost": "126", "UpgradeTime": "439200", "Type": "Attack", "Rank": "7"}, {"AbilityName": "Verdict", "AbilityID": "113", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "1533911", "IdolCost": "158", "UpgradeTime": "634800", "Type": "Attack", "Rank": "8"}, {"AbilityName": "Verdict", "AbilityID": "113", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "2413016", "IdolCost": "191", "UpgradeTime": "715200", "Type": "Attack", "Rank": "9"}, {"AbilityName": "Verdict", "AbilityID": "113", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "3", "Category": "Holy", "GoldCost": "3545511", "IdolCost": "214", "UpgradeTime": "1024200", "Type": "Attack", "Rank": "10"}, {"AbilityName": "Sanctum", "AbilityID": "114", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "4", "Category": "Holy", "GoldCost": "17929", "IdolCost": "9", "UpgradeTime": "135", "Type": "Life", "Rank": "1"}, {"AbilityName": "Sanctum", "AbilityID": "114", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "4", "Category": "Holy", "GoldCost": "41194", "IdolCost": "21", "UpgradeTime": "75600", "Type": "Life", "Rank": "2"}, {"AbilityName": "Sanctum", "AbilityID": "114", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "4", "Category": "Holy", "GoldCost": "85170", "IdolCost": "38", "UpgradeTime": "108000", "Type": "Life", "Rank": "3"}, {"AbilityName": "Sanctum", "AbilityID": "114", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "4", "Category": "Holy", "GoldCost": "164549", "IdolCost": "49", "UpgradeTime": "172800", "Type": "Life", "Rank": "4"}, {"AbilityName": "Sanctum", "AbilityID": "114", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "4", "Category": "Holy", "GoldCost": "304292", "IdolCost": "67", "UpgradeTime": "259200", "Type": "Life", "Rank": "5"}, {"AbilityName": "Sanctum", "AbilityID": "114", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "4", "Category": "Holy", "GoldCost": "543338", "IdolCost": "99", "UpgradeTime": "343800", "Type": "Life", "Rank": "6"}, {"AbilityName": "Sanctum", "AbilityID": "114", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "4", "Category": "Holy", "GoldCost": "945857", "IdolCost": "128", "UpgradeTime": "439200", "Type": "Life", "Rank": "7"}, {"AbilityName": "Sanctum", "AbilityID": "114", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "4", "Category": "Holy", "GoldCost": "1614995", "IdolCost": "162", "UpgradeTime": "634800", "Type": "Life", "Rank": "8"}, {"AbilityName": "Sanctum", "AbilityID": "114", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "4", "Category": "Holy", "GoldCost": "2505824", "IdolCost": "193", "UpgradeTime": "715200", "Type": "Life", "Rank": "9"}, {"AbilityName": "Sanctum", "AbilityID": "114", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "4", "Category": "Holy", "GoldCost": "3681877", "IdolCost": "217", "UpgradeTime": "1024200", "Type": "Life", "Rank": "10"}, {"AbilityName": "CleansingLight", "AbilityID": "115", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "5", "Category": "Holy", "GoldCost": "19615", "IdolCost": "10", "UpgradeTime": "150", "Type": "Life", "Rank": "1"}, {"AbilityName": "CleansingLight", "AbilityID": "115", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "5", "Category": "Holy", "GoldCost": "44516", "IdolCost": "23", "UpgradeTime": "75600", "Type": "Life", "Rank": "2"}, {"AbilityName": "CleansingLight", "AbilityID": "115", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "5", "Category": "Holy", "GoldCost": "91339", "IdolCost": "39", "UpgradeTime": "108000", "Type": "Life", "Rank": "3"}, {"AbilityName": "CleansingLight", "AbilityID": "115", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "5", "Category": "Holy", "GoldCost": "175706", "IdolCost": "50", "UpgradeTime": "172800", "Type": "Life", "Rank": "4"}, {"AbilityName": "CleansingLight", "AbilityID": "115", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "5", "Category": "Holy", "GoldCost": "323253", "IdolCost": "70", "UpgradeTime": "259200", "Type": "Life", "Rank": "5"}, {"AbilityName": "CleansingLight", "AbilityID": "115", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "5", "Category": "Holy", "GoldCost": "575613", "IdolCost": "104", "UpgradeTime": "343800", "Type": "Life", "Rank": "6"}, {"AbilityName": "CleansingLight", "AbilityID": "115", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "5", "Category": "Holy", "GoldCost": "1000164", "IdolCost": "131", "UpgradeTime": "439200", "Type": "Life", "Rank": "7"}, {"AbilityName": "CleansingLight", "AbilityID": "115", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "5", "Category": "Holy", "GoldCost": "1703688", "IdolCost": "166", "UpgradeTime": "634800", "Type": "Life", "Rank": "8"}, {"AbilityName": "CleansingLight", "AbilityID": "115", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "5", "Category": "Holy", "GoldCost": "2606057", "IdolCost": "195", "UpgradeTime": "715200", "Type": "Life", "Rank": "9"}, {"AbilityName": "CleansingLight", "AbilityID": "115", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "5", "Category": "Holy", "GoldCost": "3829152", "IdolCost": "220", "UpgradeTime": "1024200", "Type": "Life", "Rank": "10"}, {"AbilityName": "LeoneanAura", "AbilityID": "116", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "6", "Category": "Holy", "GoldCost": "21301", "IdolCost": "11", "UpgradeTime": "165", "Type": "Life", "Rank": "1"}, {"AbilityName": "LeoneanAura", "AbilityID": "116", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "6", "Category": "Holy", "GoldCost": "47838", "IdolCost": "24", "UpgradeTime": "75600", "Type": "Life", "Rank": "2"}, {"AbilityName": "LeoneanAura", "AbilityID": "116", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "6", "Category": "Holy", "GoldCost": "97507", "IdolCost": "40", "UpgradeTime": "108000", "Type": "Life", "Rank": "3"}, {"AbilityName": "LeoneanAura", "AbilityID": "116", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "6", "Category": "Holy", "GoldCost": "186863", "IdolCost": "52", "UpgradeTime": "172800", "Type": "Life", "Rank": "4"}, {"AbilityName": "LeoneanAura", "AbilityID": "116", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "6", "Category": "Holy", "GoldCost": "342213", "IdolCost": "73", "UpgradeTime": "259200", "Type": "Life", "Rank": "5"}, {"AbilityName": "LeoneanAura", "AbilityID": "116", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "6", "Category": "Holy", "GoldCost": "607888", "IdolCost": "108", "UpgradeTime": "343800", "Type": "Life", "Rank": "6"}, {"AbilityName": "LeoneanAura", "AbilityID": "116", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "6", "Category": "Holy", "GoldCost": "1054470", "IdolCost": "134", "UpgradeTime": "439200", "Type": "Life", "Rank": "7"}, {"AbilityName": "LeoneanAura", "AbilityID": "116", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "6", "Category": "Holy", "GoldCost": "1792381", "IdolCost": "171", "UpgradeTime": "634800", "Type": "Life", "Rank": "8"}, {"AbilityName": "LeoneanAura", "AbilityID": "116", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "6", "Category": "Holy", "GoldCost": "2706290", "IdolCost": "197", "UpgradeTime": "715200", "Type": "Life", "Rank": "9"}, {"AbilityName": "LeoneanAura", "AbilityID": "116", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "6", "Category": "Holy", "GoldCost": "3976427", "IdolCost": "223", "UpgradeTime": "1024200", "Type": "Life", "Rank": "10"}, {"AbilityName": "DivineBolt", "AbilityID": "117", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "0", "Category": "Holy", "GoldCost": "15229", "IdolCost": "8", "UpgradeTime": "180", "Type": "Life", "Rank": "1"}, {"AbilityName": "DivineBolt", "AbilityID": "117", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "0", "Category": "Holy", "GoldCost": "35854", "IdolCost": "18", "UpgradeTime": "72000", "Type": "Life", "Rank": "2"}, {"AbilityName": "DivineBolt", "AbilityID": "117", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "0", "Category": "Holy", "GoldCost": "75112", "IdolCost": "37", "UpgradeTime": "108000", "Type": "Life", "Rank": "3"}, {"AbilityName": "DivineBolt", "AbilityID": "117", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "0", "Category": "Holy", "GoldCost": "146739", "IdolCost": "46", "UpgradeTime": "172800", "Type": "Life", "Rank": "4"}, {"AbilityName": "DivineBolt", "AbilityID": "117", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "0", "Category": "Holy", "GoldCost": "272918", "IdolCost": "63", "UpgradeTime": "259200", "Type": "Life", "Rank": "5"}, {"AbilityName": "DivineBolt", "AbilityID": "117", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "0", "Category": "Holy", "GoldCost": "489969", "IdolCost": "92", "UpgradeTime": "343800", "Type": "Life", "Rank": "6"}, {"AbilityName": "DivineBolt", "AbilityID": "117", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "0", "Category": "Holy", "GoldCost": "856599", "IdolCost": "124", "UpgradeTime": "439200", "Type": "Life", "Rank": "7"}, {"AbilityName": "DivineBolt", "AbilityID": "117", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "0", "Category": "Holy", "GoldCost": "1466375", "IdolCost": "154", "UpgradeTime": "634800", "Type": "Life", "Rank": "8"}, {"AbilityName": "DivineBolt", "AbilityID": "117", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "0", "Category": "Holy", "GoldCost": "2332443", "IdolCost": "190", "UpgradeTime": "715200", "Type": "Life", "Rank": "9"}, {"AbilityName": "DivineBolt", "AbilityID": "117", "BaseClass": "<PERSON><PERSON><PERSON>", "Class": "Templar", "HotbarLocation": "0", "Category": "Holy", "GoldCost": "3427123", "IdolCost": "212", "UpgradeTime": "1024200", "Type": "Life", "Rank": "10"}, {"AbilityName": "SwordMelee", "AbilityID": "118", "BaseClass": "Any", "Class": "Any", "HotbarLocation": "0", "Category": "Assault", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "0", "Rank": "0"}]
[{"MissionName": "DefendTheShip", "MissionID": "1", "ZoneSet": "NewbieRoad", "Priority": "Story", "ReturnName": "Captain<PERSON><PERSON>", "CompleteCount": "1", "Dungeon": "TutorialBoat", "MissionLevel": "1", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Goblin Assault", "TrackerText": "Defend the Niobe from the monster onslaught", "TrackerReturn": "Get the map from Captain <PERSON>", "Description": "As the good ship <PERSON><PERSON> approaches a mysterious shore, you must defend it from an onslaught of monsters.", "ReturnText": "You've saved us!"}, {"MissionName": "MeetTheTown", "MissionID": "2", "PreReqMissions": "DefendTheShip", "ZoneSet": "NewbieRoad", "Priority": "Story", "ContactName": "Captain<PERSON><PERSON>", "ReturnName": "NR_Mayor01", "CompleteCount": "0", "MissionLevel": "2", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Washed Ashore", "TrackerReturn": "Find the Mayor of the town to the east", "Description": "You've made it to goblin-held lands. Could there really be humans still alive after all these years?", "OfferText": "We need to find that village.", "ActiveText": "You can find the village on the map.=@I'm headed there to see if those really are survivors.=Just head east and you'll find it.=I'll try to repair the Niobe.", "ReturnText": "It cannot be! Has someone from the homeland finally returned to Ellyria?=", "PraiseText": "I must get word to the king!=I'm amazed there are still humans here."}, {"MissionName": "RescueAnna", "MissionID": "3", "PreReqMissions": "MeetTheTown", "ZoneSet": "NewbieRoad", "Priority": "Story", "ContactName": "NR_Mayor01", "CompleteCount": "1", "Dungeon": "TutorialDungeon", "MissionLevel": "2", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Goblin Kidnappers", "TrackerText": "Rescue Anna from the goblin cave to the east", "Description": "The goblins who were running from you kidnapped the villager <PERSON>. Chase them out of their hiding place. And did the goblins snatch that parrot, too?", "OfferText": "Thanks to hard fighters like <PERSON>, we've held our own", "ActiveText": "Who are you? Where did you come from?=@I am #tn#. The King sent me.=@But I'm surprised to find any humans still living here.= We came over with <PERSON><PERSON><PERSON> fifty years ago.= @<PERSON><PERSON><PERSON>, <PERSON>'s son!=I was a drummer boy in his army.= Part of an expedition to save his father's barony when the goblins invaded=@And you survived all this time. Amazing!= Thanks to two generations of fighters like <PERSON>.= But goblins surprised her on the beach.=@Those sea goblins were running from me. She must have gotten in their way.=@They landed east of here. Any caves in those parts?=Just over the hill there, an old smuggler's hideout.=@Then that's where she'll be. Until I rescue her of course."}, {"MissionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MissionID": "4", "PreReqMissions": "RescueAnna", "ZoneSet": "NewbieRoad", "Priority": "Story", "ContactName": "<PERSON>", "ReturnName": "NR_Mayor01", "CompleteCount": "0", "MissionLevel": "2", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Goblin Kidnappers", "TrackerReturn": "Tell the Mayor that <PERSON> is safe", "Description": "You just helped <PERSON> and finished off the Goblins. Meet the <PERSON> and <PERSON> back in Wolf's End.", "PreReqText": "Help! Break me out of these chains!=Who are you, anyway?=You're not from around here.", "OfferText": "Thanks! We haven't had goblins in over a year.", "ReturnText": "Thank you for freeing <PERSON>! Our fighters need their leader.=@My pleasure. I've been fighting goblins all my life.=Yes, your sea captain has been telling me about the Goblin wars.=@Seems like our victory is not quite complete yet."}, {"MissionName": "ClearYourHouse", "MissionID": "5", "PreReqMissions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ZoneSet": "NewbieRoad", "Priority": "Story", "ContactName": "NR_Mayor01", "CompleteCount": "1", "MissionLevel": "3", "ExpReward": "M", "GoldReward": "M", "DisplayName": "I Claim This Keep", "TrackerText": "Clear the goblins from the ruined keep", "Description": "The original Wolf's End Keep is overrun with goblins. If you drive them out, you could make the keep your own.", "OfferText": "Fifty years ago we had Wolf's End Keep to protect us.", "ActiveText": "<PERSON><PERSON><PERSON> built the keep as a base for his expedition.=But goblins and worse drove us out decades ago.=With your goblin-slaying skills…=@I might be just the one to reclaim the keep.=Well, it's in pretty bad shape, just a ruin now.=@I will clear it out and start rebuilding it.=@I need a safe place here in Ellyria.=If you can drive away the goblins, we'll help as best we can to rebuild it.=@I can take care of the goblins.=@In fact, I will do it right now."}, {"MissionName": "<PERSON><PERSON><PERSON><PERSON>", "MissionID": "6", "PreReqMissions": "GoblinRiver", "ZoneSet": "NewbieRoad", "Priority": "Story", "ContactName": "NR_QuestAnna01", "ReturnName": "NR_QuestAnna02", "CompleteCount": "1", "Dungeon": "GhostBossDungeon", "MissionLevel": "4", "ExpReward": "M", "GoldReward": "M", "DisplayName": "<PERSON><PERSON><PERSON>'s Quest", "TrackerText": "Investigate the Mysterious Nephit's activities", "TrackerReturn": "Tell <PERSON> you sent Nephit packing", "Description": "Some of the goblins now follow a mysterious master named <PERSON><PERSON><PERSON>. He has set up base in an old tomb. Pay him a visit.", "OfferText": "Someone named \"<PERSON><PERSON><PERSON>\" is trying to control the goblins and the undead.", "ActiveText": "My father read through the old reports from when we landed 50 years ago.=@And he found someone named <PERSON><PERSON><PERSON>?=Baron <PERSON> employed the wisest men in the world for his research.=<PERSON><PERSON><PERSON> was one of the best. He was excavating a tomb when the goblins invaded.=@Any idea which one?=We've seen a lot of undead coming from the Tomb of the Slumbering King.=@I'll start my search there.=Okay I'll meet you again when the dust settles.", "ReturnText": "With <PERSON><PERSON><PERSON> driven off, maybe we'll have fewer undead and goblins both."}, {"MissionName": "<PERSON>lay<PERSON><PERSON>D<PERSON><PERSON>", "MissionID": "7", "PreReqMissions": "<PERSON><PERSON><PERSON><PERSON>", "ZoneSet": "NewbieRoad", "Priority": "Story", "ContactName": "NR_QuestAnna02", "ReturnName": "NR_QuestAnna03", "CompleteCount": "1", "Dungeon": "DreamDragonDungeon", "MissionLevel": "5", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Dragon's Dream", "TrackerText": "Learn the secret of the Dream Dragon's Tomb", "TrackerReturn": "Tell <PERSON> you slew the Dream Dragon", "Description": "Looks like <PERSON><PERSON><PERSON> wanted the Dream Dragon's secrets, but he was digging in the wrong place. Now's your chance to discover what he was searching for.", "OfferText": "There is another tomb. Maybe what <PERSON><PERSON><PERSON> sought is in there.", "ActiveText": "You said <PERSON><PERSON><PERSON> mentioned a \"Dream Dragon.\"=@Dragons have been gone for thousands of years.=This other tomb has been here longer than that.=<PERSON> was obsessed with these old crypts.=@<PERSON><PERSON><PERSON> might've wanted dragon bones. They're powerful artifacts.=The tomb is to the south, through the woods. If <PERSON><PERSON><PERSON> can rise a second time...=@Then I'd better get there before he does.=Clear a path and I'll follow.", "ReturnText": "A dragon! Amazing, even if it’s only a kind of dream phantom."}, {"MissionName": "DeliverToSwamp", "MissionID": "8", "PreReqMissions": "<PERSON>lay<PERSON><PERSON>D<PERSON><PERSON>", "ZoneSet": "NewbieRoad,SwampRoadNorth", "Priority": "Story", "ContactName": "NR_QuestAnna03", "ReturnName": "SRN_Mayor01", "CompleteCount": "0", "MissionLevel": "5", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Down the Road", "TrackerReturn": "Take the fight to the Black Rose Mire", "Description": "<PERSON>'s End has been liberated from the goblins. Time to lead the fight to retake <PERSON> from the goblin horde.", "OfferText": "With <PERSON>'s End secure, we can reclaim more of the land from evil.", "ActiveText": "Thanks to you, we're back on our feet and ready to fight!=@That's good to hear. I aim to finish the Goblins off on this side of the ocean.=@Will your people help me?=Of course! <PERSON><PERSON><PERSON>'s family is from the area to the East of here.= He's eager to reclaim his heritage.=@Excellent. And I need to learn more about the Sleeping Lands.=I've never heard of them.=But the archives in the Citadel of Wissen might have some record.=@Then I'll liberate the citadel as well as <PERSON><PERSON><PERSON>'s lands.=@Tell <PERSON><PERSON><PERSON> I'll see him there!=I'll tell him as soon as I get back to town Champ.", "ReturnText": "We will fortify our camp here, hero. My thanks for leading us.", "PraiseText": "This village will be the first of several we reclaim.=@We've got a lot of hard fighting ahead, but we can do it.=This whole land was once green pasture.=The goblins and their magic have turned it into a swamp.=@What can be done with magic can be undone.=@Together we will restore this land.=@Mostly.=Mostly would be more than I ever expected."}, {"MissionName": "GetGoblinNoserings", "MissionID": "9", "PreReqMissions": "ClearYourHouse", "ZoneSet": "NewbieRoad", "Priority": "Side", "ContactName": "NR_Villager02", "ReturnName": "NR_Villager02", "CompleteCount": "5", "ProgressIcon": "a_QuestIcon_GoblinNoserings", "ProgressText": "Goblin Nosering", "MissionLevel": "3", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Recover Rings", "TrackerText": "Defeat five big goblins and recover their noserings", "TrackerReturn": "Collect the nosering bounty from <PERSON>", "Description": "Goblins attack villagers and steal metal. The tougher goblins wear it as jewelry. Get some of the villager's stuff back.", "PreReqText": "I've been on duty for three days.", "OfferText": "Goblins stole all our horseshoes and made noserings", "ActiveText": "The big goblins wear horseshoes as noserings.=To mock us, I think.=But we need horseshoes if we're going to rebuild our army and economy.=@Any chance to take something back from the goblins.", "ReturnText": "Thanks! I'll just get these rings cleaned off…"}, {"MissionName": "GetGoblinWands", "MissionID": "10", "PreReqMissions": "GoblinRiver", "ZoneSet": "NewbieRoad", "Priority": "Side", "ContactName": "NR_Villager03", "ReturnName": "NR_Villager03", "CompleteCount": "10", "ProgressIcon": "a_QuestIcon_Wands", "ProgressText": "<PERSON><PERSON>", "MissionLevel": "4", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Recover <PERSON>", "TrackerText": "Confiscate wands from the goblin shamans", "TrackerReturn": "Turn the wands over to <PERSON><PERSON><PERSON>", "Description": "The goblin shamans use their wands for evil magic. Take the mystic weapons away from them.", "OfferText": "The goblin shamans use their wands to control Death Eyes.", "ActiveText": "If you bring me the wands of goblin shamans, I can use them to drive off Death Eyes.=It will be nice not to have those evil things spying on us.=Especially at night.=While we sleep.", "ReturnText": "Thanks! I should be able to ward off any Death Eyes with these.", "PraiseText": "I hate the way those <PERSON> Eyes look at me.=Thanks again."}, {"MissionName": "DELETED1", "MissionID": "11", "ZoneSet": "TOBEDELETED"}, {"MissionName": "Kill<PERSON><PERSON><PERSON>", "MissionID": "12", "PreReqMissions": "GetGoblinNoserings", "ZoneSet": "NewbieRoad", "Priority": "Side", "ContactName": "NR_Villager02", "ReturnName": "NR_Villager03", "CompleteCount": "30", "MissionLevel": "4", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Goblin Takedown", "TrackerText": "Drive back the goblin menace one goblin at a time", "TrackerReturn": "Tell <PERSON><PERSON><PERSON> that's a few dozen goblins down", "Description": "The goblins are still everywhere, attacking and plundering. Drive them back wherever you find them.", "OfferText": "If there were fewer goblins, we could reclaim this land.", "ActiveText": "I haven't seen the goblins this excited in years.=They're in a panic.=Probably because of you.=@Probably so.", "ReturnText": "Wow,  you're a mighty goblin-slayer!", "PraiseText": "You're one tough goblin fighter.=This place might be saved yet."}, {"MissionName": "RecoverMyStuff", "MissionID": "13", "PreReqMissions": "ClearYourHouse", "ZoneSet": "NewbieRoad", "Priority": "Side", "ContactName": "N<PERSON>_<PERSON>", "ReturnName": "N<PERSON>_<PERSON>", "ActiveTarget": "GoblinThief", "CompleteCount": "1", "ProgressIcon": "a_QuestIcon_GrandmasBracelet", "ProgressText": "Grandma's Bracelet", "MissionLevel": "4", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Looters", "TrackerText": "Teach the goblin raiders a lesson", "TrackerReturn": "Tell <PERSON> he just needs to clean up the goblin bits", "Description": "Goblin raiders still lurk in the woods. Slay their commander and help the villagers hold the line.", "OfferText": "There are goblin raiders holed up in that farm.", "ActiveText": "We've got the trogs cornered.=But they've got some tough commander in there.=If you take care of him, I can hold the line here.", "ReturnText": "You showed him!=We can keep them bottled up in there,= No problem.", "PraiseText": "You've got the goblins on the run.=As long as we keep up the presure…=We can reclaim this land."}, {"MissionName": "KillGraveyardSkeleton", "MissionID": "14", "PreReqMissions": "ClearYourHouse", "ZoneSet": "NewbieRoad", "Priority": "Side", "ContactName": "NR_<PERSON>mit", "ReturnName": "NR_<PERSON>mit", "ActiveTarget": "GraveyardSkeleton", "CompleteCount": "1", "MissionLevel": "5", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Boneyard Monster", "TrackerText": "Help stem the undead tide", "TrackerReturn": "<PERSON> <PERSON> the shrine has settled down", "Description": "The dead are rising in the shrine behind this hill. Hop on the road through the trees to investigate.", "OfferText": "The undead just keep rising from that crypt.", "ActiveText": "The old shrine up there is where the undead first came from.=They've been coming ever since.=If you can help me destroy some of the worst of them…=We can keep them from spreading to Wolf's End.", "ReturnText": "You have my thanks.", "PraiseText": "I'll keep fighting them.=But I'll never get used to them.=Or their smell.=Or the creepy things they say.=But mostly their smell."}, {"MissionName": "StopCastout", "MissionID": "15", "ZoneSet": "SwampRoadNorth", "Priority": "Story", "ContactName": "SRN_Mayor01", "ReturnName": "SRN_Mayor01", "CompleteCount": "1", "Dungeon": "SRN_Mission1", "MissionLevel": "6", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Tower of the Tuatara", "TrackerText": "Drive the Tuatara from the tower east of Sark", "TrackerReturn": "Tell Abbod you've driven away the Tuatara Chiefain", "Description": "A new foe has claimed a nearby tower. Retaking this land starts with driving them out.", "OfferText": "Some new kind of monstrous warrior stands against us now.", "ActiveText": "These aren't goblins we're fighting.=@Not gobins? What are we dealing with then?=Some kind of reptile-men. They call themselves Tuatara.=@Tuatara. I've read the name in old stories. Fearsome soldiers.=That sounds like them, they're very organized.=They've taken over a tower nearby.=We can't get past them.=@I can get past them.=I hope so.=@Me too. Only one way to find out.", "ReturnText": "You showed the Tuatara how humans fight!"}, {"MissionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MissionID": "16", "ZoneSet": "SwampRoadNorth", "Priority": "Dungeon", "ContactName": "<PERSON>", "ReturnName": "<PERSON>", "CompleteCount": "1", "Dungeon": "SRN_Mission2", "MissionLevel": "7", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Mystery of the Yornak", "TrackerText": "What is The Yornak? And what treasure does it keep?", "TrackerReturn": "Tell villagers of the Yornak's defeat", "Description": "Here, deep in the swamp, lies the castle of Lord <PERSON><PERSON><PERSON>, former governor of these lands. What secrets lie buried with him?", "OfferText": "The castle of Lord <PERSON><PERSON><PERSON> seems still inhabited although not by men.", "ActiveText": "Castle Yornak was the home of this region's lords.=The last Lord and Lady <PERSON><PERSON><PERSON> built the magnificent granary, palace, and university.=@They were renowned for their wealth and lavish spending, I know.=Indeed they were, but…=They always kept their ancient castle in good repair.=It was their safe retreat.=@Maybe there are some survivors there too.=There's someone in there.=Someone the Tuatara call Yorna<PERSON>.=@Yornak! Maybe an heir? I'll go find out.", "ReturnText": "You survived Castle Yornak.", "PraiseText": "The Yornak family is another tragic tale.=@The invasions from the Sleeping Lands have corrupted everything.=@I'm glad I finally brought them peace."}, {"MissionName": "TravelToTownTwo", "MissionID": "17", "PreReqMissions": "<PERSON><PERSON><PERSON><PERSON>", "ZoneSet": "SwampRoadNorth", "Priority": "Story", "ContactName": "SRN_Mayor01", "ReturnName": "SRN_Mayor02", "CompleteCount": "0", "MissionLevel": "7", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Deeper into the Swamp", "TrackerReturn": "Meet with <PERSON><PERSON> and see what he's found", "Description": "Travel east to where Ranger <PERSON><PERSON><PERSON> and his party have set up a foreward base. He has discovered something there.", "OfferText": "Ranger <PERSON><PERSON><PERSON> has led a party to scout the old university district.", "ActiveText": "<PERSON><PERSON><PERSON> thinks the source of the corruption is to the east.=Someone named <PERSON><PERSON><PERSON> has set up in the Citadel of Wissen.=@I wanted to poke around there myself.=@I'll go see what he has discovered.=Be careful, the road is overrung with Tuatara and Devourers.", "ReturnText": "It's good to see you again, friend.", "PraiseText": "Your legend grows.=Soon this region will follow you anywhere."}, {"MissionName": "SlayOoyak", "MissionID": "18", "PreReqMissions": "TravelToTownTwo", "ZoneSet": "SwampRoadNorth", "Priority": "Story", "ContactName": "SRN_Mayor02", "ReturnName": "SRN_Mayor02", "CompleteCount": "1", "Dungeon": "SRN_Mission4", "MissionLevel": "8", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Lair of the Ooyak", "TrackerText": "Slay the Tuatara's <PERSON><PERSON><PERSON>", "TrackerReturn": "Tell the rangers that the Great Ooyak is dead", "Description": "The Tuatara rely on enslaved canisaurs for muscle in battle. Descend into the Ooyak's Tower end the source.", "PreReqText": "Are you friend or foe?=Do you bring message from our friends in Sark?=We cannot trust strangers", "OfferText": "The Vizier of the Tuatara is breeding carnisaurs.", "ActiveText": "The Vizier may work for the dragon generals…=But we now know he's the real power behind the corruption.=The Vizier's magical experiments are breeding carnasaurs.=@Those lizard-horse things?=He's breeding them to serve as mounts for Tuatara soldiers.=@They'd be more than a match for our knights back home.=@I need to end this monster-breeding program now.", "ReturnText": "You did it! No carnasaur knights will plague us."}, {"MissionName": "StopBroodvictor", "MissionID": "19", "PreReqMissions": "SlayOoyak", "ZoneSet": "SwampRoadNorth", "Priority": "Story", "ContactName": "SRN_Mayor02", "ReturnName": "SRN_Mayor02", "CompleteCount": "1", "Dungeon": "SRN_Mission5", "MissionLevel": "9", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Citadel of the Vizier", "TrackerText": "Challenge Vizier <PERSON><PERSON><PERSON> in his lair", "TrackerReturn": "Return to the rangers with news of your victory", "Description": "Vizier <PERSON><PERSON><PERSON> is behind the foul magic that has corrupted the swamp. Time to end him and his experiments.", "OfferText": "Vizier <PERSON><PERSON><PERSON> is using the Citadel's library for evil", "ActiveText": "The Vizier will be incredibly angry at you.=@I'm even angrier at him.=@He's perverted the Citadel's wisdom for evil.=I assume you're going after him.=@I am. I'm taking back the library for all of us.=Be careful, the Vizier's magic makes him as dangerous as the dragons he serves.", "ReturnText": "The library is liberated!"}, {"MissionName": "<PERSON><PERSON><PERSON><PERSON>", "MissionID": "20", "PreReqMissions": "StopCastout", "ZoneSet": "SwampRoadNorth", "Priority": "Story", "ContactName": "SRN_Mayor01", "ReturnName": "SRN_Mayor01", "CompleteCount": "1", "Dungeon": "SRN_Mission3", "MissionLevel": "8", "ExpReward": "M", "GoldReward": "M", "DisplayName": "<PERSON><PERSON>'s Spite", "TrackerText": "Drive the Tuatara general from the ruined keep", "TrackerReturn": "Tell the Wolf Enders that generals are dragons", "Description": "The general of the Tuatara legions has his base here. Time to introduce yourself.", "OfferText": "The Tuatara general has set up his base in the castle nearby", "ActiveText": "The Tuatara General, <PERSON><PERSON>, is based in that old castle.=@This <PERSON><PERSON> seems to be one of their important leaders.=@I'm going to take him on and learn where these reptilian soldiers come from.=What is an army doing in this swamp anyway?=@I'm not sure, but armies don't stay in one place.=@Armies this big are for attacking others.=As the nearest other, that's worrisome to me.", "ReturnText": "General <PERSON><PERSON> was a dragon!", "PraiseText": "The Tuatara are led by real, live dragons.=@They are, which isn't good news.=That means there are more dragons.=@Probably bigger ones too.=Good luck with that.=Glad it's not me in your boots."}, {"MissionName": "TravelToTownThree", "MissionID": "21", "PreReqMissions": "StopBroodvictor", "ZoneSet": "SwampRoadNorth", "Priority": "Story", "ContactName": "SRN_Mayor02", "ReturnName": "SRN_Mayor03", "CompleteCount": "0", "MissionLevel": "8", "ExpReward": "L", "GoldReward": "L", "DisplayName": "The Last Town", "TrackerReturn": "Meet with <PERSON><PERSON><PERSON> to learn more", "Description": "Contact <PERSON><PERSON><PERSON> and see what he has learned about the Tuatara Legion's other general.", "OfferText": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> think they know where the Tuatara General is.", "ActiveText": "We have more soldiers scouting the mire.=<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> think they know where the other Tuatara general is.=@Another dragon?=Probably.=Go see them, they know more than I.", "ReturnText": "Glad you could make it.", "PraiseText": "The Tuatara's other general is nearby.=But he's different than <PERSON><PERSON><PERSON>.=@Different how?=We've seen signs of the undead in his service.=@Great. I sense <PERSON><PERSON><PERSON>'s hand in this."}, {"MissionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MissionID": "22", "PreReqMissions": "TravelToTownThree", "ZoneSet": "SwampRoadNorth", "Priority": "Story", "ContactName": "SRN_Mayor03", "ReturnName": "SRN_Mayor03", "CompleteCount": "1", "Dungeon": "SRN_Mission7", "MissionLevel": "9", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Great Green Svath", "TrackerText": "Cut off the head of this dragon invasion", "TrackerReturn": "Tell the soldiers that Tuatara Legion is defeated.", "Description": "The leader of this draconic invasion is in this castle. And it sounds like he's got some foul allies with him.", "PreReqText": "We're still scouting this area.=We'll send word when we know more.", "OfferText": "There's another General, called <PERSON><PERSON><PERSON>. He's in the old palace.", "ActiveText": "General <PERSON><PERSON><PERSON> commands all these Tuatara.=@<PERSON><PERSON><PERSON> and <PERSON><PERSON>? They must be brothers.=Probably so, but this sounds like the big brother.=And <PERSON><PERSON><PERSON> doesn't just command Tuatara soldiers.=He's into scarier stuff.=@You mean besides giant spiders and carnivorous plants?=I mean like undead.=@Great, another grave digger like <PERSON><PERSON><PERSON>.=@An invasion of dragons and ghosts would ruin anyone's week.", "ReturnText": "You stopped the dragon invasion in its tracks!"}, {"MissionName": "ClearTheBridge", "MissionID": "23", "PreReqMissions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ZoneSet": "SwampRoadNorth,BridgeTown", "Priority": "Story", "ContactName": "SRN_Mayor03", "ReturnName": "BT_Greeter", "CompleteCount": "1", "Dungeon": "SwampRoadConnectionMission", "MissionLevel": "10", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Arach<PERSON>'s Swamp", "TrackerText": "Fight you way up the road to Castle Hocke", "TrackerReturn": "Fight your way to Castle Hocke", "Description": "You need to get into Castle Hocke if you want to learn the truth about the monsters overruning Ellyria.", "OfferText": "What's next for you, #tn#?", "ActiveText": "Now that the Tuatara are defeated…=What is your plan?=@I'm going to head for Castle Hocke.=@I'm sure it's all monsters and ruins now.=I can't imagine anyone survived the Tuatara Legion passing through.=@And <PERSON><PERSON><PERSON> is no doubt up to something.=@I need to get into that castle...=@If I'm ever going to learn the truth about where these monsters come from.", "ReturnText": "Hold there, stanger. Who are you?", "PraiseText": "You've made some tremendous changes in these parts."}, {"MissionName": "SlayMindlessQueen", "MissionID": "24", "ZoneSet": "SwampRoadNorth", "Priority": "Dungeon", "ContactName": "Odem", "ReturnName": "Odem", "CompleteCount": "1", "Dungeon": "SRN_Mission6", "MissionLevel": "8", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Mindless Queen's Glade", "TrackerText": "Find the source of this Devourer plague", "TrackerReturn": "Return to the village to claim your reward", "Description": "This used to be the Royal Granary. Now it's the source of all those man-eating plants.", "OfferText": "The ruins of the Royal Granary is the source of the devourers.", "ActiveText": "The Royal Granary used to be a wonder of the world.=@This whole region was the breadbasket of the empire.=But now the ruined granary is corrupted.=The devourers seem to all come from there.=@Then weeding them out will be my next task.=If we can recover the grain fields, we can support a whole army.=@I just need to cut down an army of killer plants first.", "ReturnText": "You cleared the Royal Granary!"}, {"MissionName": "GetLizardBanners", "MissionID": "25", "ZoneSet": "SwampRoadNorth", "Priority": "Side", "ContactName": "<PERSON><PERSON>", "ReturnName": "<PERSON><PERSON>", "CompleteCount": "5", "ProgressIcon": "a_QuestIcon_Banner", "ProgressText": "<PERSON><PERSON>", "MissionLevel": "7", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Banners of the Tuatara", "TrackerText": "Collect the banners of the lizardmen", "TrackerReturn": "Collect the banner bounty from <PERSON><PERSON>", "Description": "A Tuatara troop becomes weak and demoralized if its banner falls. Seizing banners would deal the lizards a blow.", "OfferText": "Those lizards put a lot of stock in those banners", "ActiveText": "The Tuatara rally around their war banners.=They're ferocious when their banner is up.=@And more organized than the goblins ever were.=The banners are enchanted in some way.=If you capture some of them, I might be able to use their magic against them.=@We could use our own magic banners to retake the land!", "ReturnText": "Perfect. I think I can sew our own enchanted banners now.", "PraiseText": "Now I just need a good design.=@If you want to use my portrait, you can.=Um, thanks, I'll consider it.=@I have an inspiring profile.=Uh, you do?=Yes, well, I'll come up with a few options."}, {"MissionName": "StopTheInvader", "MissionID": "26", "ZoneSet": "SwampRoadNorth", "Priority": "Side", "ContactName": "Palok", "ReturnName": "Palok", "ActiveTarget": "<PERSON>rd<PERSON>n<PERSON><PERSON>", "CompleteCount": "1", "MissionLevel": "9", "ExpReward": "M", "GoldReward": "M", "DisplayName": "<PERSON><PERSON>", "TrackerText": "Send the Tuatara packing", "TrackerReturn": "Tell Palok it's all clear", "Description": "The Tuatara set up an outpost in an abandoned farm. You need to push them back out of there.", "OfferText": "A Tuatara squad has set up in an abandoned farm.", "ActiveText": "The lizards took over this old house.=They're using it as a base to launch raids from.=They store supplies there too.=Mostly dried beetles and rancid meat.=So you can maybe leave that there.=Unless you're into that kind of food.=I'm not judging.", "ReturnText": "Thank you!", "PraiseText": "We'll keep an eye on that place.=If any more show up, we'll take care of them.=Thanks for not bringing me the dried beetles.=Although my cousin says they're tasty.=I guess I should try one before I judge."}, {"MissionName": "DroppedInTheWell", "MissionID": "27", "ZoneSet": "SwampRoadNorth", "Priority": "Side", "ContactName": "<PERSON>", "ReturnName": "<PERSON>", "ActiveTarget": "WellMonster", "CompleteCount": "1", "MissionLevel": "8", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Overgrown Well", "TrackerText": "The well behind the thicket is overrun with Devourers", "TrackerReturn": "Tell <PERSON> she can go get a drink", "Description": "Devourers are everywhere. A nasty grove of them keeps <PERSON> from her well. You decide to lend a hand.", "OfferText": "Devourers are everywhere! I can't get to the well", "ActiveText": "Can you get the devourers away from the well?=It’s the only source of fresh water anywhere near here.=These monster have corrupted the river.=And the ponds.=They'd corrupt the rain if they could.", "ReturnText": "Whew, thanks!", "PraiseText": "Thanks for clearing the well.=We really need a reliable water source."}, {"MissionName": "SurroundedBySpiders", "MissionID": "28", "ZoneSet": "SwampRoadNorth", "Priority": "Side", "ContactName": "Sugh", "ReturnName": "Sugh", "ActiveTarget": "SpiderInvader", "CompleteCount": "1", "MissionLevel": "7", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Hut Monster", "TrackerText": "A monster has invaded the farmer's hut behind the hill", "TrackerReturn": "Tell <PERSON>gh you took care of the monster", "Description": "Every kind of monster seems to plague the people of Black Rose Mire. What lives in the hut behind this hill?", "OfferText": "There's something big up in that old farm.", "ActiveText": "I don't know what it is.=I don't think it's Tuatara though.=I figured you would want to check it out.=Because I really don't want to.=Did I mention it sounded big?", "ReturnText": "Ooo! I hate spiders! Thanks!", "PraiseText": "A colossal spider, it was?=I hate spiders.=I'm glad it was you and not me!"}, {"MissionName": "GetDevourerTeeth", "MissionID": "29", "PreReqMissions": "SlayMindlessQueen", "ZoneSet": "SwampRoadNorth", "Priority": "Side", "ContactName": "Odem", "ReturnName": "Odem", "CompleteCount": "10", "ProgressIcon": "a_QuestIcon_DevourerTooth", "ProgressText": "Dev<PERSON><PERSON>", "MissionLevel": "8", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Get Devourer <PERSON>", "TrackerText": "Harvest Devourer teeth to thin out the plant beasts", "TrackerReturn": "Collect the bounty from Odem", "Description": "Now that the Queen is dead, you can thin out the Devourers and help find a way to restore the land.", "OfferText": "These murderous plants are intriguing.", "ActiveText": "We need to learn more about the Devourers.=@I destroyed the source of those foul plants.=Yes, but they grow like weeds.=If we're going to heal the land, I to know more.=Collect teeth from the big ones.=I can use them to concoct a potion to poison them.=@I think we'd all be better off if plants didn't have teeth.", "ReturnText": "Well done, thank you!", "PraiseText": "I hate to think what <PERSON><PERSON><PERSON> did to give plants teeth.=It's terrifying to contemplate.=But it's all I can think about.=What else did he give teeth to?=Mushrooms?=Jellyfish?=Rocks?"}, {"MissionName": "GetLizardGreatHelm", "MissionID": "30", "ZoneSet": "SwampRoadNorth", "Priority": "Side", "ContactName": "<PERSON>", "ReturnName": "<PERSON>", "CompleteCount": "10", "ProgressIcon": "a_QuestIcon_GreatHelm", "ProgressText": "Great Helm", "MissionLevel": "9", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Get Tuatara Great Helms", "TrackerText": "Hunt the Greater Tuatara throughout Black Rose Mire", "TrackerReturn": "Collect the Great Helm bounty from <PERSON>", "Description": "Collect helms from the largest, meanest Tuatara tand let them know that humans are taking back Black Rose Mire.", "OfferText": "Will you help us push back the Tuatara soldiers?", "ActiveText": "Bring me the Helms of the Tuatara Commanders.=@Why do you want their helms?=First, to show them we're taking over. They love their helms.=Second, we can use the metal to forge our own armor.=@Third, it'll make them so angry.=Exactly.", "ReturnText": "Thank you! Those lizards know we're not fooling around now!", "PraiseText": "A real hero on our side!=Maybe we can take the fight to these beasties.=And not just the Tuatara.=Imagine this land free of monster rule!"}, {"MissionName": "GetSpiderFangs", "MissionID": "31", "ZoneSet": "SwampRoadNorth", "Priority": "Side", "ContactName": "Gretta", "ReturnName": "Gretta", "CompleteCount": "10", "ProgressIcon": "a_QuestIcon_SpiderFang", "ProgressText": "<PERSON>", "MissionLevel": "7", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Get Spider Fangs", "TrackerText": "Seek spiders deep in the swamp to help the healer", "TrackerReturn": "Give the spider fangs to <PERSON><PERSON> and collect your bounty", "Description": "<PERSON><PERSON> is brewing an antidote to spider poison. You decide to collect spider fangs for her.", "OfferText": "My antidote for spider poison needs spider fangs", "ActiveText": "If you bring me spider fangs, I'd be able to make an antidote.=@That would definitely come in handy.=These big spiders didn't used to live here.=@They were corrupted by the some foul magic.=That's why I hate magic, always corrupting things.=I much prefer alchemy.", "ReturnText": "Thank you, now I can finish the antidote", "PraiseText": "It will take years to get rid of these spiders.=If we can ever figure out where they come from."}, {"MissionName": "SeeTheWarden", "MissionID": "32", "PreReqMissions": "ClearTheBridge", "ZoneSet": "BridgeTown", "Priority": "Story", "ContactName": "BT_Greeter", "ReturnName": "BT_Warden", "CompleteCount": "0", "MissionLevel": "11", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Bandit Problem", "TrackerReturn": "Make contact with the Warden of Felbridge", "Description": "Felbridge was once a great capital, but the Goblin Horde surely destroyed it. Time to take it back from those monsters.", "PreReqText": "Please go back and get rid of <PERSON><PERSON><PERSON>", "OfferText": "We're wise to your kind's tricks. Present yourself to the Warden or taste steel.", "ActiveText": "The Warden must approve all outsiders.=@You're human!=And so are you, that don't mean we're friends.=@But, how did you get here?=I was born here.=@I don't understand, the Goblin Horde…=Don't know nothing about no hordes.=You either check in with the Warden or off with you.=Although the Warden will no doubt send you on your way.=We're not looking for your kind of trouble.", "ReturnText": "We've had our fill of looters and bandits.", "PraiseText": "You behave, stranger."}, {"MissionName": "DefeatBanditCamp", "MissionID": "33", "PreReqMissions": "ClearTheBridge", "ZoneSet": "BridgeTown", "Priority": "Story", "ContactName": "BT_Warden", "ReturnName": "BT_Mayor01", "CompleteCount": "1", "Dungeon": "BT_Mission1", "MissionLevel": "11", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Bandit Problem", "TrackerText": "Expel the Bandits from their camp in the woods", "TrackerReturn": "Introduce yourself to the Steward of Felbridge", "Description": "The townsfolk shun aoutsiders, who often turn to banditry. To win their trust, you decide to tackle their bandit problem.", "PreReqText": "Please go back and get rid of <PERSON><PERSON><PERSON>", "OfferText": "Stranger, I know your type. We've no patience for you here.", "ActiveText": "I don't know you, but you'd best be on your way.=@I've come from the King.=@I'm here to help restore the Barony of Ellyria.=Your king has nothing to do with us.=And we've had plenty of bandits say the same story.=I don't want trouble, so go onto the woods with the others.=@There are human bandits in the woods?=Aye, you'll find plenty of your kind there.=@Not my kind.=So you say.=I'll believe it when I see it.", "ReturnText": "Well, stranger, I'm surprised. You've done our fair town a service."}, {"MissionName": "OldHeroesNeverDie", "MissionID": "34", "PreReqMissions": "DefeatBanditCamp", "ZoneSet": "BridgeTown", "Priority": "Story", "ContactName": "BT_Mayor01", "ReturnName": "BT_Mayor01", "CompleteCount": "1", "Dungeon": "BT_Mission2", "MissionLevel": "12", "ExpReward": "M", "GoldReward": "M", "DisplayName": "S<PERSON>gg's Last Stand", "TrackerText": "Defeat the Bandit Leader deep in the woods", "TrackerReturn": "Tell the Steward of your victory over the bandits", "Description": "Your bandit hunt leads you deeper into the forest. Here dwells the old villain <PERSON><PERSON><PERSON>, the mysterious bandit leader.", "PreReqText": "Oh, glorious gods! Just what we don't need, more outsiders…", "OfferText": "The bandits say they're soldiers from your homeland.", "ActiveText": "You claim to be from some kingdom across the sea.=@That's true. I was sent by The King.=I don't know any kings, but these other men said the same.=@I don't understand, where did YOU come from?=@I thought this town destroyed.=No, no, we're doing just fine.=Except for looters and raiders from across the sea.=Apparently from your kingdom.=@I have more questions...=Which I'll answer once you take care of your fellow bandits.=@I am no bandit!=Well, they are, and they're from your land.=@Very well, I shall see to them.", "ReturnText": "You've returned? So perhaps you aren't like the others."}, {"MissionName": "FindTheOutpost", "MissionID": "35", "PreReqMissions": "OldHeroesNeverDie", "ZoneSet": "BridgeTown,CemeteryHill", "Priority": "Story", "ContactName": "BT_Mayor01", "ReturnName": "CH_Mayor01", "CompleteCount": "0", "MissionLevel": "11", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Locate the Outpost", "TrackerReturn": "Find Captain <PERSON>ar at the outpost on Cemetery Hill", "Description": "The Steward of Felbridge offers the people of Wolf's End some land in the ill-named Cemetery Hill.", "OfferText": "I sent your friends from Wolf's End to Cemetery Hill. They can settle there if they like.", "ActiveText": "There's no room for you Wolf's End folk here.=@Where did you come from, anyway.=My people settled here 40 years ago.=We came from a far, far land, where we were slaves.=Now we live free and prosperous lives.=@But the monsters! Goblins, dragons, undead…=We protect our own.=I sent your friends from Wolf's End to Cemetery Hill.=If they're looking for new lands to settle, they can have those.=@A Cemetery?=Beggars can't be choosers.", "ReturnText": "Thank the stars you're here! <PERSON><PERSON><PERSON>'s dark magic has returned!", "PraiseText": "My father led our people out of slavery.=@Where did you come from.=Somewhere you'll never go, friend.=Somewhere terrible beyond words.=@How did you survive the dragons and goblins and…=Our safety is our concern.=We have made hard sacrifices.=But they were necessary.=I have protected my people at all costs.=Maybe you should worry about your own folk."}, {"MissionName": "TheLostGuide", "MissionID": "36", "PreReqMissions": "OutpostReport", "ZoneSet": "BridgeTown", "Priority": "Story", "ContactName": "BT_Warden", "ReturnName": "BT_SubWarden", "CompleteCount": "0", "MissionLevel": "13", "ExpReward": "L", "GoldReward": "L", "DisplayName": "The Harvest Ritual", "TrackerReturn": "Talk to the Steward about getting across the bridge.", "Description": "The <PERSON>eward knows how to get across the bridge, but he's in the woods preparing for the Harvest Ritual", "PreReqText": "You're a better class of hero=One we need badly=The dead of Cemetery Hill are rising=Bandits still plague us=And we live in the shadow of Castle Hocke=That fortress was shut to us years ago=But the evil that shut it still pervades this land", "OfferText": "The <PERSON><PERSON><PERSON> isn't here right now, stranger.", "ActiveText": "You're back.=@I need to cross the bridge into the castle.=Only The Steward knows how the magical shield works.=That shield is our main protection against our enemies.=@How did the <PERSON>ew<PERSON> create such powerful magic?=I don't know, that's his doing.=But he's kept us mostly safe for years.=@Mostly safe?=A few people disappear every year, but not much.=The Steward is gathering material for the Harvest Ritual.=Go talk to his bodyguard <PERSON><PERSON> if you want to find him.", "ReturnText": "These bandits never bothered us before. Did you rile them up?"}, {"MissionName": "MouthOfMeylour", "MissionID": "37", "PreReqMissions": "TheLostGuide", "ZoneSet": "BridgeTown", "Priority": "Story", "ContactName": "BT_SubWarden", "CompleteCount": "1", "Dungeon": "BT_Mission3", "MissionLevel": "14", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Mouth of Meylour", "TrackerText": "Talk to the <PERSON><PERSON><PERSON> inside the old cave.", "Description": "This cave may hold more than bandits. What evil lies behind their depredations? You decide to investigate.", "PreReqText": "The bandit camp was just back the other way", "OfferText": "The Steward went into that cave to prepare for the Harvest Ritual.", "ActiveText": "You shouldn't disturb the <PERSON><PERSON><PERSON>.=@Does this ritual power the shield on the bridge?=I think so.=Only the <PERSON>eward knows how it works.=@It's powerful magic.=The Steward has long protected our people, ever since we came here.=@I need to speak with him about crossing the bridge.=Well, he's in there.=But don't bother him if he seems too busy.=He's doing important work.", "PraiseText": "Did you talk with the Steward?"}, {"MissionName": "DerelictionOfDuty", "MissionID": "38", "PreReqMissions": "MouthOfMeylour", "ZoneSet": "BridgeTown", "Priority": "Story", "ContactName": "BT_Warden", "ReturnName": "BT_Warden", "CompleteCount": "1", "Dungeon": "BT_Mission4", "MissionLevel": "15", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Dereliction of Duty", "TrackerText": "Chase the Steward into the catacombs under the town", "TrackerReturn": "Consult with the Warden over the truth about <PERSON><PERSON><PERSON>", "Description": "The steward flees into the catacombs beneath the town. It's up to you to chase the villain down and stop his evil plan.", "PreReqText": "Have you seen the Steward yet.", "ActiveText": "You say the <PERSON><PERSON><PERSON> worships dark powers?=@He's been sacrificing your people to some ancient god.=I had no idea!=@There's more to be learned. Where's his house?=Right over there, off the town square.=<PERSON><PERSON>, you've got to find out who else is helping him.=The Steward, making pacts with evil gods...=I never imagined it.", "ReturnText": "With the <PERSON><PERSON>ard's evil unveiled, we have a lot to figure out.", "ISayOnAccept": "^tTime to chase down that <PERSON>ew<PERSON> and end this"}, {"MissionName": "HeadToTheMou<PERSON>ins", "MissionID": "39", "PreReqMissions": "DerelictionOfDuty", "ZoneSet": "BridgeTown,OldMineMountain", "Priority": "Story", "ContactName": "BT_Warden", "ReturnName": "OMM_Scout01", "CompleteCount": "0", "MissionLevel": "15", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Stormshard Mountain", "TrackerReturn": "Head to the Stormshard Mountains", "Description": "The Cult of Meylour took its prisoners to the Stormshard Mountains. Investigate what's going on up there.", "OfferText": "According to the <PERSON><PERSON><PERSON>'s journal, his cult took sacrifices to the mountain peak.", "ActiveText": "There's a chance the Cult left survivors on the mountain.=@Possibly. At the very least there are more cultists up there.=I'm sending some of my people to investigate.=@I will join them.=@<PERSON><PERSON><PERSON>'s cult is still keeping up that magic shield.=@I need to defeat <PERSON><PERSON><PERSON> to get across the bridge.=Then meet my people in the mountains.=But tread lightly, we don't know what's up there.", "ReturnText": "The expedition is up ahead."}, {"MissionName": "RescueYagaga", "MissionID": "40", "PreReqMissions": "JackalTreasure", "ZoneSet": "CemeteryHill", "Priority": "Story", "ContactName": "CH_Mayor01", "ReturnName": "CH_Mayor02", "CompleteCount": "1", "Dungeon": "CH_Mission1", "MissionLevel": "11", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Wither the Witch?", "TrackerText": "The witch <PERSON><PERSON><PERSON> was last seen at her mausoleum home", "TrackerReturn": "Why are the dead rising? Has <PERSON><PERSON><PERSON> returned?", "Description": "Cemertery Hill is overrun with undead. A witch named <PERSON><PERSON><PERSON> might be in league with <PERSON><PERSON><PERSON>. You decide to investigate.", "PreReqText": "You should speak to the Warden in Bridgetown", "OfferText": "The Witch <PERSON> might be in league with Nephit!", "ActiveText": "The Steward of Felbridge said Cemetery Hill would be safe to settle.=@Clearly it's not.=The dead started rising from their graves this morning!=@Sounds like <PERSON><PERSON><PERSON>'s work.=Aye, it does, but we've learned about a witch.=Her name's <PERSON><PERSON><PERSON>, and she might be working with <PERSON><PERSON><PERSON>.=We think she's holed up in a mausoleum East of here.=@Any friend of <PERSON><PERSON><PERSON>'s is an enemy of mine.", "ReturnText": "Thank you for rescuing me", "PraiseText": "The gnoles have hated humans for decades.=It's no surprise they turned to <PERSON><PERSON><PERSON> for help."}, {"MissionName": "DestroyTheIdol", "MissionID": "41", "PreReqMissions": "RescueYagaga", "ZoneSet": "CemeteryHill", "Priority": "Story", "ContactName": "CH_Mayor02", "ReturnName": "CH_Mayor02", "CompleteCount": "1", "Dungeon": "CH_Mission5", "MissionLevel": "12", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Embodiment of Evil", "TrackerText": "The Gnoles and Nep<PERSON> are raising dead in a nearby tomb", "TrackerReturn": "Tell <PERSON><PERSON><PERSON> that you've taught <PERSON><PERSON><PERSON> a lesson", "Description": "The Gnoles have made an alliance with Nephit and seek to enslave the humans of Felbridge.", "PreReqText": "The undead have completely overrun my mausoleum.", "OfferText": "The Gnoles and Undead have formed a frightening alliance.", "ActiveText": "Gnoles and humans used to be allies.=Years ago we helped each other escape slavery.=But the two sides both wanted to be in charge afterwards.=We've fought ever since.=@And now they're in league with Nephit.=They must think undead allies will let them finally enslave us again.=@They might be right.=You'll find more gnoles and more undead in the tomb of <PERSON>.", "ReturnText": "Great job! Hopefully that set back <PERSON><PERSON><PERSON><PERSON><PERSON> and the Gnoles plans."}, {"MissionName": "DefeatCrovnag", "MissionID": "42", "PreReqMissions": "DestroyTheIdol", "ZoneSet": "CemeteryHill", "Priority": "Story", "ContactName": "CH_Mayor02", "ReturnName": "CH_Mayor02", "CompleteCount": "1", "Dungeon": "CH_Mission3", "MissionLevel": "13", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Undying Vendetta", "TrackerText": "What is <PERSON><PERSON><PERSON> doing in the tomb of <PERSON>?", "TrackerReturn": "Consult with <PERSON><PERSON><PERSON> to understand <PERSON><PERSON><PERSON><PERSON>'s tale", "Description": "The undead seem to be rising from this tomb. Find out what <PERSON><PERSON><PERSON> is up to this time.", "OfferText": "I interrogated one of <PERSON><PERSON><PERSON>'s ghostly servants about the tomb of  <PERSON>.", "ActiveText": "There's a storm of undead activity in <PERSON>'s tomb.=@<PERSON><PERSON><PERSON> has a thing for old tombs, and used to work for the <PERSON><PERSON> family.=You may have defeated <PERSON><PERSON><PERSON>'s latest incarnation...=But the undead are only growing more restless.=@<PERSON><PERSON><PERSON> works his evil magic even when he has no physical form.=All signs point to <PERSON><PERSON><PERSON>'s doing something in <PERSON>'s tomb.=@Then that's my next stop.", "ReturnText": "<PERSON><PERSON><PERSON> truly is a devil, he got you to carry out his ancient vendetta for him."}, {"MissionName": "DiscoverSecret", "MissionID": "43", "PreReqMissions": "DefeatCrovnag", "ZoneSet": "CemeteryHill", "Priority": "Story", "ContactName": "CH_Mayor02", "CompleteCount": "1", "Dungeon": "CH_Mission6", "MissionLevel": "14", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Mausoleum of the Wise", "TrackerText": "Investigate the tomb in search of <PERSON><PERSON><PERSON>'s wisps", "Description": "<PERSON><PERSON><PERSON>'s wisps let him raise the dead anywhere. Another tomb seems to be on the rise.", "OfferText": "I'm worried about these Wisps N<PERSON><PERSON> is using to raise the dead.", "ActiveText": "I've never seen magic like these wisps.=There's another tomb showing activity.=@Another trick of <PERSON><PERSON><PERSON>'s or the real thing?=No way to tell without going there.=Try and learn more about the wisps while you're there.=@I'll see what I can do."}, {"MissionName": "SealTheWisps", "MissionID": "44", "PreReqMissions": "DiscoverSecret", "ZoneSet": "CemeteryHill", "Priority": "Story", "ContactName": "CH_Mayor02", "ReturnName": "CH_Scout", "CompleteCount": "1", "Dungeon": "CH_Mission7", "MissionLevel": "15", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Rising Damned", "TrackerText": "End <PERSON><PERSON><PERSON>'s plans to raise every corpse in Ellyria", "TrackerReturn": "Tell Ya<PERSON>h you've solved the mystery of the rising dead", "Description": "<PERSON><PERSON><PERSON>'s wisps are created using Crystalized Thoughts. Cut him off from the source of his power.", "ActiveText": "Have you figured out what <PERSON><PERSON><PERSON>'s scheme is?", "ReturnText": "Thank you for stopping <PERSON><PERSON><PERSON>. Hopefully things return to normal soon", "ISayOnAccept": "^tI need to seal off the wisps"}, {"MissionName": "OutpostReport", "MissionID": "45", "PreReqMissions": "De<PERSON>at<PERSON><PERSON><PERSON>", "ZoneSet": "CemeteryHill,BridgeTown", "Priority": "Story", "ContactName": "CH_Scout", "ReturnName": "BT_Warden", "CompleteCount": "0", "MissionLevel": "15", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Outpost Report", "TrackerReturn": "Return victorious to Felbridge", "Description": "You've beaten <PERSON><PERSON><PERSON> again, and secured the area for settlers from Wolf's End. Now to learn more of <PERSON><PERSON><PERSON>'s mysteries.", "OfferText": "I'm proud to have helped you reclaim this land for your people.", "ActiveText": "You can tell my old friends back in Felbridge I'm staying here with your people.", "ReturnText": "You've returned once again. Whatever you did out there, it's done something here.", "PraiseText": "Thank you for everything you have done here!"}, {"MissionName": "JackalTreasure", "MissionID": "46", "PreReqMissions": "FindTheOutpost", "ZoneSet": "CemeteryHill", "Priority": "Story", "ContactName": "CH_Mayor01", "ReturnName": "CH_Mayor01", "CompleteCount": "1", "Dungeon": "CH_Mission2", "MissionLevel": "11", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Gnole's Storehouse", "TrackerText": "Liberate the storehouse from the gnoles.", "TrackerReturn": "Tell <PERSON><PERSON><PERSON> at the Outpost that the storehouse is secure.", "Description": "Gnole raiders have a base in an old storehouse. Clear them up so the Wolf's End settlers can use it to resettle this land.", "OfferText": "There are gnoles everywhere! We need a more secure location.", "ActiveText": "The Steward didn't do us any favors sending us here.=There are gnoles and undead everywhere.=@You need a better base of operations.=There's a storehouse nearby that's solid…=But it's filled with gnoles.=@It won't be for long. I'll take care of the gnoles.", "ReturnText": "Thanks! With those gnoles gone, we'll take the storehouse over."}, {"MissionName": "<PERSON><PERSON><PERSON><PERSON>", "MissionID": "47", "PreReqMissions": "JackalTreasure", "ZoneSet": "CemeteryHill", "Priority": "Dungeon", "ContactName": "CH_Villager01", "ReturnName": "CH_Villager01", "CompleteCount": "1", "Dungeon": "CH_Mission4", "MissionLevel": "12", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Lord <PERSON><PERSON>'s Rest", "TrackerText": "Investigate the tomb of Lord <PERSON><PERSON>", "TrackerReturn": "Tell <PERSON><PERSON><PERSON> his ancestor's bones are settled", "Description": "The <PERSON><PERSON> family once ruled Ellyria. Find out what's happened in Lord <PERSON><PERSON>'s tomb.", "OfferText": "My <PERSON>y ancestors were lords here long ago, but now the dead rise in my family tomb.", "ActiveText": "I wanted to pay my ancestors my respects.=But <PERSON><PERSON><PERSON>'s foul magic has cursed my family as well as the others.=@<PERSON><PERSON><PERSON>'s evil knows no depths.=@I will restore your ancestors to their rest.", "ReturnText": "Thank you. I'm glad my family will be at peace.", "PraiseText": "Thanks again for cleansing my family tomb."}, {"MissionName": "De<PERSON>at<PERSON><PERSON><PERSON>", "MissionID": "48", "PreReqMissions": "SealTheWisps", "ZoneSet": "CemeteryHill", "Priority": "Story", "ContactName": "CH_Scout", "ReturnName": "CH_Scout", "CompleteCount": "1", "Dungeon": "CH_Mission8", "MissionLevel": "15", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Gnole's Last Stand", "TrackerText": "Defeat the Gnoles and secure the crystal mines", "TrackerReturn": "Return with the news that the Gnoles are defeated", "Description": "The Gnoles still control the last source of crystals. They need to be taken care of or <PERSON><PERSON><PERSON> could return.", "OfferText": "<PERSON><PERSON><PERSON>'s taken care of, but the Gnoles remain.", "ActiveText": "The gnole fortress is to the East.=@Without <PERSON><PERSON><PERSON>, they're less of a threat.=But they still have access to the crystal mines.=@Which means they could recreate Nephit's wisps.=Or revive <PERSON><PERSON><PERSON>.=@Either way, they need to be stopped.", "PraiseText": "The crystals are secure, no more wisps."}, {"MissionName": "RetrieveHeirlooms", "MissionID": "49", "ZoneSet": "CemeteryHill", "Priority": "Side", "ContactName": "CH_Villager02", "ReturnName": "CH_Villager02", "CompleteCount": "10", "ProgressIcon": "a_QuestIcon_Heirloom", "ProgressText": "<PERSON><PERSON><PERSON>", "MissionLevel": "13", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Retreive Heirlooms", "TrackerText": "Hunt down gnoles and retrieve their ill-gotten loot", "TrackerReturn": "Return heirlooms to Renlin and collect your reward", "Description": "Jackals are stealing from the recent dead as well as ancient tombs. Hunt down the dogs and retrieve lost heirlooms.", "OfferText": "The gnoles have strange artifacts that protect them from the undead.", "ActiveText": "If you can, bring me those strange artifacts that protect the gnoles from the undead.=If we had them, it would be much easier to safely settle this area.", "ReturnText": "My thanks! With these, we can keep the undead at bay.", "PraiseText": "Thanks again, these strange heirlooms really do drive the undead away."}, {"MissionName": "SettleTheDead", "MissionID": "50", "ZoneSet": "CemeteryHill", "Priority": "Side", "ContactName": "CH_Villager03", "ReturnName": "CH_Villager03", "CompleteCount": "15", "MissionLevel": "14", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Settle the Dead", "TrackerText": "Return these risen dead to the dirt one-by-one", "TrackerReturn": "Collect the bounty from <PERSON><PERSON><PERSON> for \"settling\" the dead", "Description": "Corpses are rising all over this Cemetery. Convince the dead to return to the dirt with some well-placed blows.", "OfferText": "What is it with the undead rising here too!", "ActiveText": "It's like Wolf's End again!=The dead are rising here too.=@I'm dealing with it, fear not.", "ReturnText": "Thank you! We are very grateful for your help", "PraiseText": "You're a worthy hero. Thanks for your help"}, {"MissionName": "ClearMini1", "MissionID": "51", "ZoneSet": "CemeteryHill", "Priority": "Side-", "CompleteCount": "1", "Dungeon": "CH_MiniMission1", "MissionLevel": "11", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Lady <PERSON>'s Tomb", "TrackerText": "Explore the tomb of Lady <PERSON>", "Description": "While freeing a land of oppression, it's good to make an occasional loot run. It's not like this fellow will miss the gold."}, {"MissionName": "ClearMini2", "MissionID": "52", "ZoneSet": "CemeteryHill", "Priority": "Side-", "CompleteCount": "1", "Dungeon": "CH_MiniMission2", "MissionLevel": "11", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Sir <PERSON>'s Tomb", "TrackerText": "Explore the tomb of Sir <PERSON>", "Description": "While freeing a land of oppression, it's good to make an occasional loot run. It's not like this fellow will miss the gold."}, {"MissionName": "ClearMini3", "MissionID": "53", "ZoneSet": "CemeteryHill", "Priority": "Side-", "CompleteCount": "1", "Dungeon": "CH_MiniMission3", "MissionLevel": "12", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Lord <PERSON>'s Tomb", "TrackerText": "Explore the tomb of Lord <PERSON>", "Description": "While freeing a land of oppression, it's good to make an occasional loot run. It's not like this fellow will miss the gold."}, {"MissionName": "ClearMini4", "MissionID": "54", "ZoneSet": "CemeteryHill", "Priority": "Side-", "CompleteCount": "1", "Dungeon": "CH_MiniMission4", "MissionLevel": "12", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Lord <PERSON>'s Tomb", "TrackerText": "Explore the tomb of Lord <PERSON>", "Description": "While freeing a land of oppression, it's good to make an occasional loot run. It's not like this fellow will miss the gold."}, {"MissionName": "ClearMini5", "MissionID": "55", "ZoneSet": "CemeteryHill", "Priority": "Side-", "CompleteCount": "1", "Dungeon": "CH_MiniMission5", "MissionLevel": "13", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Baroness <PERSON>", "TrackerText": "Explore the tomb of Baroness <PERSON>", "Description": "While freeing a land of oppression, it's good to make an occasional loot run. It's not like this fellow will miss the gold."}, {"MissionName": "ClearMini6", "MissionID": "56", "ZoneSet": "CemeteryHill", "Priority": "Side-", "CompleteCount": "1", "Dungeon": "CH_MiniMission6", "MissionLevel": "13", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Baron <PERSON>", "TrackerText": "Explore the tomb of Baron <PERSON>", "Description": "While freeing a land of oppression, it's good to make an occasional loot run. It's not like this fellow will miss the gold."}, {"MissionName": "ClearMini7", "MissionID": "57", "ZoneSet": "CemeteryHill", "Priority": "Side-", "CompleteCount": "1", "Dungeon": "CH_MiniMission7", "MissionLevel": "14", "ExpReward": "M", "GoldReward": "M", "DisplayName": "<PERSON> <PERSON><PERSON><PERSON>", "TrackerText": "Explore the tomb of Baron <PERSON><PERSON><PERSON> II", "Description": "While freeing a land of oppression, it's good to make an occasional loot run. It's not like this fellow will miss the gold."}, {"MissionName": "ClearMini8", "MissionID": "58", "ZoneSet": "CemeteryHill", "Priority": "Side-", "CompleteCount": "1", "Dungeon": "CH_MiniMission8", "MissionLevel": "14", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Sir <PERSON>", "TrackerText": "Explore the Tomb of Sir <PERSON>", "Description": "While freeing a land of oppression, it's good to make an occasional loot run. It's not like this fellow will miss the gold."}, {"MissionName": "ClearMini9", "MissionID": "59", "ZoneSet": "CemeteryHill", "Priority": "Side-", "CompleteCount": "1", "Dungeon": "CH_MiniMission9", "MissionLevel": "15", "ExpReward": "M", "GoldReward": "M", "DisplayName": "General <PERSON>", "TrackerText": "Explore the Tomb of General <PERSON>", "Description": "While freeing a land of oppression, it's good to make an occasional loot run. It's not like this fellow will miss the gold."}, {"MissionName": "HillsHaveKnives", "MissionID": "60", "PreReqMissions": "HeadToTheMou<PERSON>ins", "ZoneSet": "OldMineMountain", "Priority": "Story", "ContactName": "OMM_Scout01", "ReturnName": "OMM_Mayor01", "CompleteCount": "0", "MissionLevel": "16", "ExpReward": "L", "GoldReward": "L", "DisplayName": "The Hills Have Knives", "TrackerReturn": "Clear the road from Felbridge to the camp", "Description": "The people of Felbridge have finally ventured forth from their protected village. Help clear the path to their encampment.", "PreReqText": "Stormshard Mountains are too dangerous to go farther up", "OfferText": "Be careful, there are goblins everywhere.", "ActiveText": "There are goblins aplenty in the nearby caves.=@More goblins! I'll see to them.=Do you think they're in league with the Cult of Meylour?=@With their king killed in the war, goblins are leaderless.=@I'm sure they'll serve anyone evil enough to take them.", "ReturnText": "You made it. Good. We need an experienced goblin fighter.", "PraiseText": "Thanks for clearing the way!"}, {"MissionName": "GiveVoiceToStone", "MissionID": "61", "PreReqMissions": "HillsHaveKnives", "ZoneSet": "OldMineMountain", "Priority": "Story", "ContactName": "OMM_Mayor01", "ReturnName": "OMM_Mayor01", "CompleteCount": "1", "Dungeon": "OMM_Mission1", "MissionLevel": "16", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Give Voice to Stone", "TrackerText": "The caves of Tamjin are filled with goblins", "TrackerReturn": "Tell <PERSON><PERSON><PERSON> about the sword you removed from the statue", "Description": "Goblins have taken up residence in the tunnels nearby. Clear them out and secure the encampment.", "PreReqText": "Check in with <PERSON><PERSON><PERSON> and makes sure the road is clear.", "OfferText": "Before we search for survivors, we need to secure this area from goblins.", "ActiveText": "There's a large force of goblins in those nearby tunnels.=@I've fought my way through more than one goblin warren.=What are they doing here, I wonder.=@I bet they're desperate for allies now that they lost the war.=@The Cult of Meylour might have taken them in.=@I'll dig down through their filth and discover the truth.", "ReturnText": "The Moai have awakened!"}, {"MissionName": "StoneOraclesSpeak", "MissionID": "62", "PreReqMissions": "GiveVoiceToStone", "ZoneSet": "OldMineMountain", "Priority": "Story-", "ContactName": "OMM_Mayor01", "ReturnName": "OMM_Mayor01", "CompleteCount": "3", "MissionLevel": "16", "ExpReward": "L", "GoldReward": "L", "DisplayName": "The Stone Oracles Speak", "TrackerText": "Discover what <PERSON><PERSON><PERSON>'s plans are on the mountain", "TrackerReturn": "<PERSON><PERSON> restored, return to Tamjin to consult with <PERSON><PERSON><PERSON>", "Description": "You've given voice to the three Mo<PERSON>, now scattered. They know about <PERSON><PERSON><PERSON>'s evil plans.", "ActiveText": "Removing that sword made these statues start talking!=@Really? What are they saying?=They're ancient stone elementals, silenced by <PERSON><PERSON><PERSON>.=They say <PERSON><PERSON><PERSON>'s cult is doing terrible things to our people.=@How can I help?=Talk to the Moai.=Each one has revealed something important.=@I'll consult them right away!", "ReturnText": "Those Moai spirits really hate <PERSON><PERSON><PERSON>."}, {"MissionName": "GardenOfTheLost", "MissionID": "63", "PreReqMissions": "GiveVoiceToStone", "ZoneSet": "OldMineMountain", "Priority": "Story", "ContactName": "OMM_Moai01", "CompleteCount": "1", "Dungeon": "OMM_Mission2", "MissionLevel": "16", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Rock Hulk Garden", "TrackerText": "Rescue the Felbridge prisoners from <PERSON><PERSON><PERSON>'s cult", "Description": "The Moai tells you about some human survivors, kept in chains by <PERSON><PERSON><PERSON>'s followers.", "OfferText": "Some of your kind might yet live as they once were.", "ActiveText": "I know where some of your kind were taken.=@What are you? Some spirit?=We are the ancient protectors of this mountain.=<PERSON><PERSON><PERSON> toppled us from our thrones.=@Where are the survivors?=If they live, they're in one of <PERSON><PERSON><PERSON>'s foul shrines.=You must hurry, lest their time come.=@I'm on my way!", "PraiseText": "<PERSON><PERSON><PERSON> rose in fury from the depths five decades past."}, {"MissionName": "EyeOfTheTyrant", "MissionID": "64", "PreReqMissions": "GiveVoiceToStone", "ZoneSet": "OldMineMountain", "Priority": "Story", "ContactName": "OMM_Moai02", "CompleteCount": "1", "Dungeon": "OMM_Mission3", "MissionLevel": "16", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Eye of the Tyrant", "TrackerText": "Confront the terrible monsters that used to be human", "Description": "The Moai tells you that the true fate of the people from Felbridge lies within the Cyclops stronhold.", "OfferText": "If you would know your people's fate, you must confront them.", "ActiveText": "The Cyclopses were not always thus.=<PERSON><PERSON><PERSON> has corrupted their old forms.=They are his high priests and devoted acolytes.=Seek <PERSON><PERSON>'s secrets in their stronghold.", "PraiseText": "<PERSON><PERSON><PERSON> is ancient beyond counting and hates all living things."}, {"MissionName": "HuntedToTheEdge", "MissionID": "65", "PreReqMissions": "GiveVoiceToStone", "ZoneSet": "OldMineMountain", "Priority": "Story", "ContactName": "OMM_Moai03", "CompleteCount": "1", "Dungeon": "OMM_Mission5", "MissionLevel": "17", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Hunted to the Edge", "TrackerText": "Teach the gnoles that <PERSON><PERSON><PERSON> can't save them", "Description": "The Moai tells you that high in the hills, a tribe of Gnoles has turned to worshipping <PERSON><PERSON><PERSON>.", "OfferText": "The gnoles turn to <PERSON><PERSON><PERSON> in desparation.", "ActiveText": "The gnoles normally have no love for gods.=But they hate humans for betraying them.=@Who betrayed them?=The humans of Felbridge were once their allies.=The Steward betrayed them.=Now they turn to <PERSON><PERSON><PERSON> for revenge.=@Ironic. The Steward worshipped <PERSON><PERSON><PERSON> as well.=Evil gods corrupt all.", "PraiseText": "Me<PERSON><PERSON> is the essence of uncaring stone, anathema to all life."}, {"MissionName": "GriffinsRedoubt", "MissionID": "66", "PreReqMissions": "StoneOraclesSpeak", "ZoneSet": "OldMineMountain", "Priority": "Story", "ContactName": "OMM_Mayor01", "ReturnName": "OMM_Mayor01", "CompleteCount": "1", "Dungeon": "OMM_Mission7", "MissionLevel": "17", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Gnole's Roost", "TrackerText": "Clear the gnoles from the mountain's peak", "TrackerReturn": "Return victorious to the expedition", "Description": "The Gnole leader roosts atop the mountain. What has <PERSON><PERSON><PERSON> done to him?", "OfferText": "The Gnole chief worships <PERSON><PERSON><PERSON>  on the mountain peak.", "ActiveText": "You can finish off the gnoles for good.=But you should know…=I believe <PERSON><PERSON><PERSON> has done something strange to the gnole leader.=Gnoles who live long enough can become monstrously powerful.=@I'll try and make sure he doesn't have that much time.", "ReturnText": "<PERSON><PERSON><PERSON>'s followers are digging furiously into the mountain."}, {"MissionName": "BloodInTheVeins", "MissionID": "67", "PreReqMissions": "GriffinsRedoubt", "ZoneSet": "OldMineMountain", "Priority": "Story", "ContactName": "OMM_Mayor01", "CompleteCount": "1", "Dungeon": "OMM_Mission8", "MissionLevel": "18", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Veins of Meylour", "TrackerText": "Discover the nature of <PERSON><PERSON><PERSON>'s Great Work", "Description": "<PERSON><PERSON><PERSON>'s worshippers are digging deep into the mountain. What are they planning?", "OfferText": "The goblins and cyclopses are digging away in the mountain.", "ActiveText": "There are some abandoned mines in the area.=The goblins are expanding them…=But they don't seem to be digging for gold.=@Not looking for gold?=No, they seem to have some Great Work in mind.=@Any goblin work for <PERSON><PERSON><PERSON> is bad news.=@I'll put an end to it."}, {"MissionName": "GrahlsRebellion", "MissionID": "68", "PreReqMissions": "BloodInTheVeins", "ZoneSet": "OldMineMountain", "Priority": "Story", "ContactName": "OMM_Mayor01", "CompleteCount": "1", "Dungeon": "OMM_Mission9", "MissionLevel": "18", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Growing Flame", "TrackerText": "Find out why the goblins are digging these tunnels", "Description": "Deep in the Veins of <PERSON><PERSON><PERSON>, you search for the secret of <PERSON><PERSON><PERSON>'s Great Work.", "ActiveText": "Known as the Veins of <PERSON><PERSON><PERSON>, these mines once belonged to the Earth Titan=Now <PERSON><PERSON><PERSON><PERSON>'s devotees have seized them=They seek the magic ore Alurite, and the Heart=You will need to defeat them all to seize the artifact and activate The Sword of Meylour", "ISayOnAccept": "^tI see a path deeper into the mine"}, {"MissionName": "DragonsQuarry", "MissionID": "69", "PreReqMissions": "GrahlsRebellion", "ZoneSet": "OldMineMountain", "Priority": "Story", "ContactName": "OMM_Mayor01", "CompleteCount": "1", "Dungeon": "OMM_Mission10", "MissionLevel": "18", "ExpReward": "M", "GoldReward": "M", "DisplayName": "All Shall be Ashes", "TrackerText": "Extinquish <PERSON><PERSON><PERSON>'s flame before it consumes us all", "Description": "Deeper in the Veins of <PERSON><PERSON><PERSON>, you search for the source of <PERSON><PERSON><PERSON>'s destructive machinations.", "ActiveText": "Known as the Veins of <PERSON>yl<PERSON>, these mines once belonged to the Earth Titan=Now <PERSON><PERSON><PERSON><PERSON>'s devotees have seized them=They seek the magic ore Alumite, and the Heart=You will need to defeat them all to seize the artifact and activate The Sword of Meylour", "ISayOnAccept": "^tI see a path deeper into the mine"}, {"MissionName": "CutToTheHeart", "MissionID": "70", "PreReqMissions": "DragonsQuarry", "ZoneSet": "OldMineMountain", "Priority": "Story", "ContactName": "OMM_Mayor01", "CompleteCount": "1", "Dungeon": "OMM_Mission11", "MissionLevel": "18", "ExpReward": "M", "GoldReward": "M", "DisplayName": "<PERSON><PERSON><PERSON>'s <PERSON>bers", "TrackerText": "Find out what <PERSON><PERSON><PERSON> needs to fulfill his wrath", "Description": "In the deepest pits of the Veins of <PERSON><PERSON><PERSON> you confront <PERSON><PERSON><PERSON>'s <PERSON><PERSON>.", "ActiveText": "Known as the Veins of <PERSON>yl<PERSON>, these mines once belonged to the Earth Titan=Now <PERSON><PERSON><PERSON><PERSON>'s devotees have seized them=They seek the magic ore Alumite, and the Heart=You will need to defeat them all to seize the artifact and activate The Sword of Meylour", "ISayOnAccept": "^tI see a path deeper into the mine"}, {"MissionName": "HeadToTheGlades", "MissionID": "71", "PreReqMissions": "CutToTheHeart", "ZoneSet": "Old<PERSON>ine<PERSON><PERSON><PERSON>,EmeraldGlades", "Priority": "Story", "ContactName": "OMM_Mayor01", "ReturnName": "EG_Scout01", "CompleteCount": "0", "MissionLevel": "18", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Emerald Glades", "TrackerReturn": "Travel to the legendary Emerald Glades", "Description": "You answer the call to help save Emerald Glades from <PERSON><PERSON><PERSON>'s fiery wrath.", "ActiveText": "Head down the first path east of here to get to the Emerald Glades=Keep heading east", "ReturnText": "You must be a powerful hero to have come through the mines!", "PraiseText": "Thank you for reuniting our people!", "ISayOnAccept": "^tI think I can see the Glades from here"}, {"MissionName": "AbandonedArmory", "MissionID": "72", "ZoneSet": "OldMineMountain", "Priority": "Dungeon", "ContactName": "OMM_Statue01", "CompleteCount": "1", "Dungeon": "OMM_Mission4", "MissionLevel": "17", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Abandoned Armory", "TrackerText": "Explore the hidden armory of <PERSON><PERSON><PERSON> the Artificer", "Description": "Long ago the great artificer <PERSON><PERSON><PERSON> forged powerful weapons. His armory, hidden near Stormfalls, is haunted and shunned.", "OfferText": "<PERSON><PERSON><PERSON>'s Exquisite Creations", "ActiveText": "Closed=Trespassers Will Be Annihilated", "PraiseText": "<PERSON><PERSON><PERSON>'s Exquisite Creations=Trespassers Will Be Annihilated=Closed"}, {"MissionName": "Forgotten<PERSON><PERSON>ge", "MissionID": "73", "PreReqMissions": "AbandonedArmory", "ZoneSet": "OldMineMountain", "Priority": "Dungeon", "CompleteCount": "1", "Dungeon": "OMM_Mission6", "MissionLevel": "17", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Forgotten Forge", "TrackerText": "Find the legendary lost forge of <PERSON><PERSON><PERSON> the Artificier", "Description": "High in the Stormshard Peaks, the forge of <PERSON><PERSON><PERSON> the Artificier lies hidden. Few are bold or foolish enough to look for it.", "ISayOnAccept": "^tI wonder who this <PERSON><PERSON><PERSON> they speak of is…"}, {"MissionName": "GetHobgoblinNoserings", "MissionID": "74", "PreReqMissions": "GardenOfTheLost", "ZoneSet": "OldMineMountain", "Priority": "Side", "ContactName": "OMM_Villager01", "ReturnName": "OMM_Villager01", "CompleteCount": "5", "ProgressIcon": "a_QuestIcon_HobgoblinNoserings", "ProgressText": "Goblin Nosering", "MissionLevel": "16", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Recover More Rings", "TrackerText": "Recover stolen noserings from goblin lieutenants", "TrackerReturn": "Take the disgusting noserings to Or<PERSON> for a reward", "Description": "Wearing good steel as noserings? It must just be a goblin thing. Recover noserings from the tougher hobgoblins.", "PreReqText": "More goblins, more problems.", "OfferText": "Those goblin noserings are made from fine steel.", "ActiveText": "You can find noserings on the goblin <PERSON><PERSON><PERSON>.=I'll pay you well for them.", "ReturnText": "Thanks, I know just the guy to clean these.", "ISayOnAccept": "^tSeriously... More noserings?"}, {"MissionName": "CollectRockShards", "MissionID": "75", "PreReqMissions": "GetHobgoblinNoserings", "ZoneSet": "OldMineMountain", "Priority": "Side", "ContactName": "OMM_Villager01", "ReturnName": "OMM_Villager01", "CompleteCount": "10", "ProgressIcon": "a_QuestIcon_RockShard", "ProgressText": "Alurite", "MissionLevel": "17", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Collect Alurite", "TrackerText": "Gather the Alurite ore from defeated Rock Hulks", "TrackerReturn": "Give the Alurite to Ormos and claim your profits", "Description": "The mountains are rich in the rare ore Alurite. Ormos offers a bounty for Alurite harvested from Rock Hulks you smash.", "OfferText": "These mountains are rich in the mineral Alurite.", "ActiveText": "The strange rock creatures contain Alurite.=We can use it for enchanted weapons…=Enchanted armor…=Enchanted cookware…=Enchanted everything!=Felbridge will sparkle.=And we'll both make a profit.", "ReturnText": "Thank you! The smith in Felbridge will pay a nice price for this.", "PraiseText": "Thanks again for your help!"}, {"MissionName": "DriveAwayGnomes", "MissionID": "76", "PreReqMissions": "EyeOfTheTyrant", "ZoneSet": "OldMineMountain", "Priority": "Side", "ContactName": "OMM_Villager02", "ReturnName": "OMM_Villager02", "CompleteCount": "15", "MissionLevel": "17", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Drive Back the Gnomes", "TrackerText": "Drive the cave gnomes away wherever you find them", "TrackerReturn": "<PERSON><PERSON><PERSON> will be delighted you've driven the gnomes away", "Description": "Cave gnomes are plaguing the expedition. Driving gnomes away when you see them is a simple enough job.", "PreReqText": "Have you noticed those creepy gnomes?", "OfferText": "These Gnomes are driving me crazy!", "ActiveText": "Those pesky Gnomes hide out in the caves.=They come at night and steal our supplies.", "ReturnText": "Thank you, maybe now they will bug someone else!"}, {"MissionName": "SquashSomeSpiders", "MissionID": "77", "PreReqMissions": "DriveAwayGnomes", "ZoneSet": "OldMineMountain", "Priority": "Side", "ContactName": "OMM_Villager02", "ReturnName": "OMM_Villager02", "CompleteCount": "15", "MissionLevel": "18", "ExpReward": "M", "GoldReward": "M", "DisplayName": "<PERSON>", "TrackerText": "Squash deep spiders wherever you find them", "TrackerReturn": "Tell Amilie you have thinned the spider population", "Description": "The cursed spiders of Stormshard are the product of dark magic. Destroy them wherever you find them.", "OfferText": "Please help us with our spider problem", "ActiveText": "Seems like the spiders are everywhere!=Please help us!", "ReturnText": "Thank you, maybe now I can sleep at night", "PraiseText": "Thanks again for your help!"}, {"MissionName": "SlayCyclops", "MissionID": "78", "PreReqMissions": "HuntedToTheEdge", "ZoneSet": "OldMineMountain", "Priority": "Side", "ContactName": "OMM_Villager03", "ReturnName": "OMM_Villager03", "CompleteCount": "10", "MissionLevel": "17", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Cyclops Clash", "TrackerText": "Slay the wretched <PERSON><PERSON><PERSON><PERSON> wherever you find them", "TrackerReturn": "Tell Keenai the Cyclopses taken care of", "Description": "Cyclopses were once human, but now they belong to <PERSON><PERSON><PERSON>, body and soul. Give them a final rest.", "PreReqText": "My cousin was lost up here ten years ago.", "OfferText": "It's horrible, what <PERSON><PERSON><PERSON> did to our people.", "ActiveText": "I can't believe those cyclopses were once my neighbors.=The Steward's horror went on for decades.=Please, put their tortured souls to rest.=Only death will free them from <PERSON><PERSON><PERSON>.", "ReturnText": "I'm glad to hear they are free at last, thank you."}, {"MissionName": "GatherLionJewelry", "MissionID": "79", "PreReqMissions": "SlayCyclops", "ZoneSet": "OldMineMountain", "Priority": "Side", "ContactName": "OMM_Villager03", "ReturnName": "OMM_Villager03", "CompleteCount": "10", "ProgressIcon": "a_QuestIcon_LionJewelry", "ProgressText": "Stolen Jewelry", "MissionLevel": "17", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Recover Jewelry", "TrackerText": "Recover jewelry from the gnoles wherever you find them", "TrackerReturn": "Return the jewelry to Ke<PERSON>i and collect you bounty", "Description": "The fierce Gnoles claim the jewelry of ther victims as trophies. Hunt down <PERSON><PERSON><PERSON> and reclaim the lost treasures.", "OfferText": "TheGnoles have been stealing left and right.", "ActiveText": "The Gnoles love shiny things.=They've been stealing from us for years.", "ReturnText": "Wow, look how much you found! Thanks so much!", "PraiseText": "Thanks again for your help!"}, {"MissionName": "KingOfTheWorld", "MissionID": "80", "ZoneSet": "Achievement", "CompleteCount": "1", "Achievement": "True", "AchievementPoints": "10", "DisplayName": "King of the World", "TrackerText": "What a view from up here.", "Description": "You're the King of the World!"}, {"MissionName": "KillADragon", "MissionID": "81", "ZoneSet": "Achievement", "CompleteCount": "1", "Achievement": "True", "AchievementPoints": "10", "DisplayName": "A Real Hero", "TrackerText": "Kill your first Dragon.", "Description": "You killed a Dragon!"}, {"MissionName": "KillGoblins250", "MissionID": "82", "ZoneSet": "Achievement", "CompleteCount": "250", "Achievement": "True", "AchievementPoints": "10", "DisplayName": "Goblin Slayer", "TrackerText": "Kill 250 Goblins.", "Description": "You killed 250 Goblins!"}, {"MissionName": "TheAshenDryad", "MissionID": "83", "PreReqMissions": "HeadToTheGlades", "ZoneSet": "EmeraldGlades", "Priority": "Story", "ContactName": "EG_Scout01", "ReturnName": "EG_Mayor01", "CompleteCount": "1", "Dungeon": "EG_Mission1", "MissionLevel": "19", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Ashen Dryad", "TrackerText": "Follow the Ashen Dryad and learn what's going on here", "TrackerReturn": "Travel to the Ur-Tree and consult with Dryad Longroots", "Description": "<PERSON><PERSON><PERSON> seeks to burn the Ur-Tree. Follow his Ashen Dryad and stop their evil scheme.", "PreReqText": "<PERSON><PERSON><PERSON>'s secrets are in Stormshard Mountain.", "OfferText": "<PERSON><PERSON><PERSON>'s Ashen Dryads are besieging the Ur-Tree!", "ActiveText": "One of <PERSON><PERSON><PERSON>'s <PERSON><PERSON> fled to the East.=I take it he was running from you?=@I just came from the mountain.=@<PERSON><PERSON><PERSON> is transforming your people into monsters.=The Living Mountain has already destroyed most of my kind.=@<PERSON><PERSON><PERSON> will destroy all life if he has his way.=Follow that Ashen <PERSON>ad.=Capturing him will reveal more of <PERSON><PERSON><PERSON>'s plans.", "ReturnText": "Thank you, and thank <PERSON><PERSON> for sending you!", "PraiseText": "Thank you for helping our people!"}, {"MissionName": "OutOnALimb", "MissionID": "84", "PreReqMissions": "TheAshenDryad", "ZoneSet": "EmeraldGlades", "Priority": "Story", "ContactName": "EG_Mayor01", "ReturnName": "EG_Mayor01", "CompleteCount": "1", "Dungeon": "EG_Mission2", "MissionLevel": "19", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Out on a Limb", "TrackerText": "Defeat the traitorous Coalglow high up in the First Tree", "TrackerReturn": "Descend the Ur-Tree and tell Longroots of your success", "Description": "The fiendish <PERSON>g<PERSON> plots to burn the Ur-Tree. Pursue her into the Tree's highest branches and stop <PERSON><PERSON><PERSON>'s plan.", "PreReqText": "Have you seen <PERSON><PERSON>? She left our tree headed west", "OfferText": "<PERSON><PERSON><PERSON>'s corrupted Dryads have infested the Ur-Tree!", "ActiveText": "<PERSON><PERSON><PERSON>'s fiends drove us from the tree.=His corrupted plants and flames are everywhere.=@I won't let <PERSON><PERSON><PERSON>'s cult destroy this tree.=You must hurry, I smell smoke!", "ReturnText": "Thank you, but the tree isn't safe yet."}, {"MissionName": "RottenToTheRoots", "MissionID": "85", "PreReqMissions": "OutOnALimb", "ZoneSet": "EmeraldGlades", "Priority": "Story", "ContactName": "EG_Mayor01", "ReturnName": "EG_Mayor01", "CompleteCount": "1", "Dungeon": "EG_Mission3", "MissionLevel": "20", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Rotten to the Roots", "TrackerText": "Close the tunnels deep under the First Tree's roots", "TrackerReturn": "Consult Longroots and plan your attack on <PERSON><PERSON><PERSON>'s priests", "Description": "<PERSON><PERSON><PERSON>'s cult could attack again from the roots at any moment. Descend into the tunnels and stop them.", "OfferText": "<PERSON><PERSON><PERSON>'s abominations tunneled in from the roots.", "ActiveText": "The Priests of Meylour will just send more fire.=@Where did they come from?=They burrowed here from their temples.=@Point me towards the tunnel.=:@I'll make sure no more come come from there.", "ReturnText": "<PERSON><PERSON><PERSON> wants everything dead. Everything."}, {"MissionName": "HopeSpringsEternal", "MissionID": "86", "PreReqMissions": "RottenToTheRoots", "ZoneSet": "EmeraldGlades", "Priority": "Story", "ContactName": "EG_Mayor01", "ReturnName": "EG_Mayor01", "CompleteCount": "1", "Dungeon": "EG_Mission4", "MissionLevel": "20", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Hope Springs Eternal", "TrackerText": "Confront the Priests of Meylour at the ancient temple", "TrackerReturn": "Longroots can help you find the rest of <PERSON><PERSON><PERSON>'s priests", "Description": "Defeat the Priests of Meylour and find a way to trace the murderous mountain god to his hiding place.", "OfferText": "Head to the ancient temple and defeat the <PERSON><PERSON><PERSON>'s cultists", "ActiveText": "The priests of <PERSON><PERSON><PERSON> are preparing yet another ritual.=@They never give up.=You need to find a way to <PERSON><PERSON><PERSON>'s Heart.=@<PERSON><PERSON><PERSON>'s Heart? What's that?=It is where <PERSON><PERSON><PERSON>'s power lies.=Only his priests know its location.=@Then I will make them tell me...=@One way or another.", "ReturnText": "The Cult of <PERSON>yl<PERSON> is almost defeated!"}, {"MissionName": "DELETED2", "MissionID": "87", "ZoneSet": "TOBEDELETED"}, {"MissionName": "RefugeOfTheDamned", "MissionID": "88", "PreReqMissions": "HopeSpringsEternal", "ZoneSet": "EmeraldGlades", "Priority": "Story", "ContactName": "EG_Mayor01", "ReturnName": "EG_Mayor01", "CompleteCount": "1", "Dungeon": "EG_Mission5", "MissionLevel": "20", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Refuge of the Damned", "TrackerText": "Track the <PERSON><PERSON>our cult to the far corner of the Glades", "TrackerReturn": "Tell Longroots that the Meylour hideout is cleared", "Description": "<PERSON><PERSON><PERSON>'s priests have fled from your fury to a corner of the Glades. Make them reveal <PERSON><PERSON><PERSON>'s Heart.", "OfferText": "The last of <PERSON><PERSON><PERSON>'s cult cowers in fear.", "ActiveText": "You've almost beaten them.=@But I still don't know where The Heart of Meylour is.=@I will make these last cultists tell me.=@<PERSON><PERSON><PERSON>'s terror will come to an end.", "ReturnText": "Now you can finally strike down <PERSON><PERSON><PERSON> forever.", "PraiseText": "<PERSON><PERSON><PERSON>'s followers got what they deserved."}, {"MissionName": "GatherDarkTotems", "MissionID": "89", "ZoneSet": "EmeraldGlades", "Priority": "Side", "ContactName": "EG_Villager02", "ReturnName": "EG_Villager02", "CompleteCount": "10", "ProgressIcon": "a_QuestIcon_DarkTotem", "ProgressText": "Dark Totem", "MissionLevel": "19", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Collect Dark Totems", "TrackerText": "Seize the dark totems of the Ashen Dryad betrayers", "TrackerReturn": "Present the Dryad River with the seized dark totems", "Description": "Ashen Dryads carry dark totems to show fealty to the malicious <PERSON><PERSON><PERSON>. Defeat Dryads and seize these totems.", "OfferText": "Help us gather the Dark Totems from the Ashen Dryads", "ActiveText": "You can find them on the Ashen Dryads=We need them for a protection from fire spell.", "ReturnText": "Perfect, I'll get the protection spell started."}, {"MissionName": "GatherPriestMasks", "MissionID": "90", "PreReqMissions": "GatherDarkTotems", "ZoneSet": "EmeraldGlades", "Priority": "Side", "ContactName": "EG_Villager02", "ReturnName": "EG_Villager02", "CompleteCount": "10", "ProgressIcon": "a_QuestIcon_PriestMask", "ProgressText": "<PERSON> of <PERSON><PERSON><PERSON>", "MissionLevel": "20", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Seize Fire Priest Masks", "TrackerText": "Gather masks from defeated Priest<PERSON> of Meylour", "TrackerReturn": "Present the Dryad River with the seized masks", "Description": "<PERSON><PERSON><PERSON>'s Priests wear ceremonial masks while commiting their dark deeds. Defeat the Priests and gather their bizarre masks.", "OfferText": "We also need <PERSON><PERSON><PERSON>'s <PERSON>s for the protection spell", "ActiveText": "Please hurry, the priests attacks need to be repelled.", "ReturnText": "Just what we needed. Thank you!", "PraiseText": "We should be protected now. Thanks again!"}, {"MissionName": "<PERSON><PERSON>lade<PERSON><PERSON><PERSON>", "MissionID": "91", "ZoneSet": "EmeraldGlades", "Priority": "Side", "ContactName": "EG_Villager03", "ReturnName": "EG_Villager03", "CompleteCount": "20", "MissionLevel": "20", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Extinguish the Fire", "TrackerText": "Destroy the fiery Embers wherever you find them", "TrackerReturn": "Tell Briarbranch you've cleared many fire Embers", "Description": "The Dryads call the ravaging fire spirits that plague Emerald Glades 'Embers.' Whatever, you decide to destroy them.", "OfferText": "Can you help us put out the fires plaguing our land?", "ActiveText": "Living fire is everywhere, please destroy it.", "ReturnText": "Thank you. I can breath better already.", "PraiseText": "Hopefully our land will grow green once more!"}, {"MissionName": "DeepgardDragon", "MissionID": "92", "PreReqMissions": "MeylourFinale", "ZoneSet": "BridgeTown,Castle", "Priority": "Story", "ContactName": "BT_Warden", "ReturnName": "AC_Mayor01", "CompleteCount": "1", "Dungeon": "AC_Mission1", "MissionLevel": "21", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Castle Hocke", "TrackerText": "Enter Castle Hocke and discover what lies beneath it.", "TrackerReturn": "Speak to the survivors in their camp in the courtyard", "Description": "The mystic barrier surrounding Castle Hocke has fallen. Enter the castle and discover its secrets.", "PreReqText": "You've changed this land for the better.=But I fear our troubles have just begun.", "OfferText": "By destroying <PERSON><PERSON><PERSON>, you've left the path to the Sleeping Lands unblocked.", "ActiveText": "With <PERSON><PERSON><PERSON>'s barrier gone, the path to the castle is open.=My people came up from there decades ago.=We were fleeing a life of slavery in The Sleeping Lands.=The Dragons and Goblins kept us in chains.=There's no knowing what's there now.=<PERSON><PERSON><PERSON>'s barrier kept the Sleeping Lands locked away from this world.", "ReturnText": "Marshal <PERSON><PERSON><PERSON><PERSON><PERSON> is defeated, not just his dream this time.", "PraiseText": "You've stopped the dragons from invading again.=But who knows what else is down there."}, {"MissionName": "BattlesLostAndWon", "MissionID": "93", "PreReqMissions": "DeepgardDragon", "ZoneSet": "Castle", "Priority": "Story", "ContactName": "AC_Mayor01", "CompleteCount": "1", "Dungeon": "AC_Mission3", "MissionLevel": "21", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Battles Lost and Won", "TrackerText": "Find the <PERSON> Titus on the haunted batlefield", "Description": "Somewhere on this battlefield is the <PERSON>. He might know the secret of the Sleeping Lands.", "OfferText": "Our leader, <PERSON>, went to the Observatory looking for some way to fight the dragons.", "ActiveText": "Thank you for slaying that dragon.=@Who are you people?=We were with <PERSON><PERSON><PERSON>'s expedition.=@Like the people of Wolf's End?=Yes, the very same.=We fought through the goblins and into the castle.=<PERSON><PERSON><PERSON> left us to guard the camp.=He and his knights went down below.=They passed through the portal.=@And you've been trapped here ever since.=The <PERSON> helped us.=When the dragon legion came, he went to find a way to stop them.=You should find find him.", "PraiseText": "I still wonder what became of <PERSON><PERSON><PERSON> in the Sleeping Lands."}, {"MissionName": "AethericObservatory", "MissionID": "94", "PreReqMissions": "BattlesLostAndWon", "ZoneSet": "Castle", "Priority": "Story", "ContactName": "AC_Titus01", "ReturnName": "AC_Titus01", "CompleteCount": "1", "Dungeon": "AC_Mission4", "MissionLevel": "22", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Aetheric Observatory", "TrackerText": "Get to the top of the observatory before Nephit.", "TrackerReturn": "Consult with <PERSON> over the Aetheric Map", "Description": "<PERSON> kept the text of a powerful ritual in his observatory. Now <PERSON><PERSON><PERSON> wants is, no doubt for a very bad reason.", "PreReqText": "How is it you see me?", "ActiveText": "<PERSON><PERSON><PERSON> seeks an ancient ritual from the Aetheric Observatory.=When we were both alive, he asked for it.=I never trusted him with such power.=@What is this ritual?=It contacts terrible, ancient gods.=@Like <PERSON><PERSON><PERSON>. Why would he want that?=I don't know, but it's a bad idea.=A really, really bad idea.=Terrible idea.=@I get it. I'll stop him.", "ReturnText": "Stop <PERSON><PERSON><PERSON> from using the ritual."}, {"MissionName": "LastStand", "MissionID": "95", "PreReqMissions": "AethericObservatory", "ZoneSet": "Castle", "Priority": "Story", "ContactName": "AC_Titus01", "CompleteCount": "1", "Dungeon": "AC_Mission5", "MissionLevel": "22", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Last Stand", "TrackerText": "Stop <PERSON><PERSON><PERSON>! He fled to the battlements.", "Description": "<PERSON><PERSON><PERSON> has the ritual and is fleeing to the battlements. Stop him!", "OfferText": "<PERSON><PERSON><PERSON> materialized on the ramparts after you fought him.", "ActiveText": "I take it <PERSON><PERSON><PERSON> found the ritual?=@He has it, yes.=I don't know why, but he's going to use it.=Maybe he hopes it will restore him to life.=@Is that possible?=Maybe, but it would be a monstrous existence.=<PERSON><PERSON> now, stop him before he can find out.", "PraiseText": "<PERSON><PERSON><PERSON> went west to the Castle!"}, {"MissionName": "Capstone", "MissionID": "96", "PreReqMissions": "LastStand", "ZoneSet": "Castle", "Priority": "Story", "ContactName": "AC_Titus02", "ReturnName": "AC_Titus03", "CompleteCount": "1", "Dungeon": "AC_Mission6", "MissionLevel": "22", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Capstone", "TrackerText": "<PERSON><PERSON><PERSON> Nephit into the dungeons of Castle Hocke", "TrackerReturn": "<PERSON> of <PERSON><PERSON><PERSON>'s defeat and the broken Capstone", "Description": "Pursue the Nephit into the dungeons where the legendary <PERSON><PERSON> sits. Stop him from using the ritual", "PreReqText": "Hurry! <PERSON><PERSON><PERSON> went this way!", "ActiveText": "Beyond lies the portal to the Sleeping Lands=<PERSON>'s sages created a powerful Capstone to seal the portal.=It limits the travel between worlds.=<PERSON><PERSON><PERSON> will try and break the Capstone and use its energy for his ritual.=@What happens if he does that?=Travel between the realms will be unstoppable.=@More dragons, goblins and worse.=Assuming <PERSON><PERSON><PERSON>'s ritual doesn't kill us all.", "ReturnText": "At least, <PERSON><PERSON><PERSON> is no more.", "PraiseText": "It's coming=@What is coming?=Everything=Riches and Nightmares=Monsters and Magic=Good and Evil=It's all down there, and it's all awake now"}, {"MissionName": "EmeraldThrone", "MissionID": "97", "ZoneSet": "Castle", "Priority": "Dungeon", "ContactName": "AC_Villager04", "CompleteCount": "1", "Dungeon": "AC_Mission2", "MissionLevel": "21", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Emerald Throne", "TrackerText": "A secret door in the Throne Room leads to the treasury", "Description": "The survivors remember a secret door in the castle's Throne Room that leads the treasury. You always have time for that.", "OfferText": "There is a secret door in the throne room that leads to the cave", "ActiveText": "Some of the dragon's soldiers are holed up down there.=Be careful, they have one of <PERSON>'s golems at their command.", "PraiseText": "That was <PERSON>'s Prime <PERSON><PERSON><PERSON>.=The first of his Golem guards.=A shame to see such a wonder perverted."}, {"MissionName": "SpiritProblem", "MissionID": "98", "ZoneSet": "Castle", "Priority": "Side", "ContactName": "AC_Villager03", "ReturnName": "AC_Villager03", "CompleteCount": "30", "MissionLevel": "21", "ExpReward": "M", "GoldReward": "M", "DisplayName": "A Lizard Problem", "TrackerText": "Evict the Tuatara legionnaires that infest the castle", "TrackerReturn": "Check on <PERSON><PERSON> and the other survivors in the courtyard", "Description": "You've slain their general, but the Tuatara Legion still infests the castle. Time to clean house.", "OfferText": "So many Tuatara soldiers, can you trim their numbers?", "ActiveText": "They are all over the castle=They came from below=@I've fought their kind before.=@I'll take care of them.", "ReturnText": "Thank you, maybe we can finally rebuild here.", "PraiseText": "Thanks, I already feel safer here."}, {"MissionName": "GatherDreadMasks", "MissionID": "99", "ZoneSet": "Castle", "Priority": "Side", "ContactName": "AC_Villager05", "ReturnName": "AC_Villager05", "CompleteCount": "10", "ProgressIcon": "a_QuestIcon_DreadMask", "ProgressText": "Dread <PERSON>", "MissionLevel": "22", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Gather Dread Masks", "TrackerText": "Defeat Dread Paladins and claim their helms as prizes", "TrackerReturn": "Check on <PERSON><PERSON> and the other survivors in the courtyard", "Description": "The great Dr<PERSON>, were golems in <PERSON>'s defense. Now the Tuatara control them.", "OfferText": "The Dread Paladin masks are the secret to controlling these golems.", "ActiveText": "The dragon general took control of the Dread Paladin Golems.=They used to be <PERSON>'s private bodyguard.=Their masks are the key to controlling them.=If you bring me any you find, perhaps I can restore control.", "ReturnText": "Thanks, I will get started on deconstructing these right away.", "PraiseText": "Thanks again for gathering the masks."}, {"MissionName": "IntoTheDepths", "MissionID": "100", "PreReqMissions": "Capstone", "ZoneSet": "Castle,ShazariDesert", "Priority": "Story", "ContactName": "AC_Titus03", "ReturnName": "SD_Titus01", "CompleteCount": "0", "MissionLevel": "22", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Into the Depths", "TrackerText": "Take to portal to the Sleeping Lands", "TrackerReturn": "Speak with <PERSON> on the other side", "Description": "The seal between the Sleeping Land and this land lies broken. Unlimited danger lies below, and unlimited opportunity.", "PreReqText": "He's headed towards the Capstone!", "OfferText": "<PERSON><PERSON><PERSON> is defeated, but the Capstone is destroyed. The link to the Sleeping Lands lies open", "ActiveText": "What are the Sleeping Lands?=Everything=Riches and Nightmares=Monsters and Magic=Good and Evil=It's all down there, and it's all awake now=@Riches?=Ha, you are still an adventurer=You are going down there, aren't you?=Well, then, I will accompany you=That gate should take us there=Or else tear us to pieces=@Enough talk, let's go", "ReturnText": "Ah. You made it over and appear to be in one piece. Excellent!"}, {"MissionName": "TempleOfShadows", "MissionID": "101", "PreReqMissions": "ScarabInvasion", "ZoneSet": "ShazariDesert", "Priority": "Dungeon", "ContactName": "SD_Sage01", "ReturnName": "SD_Sage01", "CompleteCount": "1", "Dungeon": "SD_Mission1", "MissionLevel": "23", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Unearthing the Past", "TrackerText": "Discover the secrets of the Temple of Umbra", "TrackerReturn": "Reveal what you discovered in the temple", "Description": "The ancient Magi built the pyramids thousands of years ago. Many of their secrets lie buried in their tombs.", "OfferText": "These grand pyramids are not the work of our people.", "ActiveText": "The Magi who built the pyramids left this land long ago.=We have taken some of their ruins as our own.=But others remain guarded by powerful spirits.=If it's ancient knowledge you seek…=And you're daring enough to face ancient magics…=Then you should plumb the depths of the Umbral Temple to the West.", "ReturnText": "You are a survivor and a scholar. I hope you learned much of value."}, {"MissionName": "TravelToTownOne", "MissionID": "102", "PreReqMissions": "IntoTheDepths", "ZoneSet": "ShazariDesert", "Priority": "Story", "ContactName": "SD_Titus01", "ReturnName": "SD_Mayor01", "CompleteCount": "0", "MissionLevel": "23", "ExpReward": "L", "GoldReward": "L", "DisplayName": "The Sand People", "TrackerReturn": "Head to the Town of Kovah and speak with Mayor <PERSON><PERSON>", "Description": "The Town Elder in Kovah wishes to pay his respects to you and has urgent need of your assistance.", "OfferText": "Go to the near by village and meet the Town Elder.", "ActiveText": "Welcome to the Sleeping Lands, #tn#.=@It seems a wondrous place=This country is called Shazari.=For centuries the dragons ruled here.=@I bet the dragons were cruel masters.=Terrible and cruel, yes.=But the people here are free now.=Go introduce yourself to the town elder.", "ReturnText": "I hope we find some way to avoid falling under The Emperor's power."}, {"MissionName": "DELETED3", "MissionID": "103", "ZoneSet": "TOBEDELETED"}, {"MissionName": "ScarabInvasion", "MissionID": "104", "PreReqMissions": "TravelToTownOne", "ZoneSet": "ShazariDesert", "Priority": "Story", "ContactName": "SD_Mayor01", "ReturnName": "SD_Mayor01", "CompleteCount": "1", "Dungeon": "SD_Mission2", "MissionLevel": "23", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Scarab Invasion", "TrackerText": "Help <PERSON><PERSON><PERSON> reclaim its temple", "TrackerReturn": "Let the Elder know the temple is safe again", "Description": "Seelie magic has transformed the desert's insects into enormous creatures. They've overrun the town's temple.", "OfferText": "Thank you for coming, #tn#. We need your help.", "ActiveText": "Welcome to humble <PERSON><PERSON><PERSON>.=@It is an honor to meet you, Elder.=The honor is all mine.=Slayer of the dragons and Opener of the Path…=You have accomplished true wonders.=@I do what I can.=We will speak more on these matters…=But now I beg your help.=Seelie magicks plague our temple, filling it with abominations.=Centuries under dragon rule left us with no warriors to fight them.=@It will be my pleasure to take care of it, Elder.", "ReturnText": "Now our village will survive!"}, {"MissionName": "BloodAndSand", "MissionID": "105", "PreReqMissions": "GoSeeGladiator", "ZoneSet": "ShazariDesert", "Priority": "Story", "ContactName": "SD_Gladiator01", "ReturnName": "SD_Gladiator01", "CompleteCount": "1", "Dungeon": "SD_Mission3", "MissionLevel": "24", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Blood and Sand", "TrackerText": "Defeat the Pit Lord in the Shazari Arena", "TrackerReturn": "Tell Zumwalt the Pit Lord's reign is over", "Description": "The last of the dragon tyrants calls himself The Pit Lord. Bring down his slave arena and end his reign of terror.", "OfferText": "The other dragons are gone, but the foul Pit Lord remains.", "ActiveText": "Watch yourself, stranger…=The Pit Lord enslaves all who come in his grasp.=@Elder <PERSON> sent me.=@I have some experience slaying dragons.=Well, that's a rare skill indeed.=But this wily old wyrm isn't alone.=He has slaves who fight for him, which is bad enough.=Recently he got some other monsters as well.=I don't know where his reinforcements came from.=But they've got strange magicks.", "ReturnText": "The Pit Lord slain! We are a free realm at last.", "PraiseText": "I still wonder where The <PERSON> Lord got his reinforcements from.=Someone was trying to prop him up.=Someone is causing trouble for the free peoples of Shazari."}, {"MissionName": "GoblinDiplomacy", "MissionID": "106", "PreReqMissions": "GoSee<PERSON><PERSON>lin", "ZoneSet": "ShazariDesert", "Priority": "Story", "ContactName": "SD_Chief01", "ReturnName": "SD_Chief01", "CompleteCount": "1", "Dungeon": "SD_Mission4", "MissionLevel": "24", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Goblin Diplomacy", "TrackerText": "What lies beyond the Eastern Wall?", "TrackerReturn": "Tell the Goblin Elder that the Seelie are readying for war", "Description": "Beyond the Eastern Wall strange forces are mustering for war. Find out what you can about them.", "OfferText": "A new force seeks to replace the dragon legions here in Shazari.", "ActiveText": "Just when I thought we could resume normal trading.=@Goblin. What do you want?=Human! I want to be free to trade with my friends and allies.=@Well, what's stopping you?=Strange new warriors, taller than men.=I believe they might be Seelie Ogres.=@What are Seelie?=An ancient faerie race, expert in magicks.=They corrupt all they touch.=They're encamped beyond the wall to the East.=@I shall investigate...=@But you best be speaking truth, goblin.=Thank you, hero! Good luck!", "ReturnText": "So, it was <PERSON><PERSON>. We shall need the Emperor's help for sure!", "PraiseText": "Find someone from Valhaven.=We need the Emperor's aid against a foe like the Seelie.=We stand no chance!"}, {"MissionName": "AncientBurialGrounds", "MissionID": "107", "PreReqMissions": "GoblinDiplomacy", "ZoneSet": "ShazariDesert", "Priority": "Story", "ContactName": "SD_Jackal01", "ReturnName": "SD_Jackal01", "CompleteCount": "1", "Dungeon": "SD_Mission5", "MissionLevel": "25", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Ancient Unrest", "TrackerText": "Investigate the Burial Grounds", "TrackerReturn": "Tell <PERSON><PERSON><PERSON> what you discovered", "Description": "Search the ancient burial grounds for signs of The Emperor's involvement with Seelie magicks.", "OfferText": "The goblin elder sent you to find a Valhaven ambassador, right?", "ActiveText": "I'm certainly not the ambassador.=@Then what business do we have, stranger?=I believe the Emperor himself is behind the Seelie.=@That's a serious accusation.=Which is why I need proof.=@Why would the Emperor corrupt this land.=With the dragons gone, he seeks to add <PERSON><PERSON><PERSON> to his empire.=The ancient burial grounds seem the source of the trouble.=See if you find any sign of Imperial meddling.", "ReturnText": "I thought as much! The Emperor is ever pitiless and vile.", "PraiseText": "Meet me by the docks.=@Why?=So I can sneak you into Valhaven of course.=You've foiled the Emperor's plans here.=You're going to need help fighting your newest foe.=@And you're that help?=I have some very capable friends.=Come to the boat."}, {"MissionName": "AttuneTheAnchor", "MissionID": "108", "PreReqMissions": "GoblinDiplomacy", "ZoneSet": "ShazariDesert", "Priority": "Dungeon", "ContactName": "SD_Titus02", "ReturnName": "SD_Titus02", "CompleteCount": "1", "Dungeon": "SD_Mission6", "MissionLevel": "25", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Legacy of the Magi", "TrackerText": "The Shining Pyramid holds the secrets of the Magi", "TrackerReturn": "Meet up with <PERSON> to tell him what you found", "Description": "An ancient pyramid looms off in the distance. It contains the secrets of the Magi. Closely guarded secrets.", "OfferText": "The old pyramids around here might hold something of interest.", "ActiveText": "The ancient Seelie built these pyramids.=One of them remains sealed still.=I imagine there are wonders and dangers inside.=@Why do wonders always come with danger?=The Seelie were always cruel that way.", "ReturnText": "It sounds more wondrous than I imagined!", "PraiseText": "And you took care of the dangers as well.=@It was a very strange place.=Now that you've defeated the defenses…=I'm going to spend some time plumbing its secrets.=@Good luck.=@The place is too cold and harsh for me."}, {"MissionName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MissionID": "109", "PreReqMissions": "GoblinDiplomacy", "ZoneSet": "ShazariDesert", "Priority": "Side", "ContactName": "SD_Emissary02", "ReturnName": "SD_Chief01", "ActiveTarget": "SD_Emissary01", "CompleteCount": "1", "MissionLevel": "24", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Goblin Messenger", "TrackerText": "Find the second goblin messenger", "TrackerReturn": "Let the Goblin chief know you've helped him out", "Description": "The Goblin messengers fled when the <PERSON><PERSON> came. Tell them it's safe to return home.", "OfferText": "Some of my people fled West when the Seelie came.", "ActiveText": "If you see my fearful messengers in other towns…=Could you tell them it's safe to come back?=Thanks to you, my friend.=@No goblin has called me friend before.=Well, friends we are!=@I guess that's true.=@Weird but true.", "ReturnText": "My thanks! We have much less to fear now that the Seelie are defeated.", "PraiseText": "I think now we goblins can return to our peaceful ways.=@Do goblins really love peace?=Long ago we did.=I think we shall again.=@I certainly hope so."}, {"MissionName": "GatherScorpionStingers", "MissionID": "110", "PreReqMissions": "TravelToTownOne", "ZoneSet": "ShazariDesert", "Priority": "Side", "ContactName": "SD_Acolyte01", "ReturnName": "SD_Acolyte01", "CompleteCount": "10", "ProgressIcon": "a_QuestIcon_ScorpionStinger", "ProgressText": "<PERSON><PERSON><PERSON>er", "MissionLevel": "23", "ExpReward": "M", "GoldReward": "M", "DisplayName": "An Ironic Hunt", "TrackerText": "Poach Stingers for anti-venom", "TrackerReturn": "Bring the stingers back to the Acolyte", "Description": "The scorpions are the plague of this land. If the acolyte had enough of their poison, she could create an anti-venom.", "OfferText": "Even without the threat of drought, travel just isn't safe anymore", "ActiveText": "There are so many dangers on the roads these days=People are turning up with nasty stings from scarab scorpions=I can't do anything about the Outlander problem, but I could at least solve the scorpion problem=If you bring me enough stingers I could make as anti-venom", "ReturnText": "Oh excellent! This is more than enough!", "PraiseText": "This anti-venom will save a lot of lives=You look like you took quite a few stings yourself=You must be made of tougher stuff than your average traveler to still be walking around=Thank you for all you've done for us!=The Sky bless you on your journey"}, {"MissionName": "DestroyWaspHives", "MissionID": "111", "PreReqMissions": "ScarabInvasion", "ZoneSet": "ShazariDesert", "Priority": "Side", "ContactName": "SD_Acolyte02", "ReturnName": "SD_Acolyte02", "CompleteCount": "20", "MissionLevel": "24", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Heavy Hive Hittin'", "TrackerText": "Knock down a few wasp hives", "TrackerReturn": "Let the acolyte know you've thinned the Wasps", "Description": "The wasp hives are a danger to anyone that goes near them. Knock a few down to make things safer.", "OfferText": "Perhaps you could do something else for us", "ActiveText": "You see these wasp hives?=They've really gotten out of hand and the wasps attack people that get too near=The roads would be much safer if you could knock a few of them down", "ReturnText": "Great!", "PraiseText": "Looks like you took a sting or two=Let me take care of that for you=We really appreciate your sacrifice=The Sky bless you on your journey."}, {"MissionName": "CollectGoblinCharms", "MissionID": "112", "PreReqMissions": "TravelToTownOne", "ZoneSet": "ShazariDesert", "Priority": "Side", "ContactName": "SD_Slave01", "ReturnName": "SD_Slave01", "CompleteCount": "10", "ProgressIcon": "a_QuestIcon_GoblinTag", "ProgressText": "Goblin Memory Charm", "MissionLevel": "24", "ExpReward": "M", "GoldReward": "M", "DisplayName": "You Lost Your Marbles", "TrackerText": "Reclaim fallen slave charms from the outlanders", "TrackerReturn": "Return the charms to the escapee", "Description": "Most of the goblin slaves didn't survive the escape. Find their memory charms so <PERSON><PERSON> can return them to their families.", "OfferText": "Our escape plan failed. None of the goblin slaves made it out", "ActiveText": "Find their memory charms and I can take them to their families=They fought bravely, but they just weren't strong enough=Their families deserve some closure", "ReturnText": "You found them all?", "PraiseText": "I'll make sure their families know they fought bravely=I'd come to know them all in the slave pits=They are all really great guys=Thank you for what you've done"}, {"MissionName": "CollectGiantBracers", "MissionID": "113", "PreReqMissions": "TravelToTownOne", "ZoneSet": "ShazariDesert", "Priority": "Side", "ContactName": "SD_Nomad01", "ReturnName": "SD_Nomad01", "CompleteCount": "10", "ProgressIcon": "a_QuestIcon_GiantBracer", "ProgressText": "<PERSON><PERSON>", "MissionLevel": "24", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Repo Men", "TrackerText": "Gather up bracers from the Seelie", "TrackerReturn": "Bring the bracers back to Kiba Nibs", "Description": "The craftsman <PERSON><PERSON> can pay well for <PERSON><PERSON> bracers. Bring him any you find.", "OfferText": "Those Seelie bracers fascinate me", "ActiveText": "If had a bunch of those <PERSON>lie bracers...=I could break them down and start replicating them.=Smaller size of course.=But Goblins would pay well for something like those.=Bring me what you can.", "ReturnText": "Hmm. Not exactly new anymore. Ah well, here's your pay.", "PraiseText": "See?=Everyone profits!"}, {"MissionName": "CollectWormGlands", "MissionID": "114", "PreReqMissions": "ScarabInvasion", "ZoneSet": "ShazariDesert", "Priority": "Side", "ContactName": "SD_Matron01", "ReturnName": "SD_Matron01", "CompleteCount": "15", "ProgressIcon": "a_QuestIcon_SandWormGland", "ProgressText": "Sandworm Mucus Gland", "MissionLevel": "25", "ExpReward": "M", "GoldReward": "M", "DisplayName": "It's Snot a Problem", "TrackerText": "Gather Sandworm Mucus in the Ancient Burial Grounds", "TrackerReturn": "Bring the glands to the <PERSON>ron", "Description": "The <PERSON><PERSON> believes she can make an insect repellant from Sandworm Mucus. Sounds disgusting, but maybe it will work.", "OfferText": "We may be able to keep this from happening again", "ActiveText": "Bring me enough mucus glands and I can mix up an repellent=That will keep the bugs out of the temple", "ReturnText": "This will do nicely. Take this for your trouble", "PraiseText": "Wow, this stuff smells horrid=It'll scare away the bugs though, so it's worth it=The Sky bless you on your journey"}, {"MissionName": "DELETED4", "MissionID": "115", "ZoneSet": "TOBEDELETED"}, {"MissionName": "RescueAnnaHard", "MissionID": "116", "ZoneSet": "NewbieRoadHard", "Priority": "Story", "ContactName": "NR_Mayor01Hard", "ReturnName": "NR_Mayor01Hard", "CompleteCount": "1", "Dungeon": "TutorialDungeonHard", "MissionLevel": "37", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Goblin Kidnappers", "TrackerText": "Rescue Anna from the goblin cave to the east", "Description": "The goblins who were running from you kidnapped the villager <PERSON>. Chase them out of their hiding place. And did the goblins snatch that parrot, too?", "OfferText": "Thanks to hard fighters like <PERSON>, we've held our own", "ActiveText": "Who are you? Where did you come from?=@I am #tn#. The King sent me.=@But I'm surprised to find any humans still living here.= We came over with <PERSON><PERSON><PERSON> fifty years ago.= @<PERSON><PERSON><PERSON>, <PERSON>'s son!=I was a drummer boy in his army.= Part of an expedition to save his father's barony when the goblins invaded=@And you survived all this time. Amazing!= Thanks to two generations of fighters like <PERSON>.= But goblins surprised her on the beach.=@Those sea goblins were running from me. She must have gotten in their way.=@They landed east of here. Any caves in those parts?=Just over the hill there, an old smuggler's hideout.=@Then that's where she'll be. Until I rescue her of course."}, {"MissionName": "KillNephitHard", "MissionID": "117", "PreReqMissions": "GoblinRiverHard", "ZoneSet": "NewbieRoadHard", "Priority": "Story", "ContactName": "NR_QuestAnna01Hard", "ReturnName": "NR_QuestAnna02Hard", "CompleteCount": "1", "Dungeon": "GhostBossDungeonHard", "MissionLevel": "39", "ExpReward": "M", "GoldReward": "M", "DisplayName": "<PERSON><PERSON><PERSON>'s Quest", "TrackerText": "Investigate the Mysterious Nephit's activities", "TrackerReturn": "Tell <PERSON> you sent Nephit packing", "Description": "Some of the goblins now follow a mysterious master named <PERSON><PERSON><PERSON>. He has set up base in an old tomb. Pay him a visit.", "OfferText": "Someone named \"<PERSON><PERSON><PERSON>\" is trying to control the goblins and the undead.", "ActiveText": "My father read through the old reports from when we landed 50 years ago.=@And he found someone named <PERSON><PERSON><PERSON>?=Baron <PERSON> employed the wisest men in the world for his research.=<PERSON><PERSON><PERSON> was one of the best. He was excavating a tomb when the goblins invaded.=@Any idea which one?=We've seen a lot of undead coming from the Tomb of the Slumbering King.=@I'll start my search there.=Okay I'll meet you again when the dust settles.", "ReturnText": "With <PERSON><PERSON><PERSON> driven off, maybe we'll have fewer undead and goblins both."}, {"MissionName": "SlayTheDragonHard", "MissionID": "118", "PreReqMissions": "KillNephitHard", "ZoneSet": "NewbieRoadHard", "Priority": "Story", "ContactName": "NR_QuestAnna02Hard", "ReturnName": "NR_QuestAnna03Hard", "CompleteCount": "1", "Dungeon": "DreamDragonDungeonHard", "MissionLevel": "40", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Dragon's Dream", "TrackerText": "Learn the secret of the Dream Dragon's Tomb", "TrackerReturn": "Tell <PERSON> you slew the Dream Dragon", "Description": "Looks like <PERSON><PERSON><PERSON> wanted the Dream Dragon's secrets, but he was digging in the wrong place. Now's your chance to discover what he was searching for.", "OfferText": "There is another tomb. Maybe what <PERSON><PERSON><PERSON> sought is in there.", "ActiveText": "You said <PERSON><PERSON><PERSON> mentioned a \"Dream Dragon.\"=@Dragons have been gone for thousands of years.=This other tomb has been here longer than that.=<PERSON> was obsessed with these old crypts.=@<PERSON><PERSON><PERSON> might've wanted dragon bones. They're powerful artifacts.=The tomb is to the south, through the woods. If <PERSON><PERSON><PERSON> can rise a second time...=@Then I'd better get there before he does.=Clear a path and I'll follow.", "ReturnText": "A dragon! Amazing, even if it’s only a kind of dream phantom."}, {"MissionName": "GetGoblinNoseringsHard", "MissionID": "119", "ZoneSet": "NewbieRoadHard", "Priority": "Side", "ContactName": "NR_Villager02Hard", "ReturnName": "NR_Villager02Hard", "CompleteCount": "10", "ProgressIcon": "a_QuestIcon_GoblinNoserings", "ProgressText": "Goblin Nosering", "MissionLevel": "38", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Recover Rings", "TrackerText": "Defeat big goblins and recover their noserings", "TrackerReturn": "Collect the nosering bounty from <PERSON>", "Description": "Goblins attack villagers and steal metal. The tougher goblins wear it as jewelry. Get some of the villager's stuff back.", "PreReqText": "I'm on duty soon=Goblins steal anything metal=They kill what they can't take%What are they doing here?", "OfferText": "Goblins stole all our horseshoes and made noserings", "ActiveText": "The big goblins wear horseshoes as noserings.=To mock us, I think.=But we need horseshoes if we're going to rebuild our army and economy.=@Any chance to take something back from the goblins.", "ReturnText": "Thanks! I'll just get these rings cleaned off…"}, {"MissionName": "GetGoblinWandsHard", "MissionID": "120", "PreReqMissions": "GetGoblinNoseringsHard", "ZoneSet": "NewbieRoadHard", "Priority": "Side", "ContactName": "NR_Villager02Hard", "ReturnName": "NR_Villager02Hard", "CompleteCount": "20", "ProgressIcon": "a_QuestIcon_Wands", "ProgressText": "<PERSON><PERSON>", "MissionLevel": "39", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Recover <PERSON>", "TrackerText": "Confiscate wands from goblin shamans", "TrackerReturn": "Turn the wands over to <PERSON>", "Description": "The goblin shamans use their wands for evil magic. Take the mystic weapons away from them.", "OfferText": "The goblin shamans use their wands to control Death Eyes.", "ActiveText": "If you bring me the wands of goblin shamans, I can use them to drive off Death Eyes.=It will be nice not to have those evil things spying on us.=Especially at night.=While we sleep.", "ReturnText": "Thanks! I should be able to ward off any Death Eyes with these.", "PraiseText": "I hate the way those Death Eyes look at me.=Thanks again"}, {"MissionName": "DELETED5", "MissionID": "121", "ZoneSet": "TOBEDELETED"}, {"MissionName": "KillGoblinsHard", "MissionID": "122", "PreReqMissions": "GoblinRiverHard", "ZoneSet": "NewbieRoadHard", "Priority": "Side", "ContactName": "NR_Villager03Hard", "ReturnName": "NR_Villager03Hard", "CompleteCount": "60", "MissionLevel": "39", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Goblin Takedown", "TrackerText": "Drive back the goblin menace one goblin at a time", "TrackerReturn": "Tell <PERSON><PERSON><PERSON> that's a few dozen goblins down", "Description": "The goblins are still everywhere, attacking and plundering. Drive them back wherever you find them.", "OfferText": "If there were fewer goblins, we could clear the east road", "ActiveText": "Goblins keep the roads closed=I wonder about my friends in the eastern villages=@^tI could take care of their Goblin problem", "ReturnText": "Wow, the Mayor gave that homestead to the right dude|lady!", "PraiseText": "You're one tough goblin fighter=This place might be saved yet"}, {"MissionName": "RecoverMyStuffHard", "MissionID": "123", "ZoneSet": "NewbieRoadHard", "Priority": "Side", "ContactName": "NR_CartGuyHard", "ReturnName": "NR_CartGuyHard", "ActiveTarget": "GoblinThiefHard", "CompleteCount": "1", "ProgressIcon": "a_QuestIcon_GrandmasBracelet", "ProgressText": "Grandma's Bracelet", "MissionLevel": "39", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Looters", "TrackerText": "Teach the goblin raiders a lesson", "TrackerReturn": "Tell <PERSON> he just needs to clean up the goblin bits", "Description": "Goblin raiders still lurk in the woods. Slay their commander and help the villagers hold the line.", "OfferText": "There are goblin raiders holed up in that farm.", "ActiveText": "We've got the trogs cornered.=But they've got some tough commander in there.=If you take care of him, I can hold the line here.", "ReturnText": "You showed him!:We can keep them bottled up in there,: No problem.", "PraiseText": "You've got the goblins on the run.=As long as we keep up the presure…=We can reclaim this land."}, {"MissionName": "KillGraveyardSkeletonHard", "MissionID": "124", "ZoneSet": "NewbieRoadHard", "Priority": "Side", "ContactName": "NR_Hermit<PERSON>ard", "ReturnName": "NR_Hermit<PERSON>ard", "ActiveTarget": "GraveyardSkeletonHard", "CompleteCount": "1", "MissionLevel": "40", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Boneyard Monster", "TrackerText": "Help stem the undead tide", "TrackerReturn": "<PERSON> <PERSON> the shrine has settled down", "Description": "The dead are rising in the shrine behind this hill. Hop on the road through the trees to investigate.", "OfferText": "The undead just keep rising from that crypt.", "ActiveText": "The old shrine up there is where the undead first came from.=They've been coming ever since.=If you can help me destroy some of the worst of them…=We can keep them from spreading to Wolf's End.", "ReturnText": "You have my thanks.", "PraiseText": "I'll keep fighting them.=But I'll never get used to them.=Or their smell.=Or the creepy things they say.=But mostly their smell."}, {"MissionName": "StopCastoutHard", "MissionID": "125", "ZoneSet": "SwampRoadNorthHard", "Priority": "Story", "ContactName": "SRN_Mayor01Hard", "ReturnName": "SRN_Mayor01Hard", "CompleteCount": "1", "Dungeon": "SRN_Mission1Hard", "MissionLevel": "31", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Tower of the Tuatara", "TrackerText": "Drive the Tuatara from the tower east of Sark", "TrackerReturn": "Tell Abbod you've driven away the Tuatara Chiefain", "Description": "A new foe has claimed a nearby tower. Retaking this land starts with driving them out.", "OfferText": "Some new kind of monstrous warrior stands against us now.", "ActiveText": "These aren't goblins we're fighting.=@Not gobins? What are we dealing with then?=Some kind of reptile-men. They call themselves Tuatara.=@Tuatara. I've read the name in old stories. Fearsome soldiers.=That sounds like them, they're very organizard.=They've taken over a tower nearby.=We can't get past them.=@I can get past them.=I hope so.=@Me too. Only one way to find out.", "ReturnText": "You showed the Tuatara how humans fight!"}, {"MissionName": "SlayYornakHard", "MissionID": "126", "ZoneSet": "SwampRoadNorthHard", "Priority": "Dungeon", "ContactName": "DaneHard", "ReturnName": "DaneHard", "CompleteCount": "1", "Dungeon": "SRN_Mission2Hard", "MissionLevel": "32", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Mystery of the Yornak", "TrackerText": "What is The Yornak? And what treasure does it keep?", "TrackerReturn": "Tell villagers of the Yornak's defeat", "Description": "Here, deep in the swamp, lies the castle of Lord <PERSON><PERSON><PERSON>, former governor of these lands. What secrets lie buried with him?", "OfferText": "The castle of Lord <PERSON><PERSON><PERSON> seems still inhabited although not by men.", "ActiveText": "Castle Yornak was the home of this region's lords.=The last Lord and Lady <PERSON><PERSON><PERSON> built the magnificent granary, palace, and university.=@They were renowned for their wealth and lavish spending, I know.=Indeed they were, but…=They always kept their ancient castle in good repair.=It was their safe retreat.=@Maybe there are some survivors there too.=There's someone in there.=Someone the Tuatara call Yorna<PERSON>.=@Yornak! Maybe an heir? I'll go find out.", "ReturnText": "You survived Castle Yornak.", "PraiseText": "The Yornak family is another tragic tale.=@The invasions from the Sleeping Lands have corrupted everything.=@I'm glad I finally brought them peace."}, {"MissionName": "TravelToTownTwoHard", "MissionID": "127", "PreReqMissions": "SlaySvarHard", "ZoneSet": "SwampRoadNorthHard", "Priority": "Story", "ContactName": "SRN_Mayor01Hard", "ReturnName": "SRN_Mayor02Hard", "CompleteCount": "0", "MissionLevel": "32", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Deeper into the Swamp", "TrackerReturn": "Meet with <PERSON><PERSON> and see what he's found", "Description": "Travel east to where Ranger <PERSON><PERSON><PERSON> and his party have set up a foreward base. He has discovered something there.", "OfferText": "Ranger <PERSON><PERSON><PERSON> has led a party to scout the old university district.", "ActiveText": "<PERSON><PERSON><PERSON> thinks the source of the corruption is to the east.=Someone named <PERSON><PERSON><PERSON> has set up in the Citadel of Wissen.=@I wanted to poke around there myself.=@I'll go see what he has discovered.=Be careful, the road is overrung with Tuatara and Devourers.", "ReturnText": "It's good to see you again, friend.", "PraiseText": "Your legend grows.=Soon this region will follow you anywhere."}, {"MissionName": "SlayOoyakHard", "MissionID": "128", "PreReqMissions": "TravelToTownTwoHard", "ZoneSet": "SwampRoadNorthHard", "Priority": "Story", "ContactName": "SRN_Mayor02Hard", "ReturnName": "SRN_Mayor02Hard", "CompleteCount": "1", "Dungeon": "SRN_Mission4Hard", "MissionLevel": "33", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Lair of the Ooyak", "TrackerText": "Slay the Tuatara's Colossal Canisaur", "TrackerReturn": "Tell the Rangers that the Great Ooyak is dead", "Description": "The Tuatara rely on enslaved canisaurs for muscle in battle. Descend into the Ooyak's Tower end the source.", "PreReqText": "Are you friend or foe?=Do you bring message from our friends in Sark?=We cannot trust strangers", "OfferText": "The Vizier of the Tuatara is breeding carnisaurs.", "ActiveText": "The Vizier may work for the dragon generals…=But we now know he's the real power behind the corruption.=The Vizier's magical experiments are breeding carnasaurs.=@Those lizard-horse things?=He's breeding them to serve as mounts for Tuatara soldiers.=@They'd be more than a match for our knights back home.=@I need to end this monster-breeding program now.", "ReturnText": "You did it! No carnasaur knights will plague us."}, {"MissionName": "StopBroodvictorHard", "MissionID": "129", "PreReqMissions": "SlayOoyakHard", "ZoneSet": "SwampRoadNorthHard", "Priority": "Story", "ContactName": "SRN_Mayor02Hard", "ReturnName": "SRN_Mayor02Hard", "CompleteCount": "1", "Dungeon": "SRN_Mission5Hard", "MissionLevel": "34", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Citadel of the Vizier", "TrackerText": "Challenge Vizier <PERSON><PERSON><PERSON> in his lair", "TrackerReturn": "Return to the rangers with news of your victory", "Description": "Vizier <PERSON><PERSON><PERSON> is behind the foul magic that has corrupted the swamp. Time to end him and his experiments.", "OfferText": "Vizier <PERSON><PERSON><PERSON> is using the Citadel's library for evil", "ActiveText": "The Vizier will be incredibly angry at you.=@I'm even angrier at him.=@He's perverted the Citadel's wisdom for evil.=I assume you're going after him.=@I am. I'm taking back the library for all of us.=Be careful, the Vizier's magic makes him as dangerous as the dragons he serves.", "ReturnText": "The library is liberated!"}, {"MissionName": "SlaySvarHard", "MissionID": "130", "PreReqMissions": "StopCastoutHard", "ZoneSet": "SwampRoadNorthHard", "Priority": "Story", "ContactName": "SRN_Mayor01Hard", "ReturnName": "SRN_Mayor01Hard", "CompleteCount": "1", "Dungeon": "SRN_Mission3Hard", "MissionLevel": "33", "ExpReward": "M", "GoldReward": "M", "DisplayName": "<PERSON><PERSON>'s Spite", "TrackerText": "Drive the Tuatara general from the ruined keep", "TrackerReturn": "Tell the Wolf Enders that generals are dragons", "Description": "The general of the Tuatara legions has his base here. Time to introduce yourself.", "OfferText": "The Tuatara general has set up his base in the castle nearby", "ActiveText": "The Tuatara General, <PERSON><PERSON>, is based in that old castle.=@This <PERSON><PERSON> seems to be one of their important leaders.=@I'm going to take him on and learn where these reptilian soldiers come from.=What is an army doing in this swamp anyway?=@I'm not sure, but armies don't stay in one place.=@Armies this big are for attacking others.=As the nearest other, that's worrisome to me.", "ReturnText": "General <PERSON><PERSON> was a dragon!", "PraiseText": "The Tuatara are led by real, live dragons.=@They are, which isn't good news.=That means there are more dragons.=@Probably bigger ones too.=Good luck with that.=Glad it's not me in your boots."}, {"MissionName": "TravelToTownThreeHard", "MissionID": "131", "PreReqMissions": "StopBroodvictorHard", "ZoneSet": "SwampRoadNorthHard", "Priority": "Story", "ContactName": "SRN_Mayor02Hard", "ReturnName": "SRN_Mayor03Hard", "CompleteCount": "0", "MissionLevel": "33", "ExpReward": "L", "GoldReward": "L", "DisplayName": "The Last Outpost", "TrackerReturn": "Meet with <PERSON><PERSON><PERSON> to learn more", "Description": "Contact <PERSON><PERSON><PERSON> and see what he has learned about the Tuatara Legion's other general.", "OfferText": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> think they know where the Tuatara General is.", "ActiveText": "We have more soldiers scouting the mire.=<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> think they know where the other Tuatara general is.=@Another dragon?=Probably.=Go see them, they know more than I.", "ReturnText": "Glad you could make it.", "PraiseText": "The Tuatara's other general is nearby.=But he's different than <PERSON><PERSON><PERSON>.=@Different how?=We've seen signs of the undead in his service.=@Great. I sense <PERSON><PERSON><PERSON>'s hand in this."}, {"MissionName": "SlaySvathHard", "MissionID": "132", "PreReqMissions": "TravelToTownThreeHard", "ZoneSet": "SwampRoadNorthHard", "Priority": "Story", "ContactName": "SRN_Mayor03Hard", "ReturnName": "SRN_Mayor03Hard", "CompleteCount": "1", "Dungeon": "SRN_Mission7Hard", "MissionLevel": "34", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Great Green Svath", "TrackerText": "Cut off the head of this dragon invasion", "TrackerReturn": "Tell the soldiers that Tuatara Legion is defeated.", "Description": "The leader of this draconic invasion is in this castle. And it sounds like he's got some foul allies with him.", "PreReqText": "We're still scouting this area.=We'll send word when we know more.", "OfferText": "There's another General, called <PERSON><PERSON><PERSON>. He's in the old palace.", "ActiveText": "General <PERSON><PERSON><PERSON> commands all these Tuatara.=@<PERSON><PERSON><PERSON> and <PERSON><PERSON>? They must be brothers.=Probably so, but this sounds like the big brother.=And <PERSON><PERSON><PERSON> doesn't just command Tuatara soldiers.=He's into scarier stuff.=@You mean besides giant spiders and carnivorous plants?=I mean like undead.=@Great, another grave digger like <PERSON><PERSON><PERSON>.=@An invasion of dragons and ghosts would ruin anyone's week.", "ReturnText": "You stopped the dragon invasion in its tracks!"}, {"MissionName": "ClearTheBridgeHard", "MissionID": "133", "PreReqMissions": "SlaySvathHard", "ZoneSet": "SwampRoadNorthHard", "Priority": "Story", "ContactName": "SRN_Mayor03Hard", "CompleteCount": "1", "Dungeon": "SwampRoadConnectionMissionHard", "MissionLevel": "35", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Arach<PERSON>'s Swamp", "TrackerText": "Fight you way up the road to Castle Hocke", "TrackerReturn": "Fight your way to Castle Hocke", "Description": "You need to get into Castle Hocke if you want to learn the truth about the monsters overruning Ellyria.", "OfferText": "What's next for you, #tn#?", "ActiveText": "Now that the Tuatara are defeated…=What is your plan?=@I'm going to head for Castle Hocke.=@I'm sure it's all monsters and ruins now.=I can't imagine anyone survived the Tuatara Legion passing through.=@And <PERSON><PERSON><PERSON> is no doubt up to something.=@I need to get into that castle...=@If I'm ever going to learn the truth about where these monsters come from.", "ReturnText": "Hold there, stanger. Who are you?", "PraiseText": "You've made some tremendous changes in these parts."}, {"MissionName": "SlayMindlessQueenHard", "MissionID": "134", "ZoneSet": "SwampRoadNorthHard", "Priority": "Dungeon", "ContactName": "OdemHard", "ReturnName": "OdemHard", "CompleteCount": "1", "Dungeon": "SRN_Mission6Hard", "MissionLevel": "33", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Mindless Queen's Glade", "TrackerText": "Find the source of this Devourer plague", "TrackerReturn": "Return to the village to claim your reward", "Description": "This used to be the Royal Granary. Now it's the source of all those man-eating plants.", "OfferText": "The ruins of the Royal Granary is the source of the devourers.", "ActiveText": "The Royal Granary used to be a wonder of the world.=@This whole region was the breadbasket of the empire.=But now the ruined granary is corrupted.=The devourers seem to all come from there.=@Then weeding them out will be my next task.=If we can recover the grain fields, we can support a whole army.=@I just need to cut down an army of killer plants first.", "ReturnText": "You cleared the Royal Granary!"}, {"MissionName": "GetLizardBannersHard", "MissionID": "135", "ZoneSet": "SwampRoadNorthHard", "Priority": "Side", "ContactName": "IeldHard", "ReturnName": "IeldHard", "CompleteCount": "10", "ProgressIcon": "a_QuestIcon_Banner", "ProgressText": "<PERSON><PERSON>", "MissionLevel": "32", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Banners of the Tuatara", "TrackerText": "Collect the banners of the lizardmen", "TrackerReturn": "Collect the banner bounty from <PERSON><PERSON>", "Description": "A Tuatara troop becomes weak and demoralized if its banner falls. Seizing banners would deal the lizards a blow.", "OfferText": "Those lizards put a lot of stock in those banners", "ActiveText": "The Tuatara rally around their war banners.=They're ferocious when their banner is up.=@And more organized than the goblins ever were.=The banners are enchanted in some way.=If you capture some of them, I might be able to use their magic against them.=@We could use our own magic banners to retake the land!", "ReturnText": "Perfect. I think I can sew our own enchanted banners now.", "PraiseText": "Now I just need a good design.=@If you want to use my portrait, you can.=Um, thanks, I'll consider it.=@I really have an inspiring profile.=Uh, you do?= Yes, well, I'll come up with a few options."}, {"MissionName": "StopTheInvaderHard", "MissionID": "136", "ZoneSet": "SwampRoadNorthHard", "Priority": "Side", "ContactName": "PalokHard", "ReturnName": "PalokHard", "ActiveTarget": "LizardInvaderHard", "CompleteCount": "1", "MissionLevel": "34", "ExpReward": "M", "GoldReward": "M", "DisplayName": "<PERSON><PERSON>", "TrackerText": "Send the Tuatara packing", "TrackerReturn": "Tell Palok it's all clear", "Description": "A Tuatara squad has set up in an abandoned farm. You need to push them out of there.", "OfferText": "A Tuatara squad has set up in an abandoned farm.", "ActiveText": "The lizards took over this old house.=They're using it as a base to launch raids from.=They store supplies there too.=Mostly dried beetles and rancid meat.=So you can maybe leave that there.=Unless you're into that kind of food.=I'm not judging.", "ReturnText": "Thank you!", "PraiseText": "We'll keep an eye on that place.=If any more show up, we'll take care of them.=Thanks for not bringing me the dried beetles.=Although my cousin says they're tasty.=I guess I should try one before I judge."}, {"MissionName": "DroppedInTheWellHard", "MissionID": "137", "ZoneSet": "SwampRoadNorthHard", "Priority": "Side", "ContactName": "RoseHard", "ReturnName": "RoseHard", "ActiveTarget": "WellMonsterHard", "CompleteCount": "1", "MissionLevel": "33", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Overgrown Well", "TrackerText": "The well behind the thicket is overrun with Devourers", "TrackerReturn": "Tell <PERSON> she can go get a drink", "Description": "Devourers are everywhere. A nasty grove of them keeps <PERSON> from her well. You decide to lend a hand.", "OfferText": "Devourers are everywhere! I can't get to the well", "ActiveText": "Can you get the devourers away from the well?=It’s the only source of fresh water anywhere near here.=These monster have corrupted the river.=And the ponds.=They'd corrupt the rain if they could.", "ReturnText": "Whew, thanks!", "PraiseText": "Thanks for clearing the well.=We really need a reliable water source."}, {"MissionName": "SurroundedBySpidersHard", "MissionID": "138", "ZoneSet": "SwampRoadNorthHard", "Priority": "Side", "ContactName": "SughHard", "ReturnName": "SughHard", "ActiveTarget": "SpiderInvaderHard", "CompleteCount": "1", "MissionLevel": "32", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Hut Monster", "TrackerText": "Something very terrible is hiding in that hut", "TrackerReturn": "Tell <PERSON>gh you took care of the monster", "Description": "Every kind of monster seems to plague the people of Black Rose Mire. What lives in the hut behind this hill?", "OfferText": "There's something big up in that old farm.", "ActiveText": "I don't know what it is.=I don't think it's Tuatara though.=I figured you would want to check it out.=Because I really don't want to.=Did I mention it sounded big?", "ReturnText": "Ooo! I hate spiders! Thanks!", "PraiseText": "A colossal spider, it was?=I hate spiders.=I'm glad it was you and not me!"}, {"MissionName": "GetDevourerTeethHard", "MissionID": "139", "PreReqMissions": "SlayMindlessQueenHard", "ZoneSet": "SwampRoadNorthHard", "Priority": "Side", "ContactName": "OdemHard", "ReturnName": "OdemHard", "CompleteCount": "20", "ProgressIcon": "a_QuestIcon_DevourerTooth", "ProgressText": "Dev<PERSON><PERSON>", "MissionLevel": "33", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Get Devourer <PERSON>", "TrackerText": "Harvest Devourer teeth to thin out the plant beasts", "TrackerReturn": "Collect the bounty from Odem", "Description": "Now that the Queen is dead, you can thin out the Devourers and help find a way to restore the land.", "OfferText": "These murderous plants are intriguing.", "ActiveText": "We need to learn more about the Devourers.=@I destroyed the source of those foul plants.=Yes, but they grow like weeds.=If we're going to heal the land, I to know more.=Collect teeth from the big ones.=I can use them to concoct a potion to poison them.=@I think we'd all be better off if plants didn't have teeth.", "ReturnText": "Well done, thank you!", "PraiseText": "I hate to think what <PERSON><PERSON><PERSON> did to give plants teeth.=It's terrifying to contemplate.=But it's all I can think about.=What else did he give teeth to?=Mushrooms?=Jellyfish?=Rocks?"}, {"MissionName": "GetLizardGreatHelmHard", "MissionID": "140", "ZoneSet": "SwampRoadNorthHard", "Priority": "Side", "ContactName": "GranHard", "ReturnName": "GranHard", "CompleteCount": "20", "ProgressIcon": "a_QuestIcon_GreatHelm", "ProgressText": "Great Helm", "MissionLevel": "34", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Get Tuatara Great Helms", "TrackerText": "Hunt the Greater Tuatara throughout Black Rose Mire", "TrackerReturn": "Collect the Great Helm bounty from <PERSON>", "Description": "Collect helms from the largest, meanest Tuatara tand let them know that humans are taking back Black Rose Mire.", "OfferText": "Will you help us push back the Tuatara soldiers?", "ActiveText": "Bring me the Helms of the Tuatara Commanders.=@Why do you want their helms?=First, to show them we're taking over. They love their helms.=Second, we can use the metal to forge our own armor.=@Third, it'll make them so angry.=Exactly.", "ReturnText": "Thank you! Those lizards know we're not fooling around now!", "PraiseText": "A real hero on our side!=Maybe we can take the fight to these beasties.=And not just the Tuatara.=Imagine this land free of monster rule!"}, {"MissionName": "GetSpiderFangsHard", "MissionID": "141", "ZoneSet": "SwampRoadNorthHard", "Priority": "Side", "ContactName": "GrettaHard", "ReturnName": "GrettaHard", "CompleteCount": "20", "ProgressIcon": "a_QuestIcon_SpiderFang", "ProgressText": "<PERSON>", "MissionLevel": "32", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Get Spider Fangs", "TrackerText": "Seek spiders deep in the swamp to help the healer", "TrackerReturn": "Give the spider fangs to <PERSON><PERSON> and collect your bounty", "Description": "<PERSON><PERSON> is brewing an antidote to spider poison. You decide to collect spider fangs for her.", "OfferText": "My antidote for spider poison needs spider fangs", "ActiveText": "If you bring me spider fangs, I'd be able to make an antidote.=@That would definitely come in handy.=These big spiders didn't used to live here.=@They were corrupted by the some foul magic.=That's why I hate magic, always corrupting things.=I much prefer alchemy.", "ReturnText": "Thank you, now I can finish the antidote", "PraiseText": "It will take years to get rid of these spiders.=If we can ever figure out where they come from."}, {"MissionName": "SeeTheWardenHard", "MissionID": "142", "ZoneSet": "BridgeTownHard", "Priority": "Story", "ContactName": "BT_GreeterHard", "ReturnName": "BT_WardenHard", "CompleteCount": "0", "MissionLevel": "26", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Bandit Problem", "TrackerReturn": "Make contact with the Warden of Felbridge", "Description": "Felbridge was once a great capital, but the Goblin Horde surely destroyed it. Time to take it back from those monsters.", "PreReqText": "Please go back and get rid of <PERSON><PERSON><PERSON>", "OfferText": "We're wise to your kind's tricks. Present yourself to the Warden or taste steel.", "ActiveText": "The Warden must approve all outsiders.=@You're human!=And so are you, that don't mean we're friends.=@But, how did you get here?=I was born here.=@I don't understand, the Goblin Horde…=Don't know nothing about no hordes.=You either check in with the Warden or off with you.=Although the Warden will no doubt send you on your way.=We're not looking for your kind of trouble.", "ReturnText": "Halt, outsider, we've had our fill of looters and bandits.", "PraiseText": "How would we see this?"}, {"MissionName": "DefeatBanditCampHard", "MissionID": "143", "PreReqMissions": "SeeTheWardenHard", "ZoneSet": "BridgeTownHard", "Priority": "Story", "ContactName": "BT_WardenHard", "ReturnName": "BT_Mayor01Hard", "CompleteCount": "1", "Dungeon": "BT_Mission1Hard", "MissionLevel": "26", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Bandit Problem", "TrackerText": "Expel the Bandits from their camp in the woods", "TrackerReturn": "Introduce yourself to the Steward of Felbridge", "Description": "The townsfolk shun aoutsiders, who often turn to banditry. To win their trust, you decide to tackle their bandit problem.", "PreReqText": "Please go back and get rid of <PERSON><PERSON><PERSON>", "OfferText": "Stranger, I know your type. We've no patience for you here.", "ActiveText": "I don't know you, but you'd best be on your way.=@I've come from the King.=@I'm here to help restore the Barony of Ellyria.=Your king has nothing to do with us.=And we've had plenty of bandits say the same story.=I don't want trouble, so go onto the woods with the others.=@There are human bandits in the woods?=Aye, you'll find plenty of your kind there.=@Not my kind.=So you say.=I'll believe it when I see it.", "ReturnText": "Well, stranger, I'm surprised. You've done our fair town a service."}, {"MissionName": "OldHeroesNeverDieHard", "MissionID": "144", "PreReqMissions": "DefeatBanditCampHard", "ZoneSet": "BridgeTownHard", "Priority": "Story", "ContactName": "BT_Mayor01Hard", "ReturnName": "BT_Mayor01Hard", "CompleteCount": "1", "Dungeon": "BT_Mission2Hard", "MissionLevel": "27", "ExpReward": "M", "GoldReward": "M", "DisplayName": "S<PERSON>gg's Last Stand", "TrackerText": "Defeat the Bandit Leader deep in the woods", "TrackerReturn": "Tell the Steward of your victory over the bandits", "Description": "Your bandit hunt leads you deeper into the forest. Here dwells the old villain <PERSON><PERSON><PERSON>, the mysterious bandit leader.", "PreReqText": "Oh, glorious gods! Just what we don't need, more outsiders…", "OfferText": "The bandits say they're soldiers from your homeland.", "ActiveText": "You claim to be from some kingdom across the sea.=@That's true. I was sent by The King.=I don't know any kings, but these other men said the same.=@I don't understand, where did YOU come from?=@I thought this town destroyed.=No, no, we're doing just fine.=Except for looters and raiders from across the sea.=Apparently from your kingdom.=@I have more questions...=Which I'll answer once you take care of your fellow bandits.=@I am no bandit!=Well, they are, and they're from your land.=@Very well, I shall see to them.", "ReturnText": "You've returned? So perhaps you aren't like the others."}, {"MissionName": "FindTheOutpostHard", "MissionID": "145", "PreReqMissions": "OldHeroesNeverDieHard", "ZoneSet": "BridgeTownHard,CemeteryHillHard", "Priority": "Story", "ContactName": "BT_Mayor01Hard", "ReturnName": "CH_Mayor01H<PERSON>", "CompleteCount": "0", "MissionLevel": "26", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Locate the Outpost", "TrackerReturn": "Find Captain <PERSON>ar at the outpost on Cemetery Hill", "Description": "The Steward of Felbridge offers the people of Wolf's End some land in the ill-named Cemetery Hill.", "OfferText": "I sent your friends from Wolf's End to Cemetery Hill. They can settle there if they like.", "ActiveText": "There's no room for you Wolf's End folk here.=@Where did you come from, anyway.=My people settled here 40 years ago.=We came from a far, far land, where we were slaves.=Now we live free and prosperous lives.=@But the monsters! Goblins, dragons, undead…=We protect our own.=I sent your friends from Wolf's End to Cemetery Hill.=If they're looking for new lands to settle, they can have those.=@A Cemetery?=Beggars can't be choosers.", "ReturnText": "Thank the stars you're here! <PERSON><PERSON><PERSON>'s dark magic has returned!", "PraiseText": "My father led our people out of slavery.=@Where did you come from.=Somewhere you'll never go, friend.=Somewhere terrible beyond words.=@How did you survive the dragons and goblins and…=Our safety is our concern.=We have made hard sacrifices.=But they were necessary.=I have protected my people at all costs.=Maybe you should worry about your own folk."}, {"MissionName": "TheLostGuideHard", "MissionID": "146", "PreReqMissions": "OutpostReportHard", "ZoneSet": "BridgeTownHard", "Priority": "Story", "ContactName": "BT_WardenHard", "ReturnName": "BT_SubWardenHard", "CompleteCount": "0", "MissionLevel": "28", "ExpReward": "L", "GoldReward": "L", "DisplayName": "The Harvest Ritual", "TrackerReturn": "Talk to the Steward about getting across the bridge.", "Description": "The <PERSON>eward knows how to get across the bridge, but he's in the woods preparing for the Harvest Ritual", "PreReqText": "You're a better class of hero=One we need badly=The dead of Cemetery Hill are rising=Bandits still plague us=And we live in the shadow of Castle Hocke=That fortress was shut to us years ago=But the evil that shut it still pervades this land", "OfferText": "The <PERSON><PERSON><PERSON> isn't here right now, stranger.", "ActiveText": "You're back.=@I need to cross the bridge into the castle.=Only The Steward knows how the magical shield works.=That shield is our main protection against our enemies.=@How did the <PERSON>ew<PERSON> create such powerful magic?=I don't know, that's his doing.=But he's kept us mostly safe for years.=@Mostly safe?=A few people disappear every year, but not much.=The Steward is gathering material for the Harvest Ritual.=Go talk to his bodyguard <PERSON><PERSON> if you want to find him.", "ReturnText": "These bandits never bothered us before. Did you rile them up?"}, {"MissionName": "MouthOfMeylourHard", "MissionID": "147", "PreReqMissions": "TheLostGuideHard", "ZoneSet": "BridgeTownHard", "Priority": "Story", "ContactName": "BT_SubWardenHard", "CompleteCount": "1", "Dungeon": "BT_Mission3Hard", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Mouth of Meylour", "TrackerText": "Talk to the <PERSON><PERSON><PERSON> inside the old cave.", "Description": "This cave may hold more than bandits. What evil lies behind their depredations? You decide to investigate.", "PreReqText": "The bandit camp was just back the other way", "OfferText": "The Steward went into that cave to prepare for the Harvest Ritual.", "ActiveText": "You shouldn't disturb the <PERSON><PERSON><PERSON>.=@Does this ritual power the shield on the bridge?=I think so.=Only the <PERSON>eward knows how it works.=@It's powerful magic.=The Steward has long protected our people, ever since we came here.=@I need to speak with him about crossing the bridge.=Well, he's in there.=But don't bother him if he seems too busy.=He's doing important work.", "PraiseText": "Did you talk with the Steward?"}, {"MissionName": "DerelictionOfDutyHard", "MissionID": "148", "PreReqMissions": "MouthOfMeylourHard", "ZoneSet": "BridgeTownHard", "Priority": "Story", "ContactName": "BT_WardenHard", "ReturnName": "BT_WardenHard", "CompleteCount": "1", "Dungeon": "BT_Mission4Hard", "MissionLevel": "30", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Dereliction of Duty", "TrackerText": "Chase the Steward into the catacombs under the town", "TrackerReturn": "Consult with the Warden over the truth about <PERSON><PERSON><PERSON>", "Description": "The steward flees into the catacombs beneath the town. It's up to you to chase the villain down and stop his evil plan.", "PreReqText": "Have you seen the Steward yet.", "ActiveText": "You say the <PERSON><PERSON><PERSON> worships dark powers?=@He's been sacrificing your people to some ancient god.=I had no idea!=@There's more to be learned. Where's his house?=Right over there, off the town square.=<PERSON><PERSON>, you've got to find out who else is helping him.=The Steward, making pacts with evil gods...=I never imagined it.", "ReturnText": "With the <PERSON><PERSON>ard's evil unveiled, we have a lot to figure out.", "ISayOnAccept": "^tTime to chase down that <PERSON>ew<PERSON> and end this"}, {"MissionName": "HeadToTheMountainsHard", "MissionID": "149", "PreReqMissions": "DerelictionOfDutyHard", "ZoneSet": "BridgeTownHard,OldMineMountainHard", "Priority": "Story", "ContactName": "BT_WardenHard", "ReturnName": "OMM_Scout01Hard", "CompleteCount": "0", "MissionLevel": "30", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Stormshard Mountain", "TrackerReturn": "Head to the Stormshard Mountains", "Description": "The Cult of Meylour took its prisoners to the Stormshard Mountains. Investigate what's going on up there.", "OfferText": "According to the <PERSON><PERSON><PERSON>'s journal, his cult took sacrifices to the mountain peak.", "ActiveText": "There's a chance the Cult left survivors on the mountain.=@Possibly. At the very least there are more cultists up there.=I'm sending some of my people to investigate.=@I will join them.=@<PERSON><PERSON><PERSON>'s cult is still keeping up that magic shield.=@I need to defeat <PERSON><PERSON><PERSON> to get across the bridge.=Then meet my people in the mountains.=But tread lightly, we don't know what's up there.", "ReturnText": "The rest of the expedition is futher up the mountain."}, {"MissionName": "RescueYagagaHard", "MissionID": "150", "PreReqMissions": "JackalTreasureHard", "ZoneSet": "CemeteryHillHard", "Priority": "Story", "ContactName": "CH_Mayor01H<PERSON>", "ReturnName": "CH_Mayor02Hard", "CompleteCount": "1", "Dungeon": "CH_Mission1Hard", "MissionLevel": "26", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Wither the Witch?", "TrackerText": "The witch <PERSON><PERSON><PERSON> was last seen at her mausoleum home", "TrackerReturn": "Why are the dead rising? Has <PERSON><PERSON><PERSON> returned?", "Description": "Cemertery Hill is overrun with undead. A witch named <PERSON><PERSON><PERSON> might be in league with <PERSON><PERSON><PERSON>. You decide to investigate.", "PreReqText": "You should speak to the Warden in Bridgetown", "OfferText": "The Witch <PERSON> might be in league with Nephit!", "ActiveText": "The Steward of Felbridge said Cemetery Hill would be safe to settle.=@Clearly it's not.=The dead started rising from their graves this morning!=@Sounds like <PERSON><PERSON><PERSON>'s work.=Aye, it does, but we've learned about a witch.=Her name's <PERSON><PERSON><PERSON>, and she might be working with <PERSON><PERSON><PERSON>.=We think she's holed up in a mausoleum East of here.=@Any friend of <PERSON><PERSON><PERSON>'s is an enemy of mine.", "ReturnText": "Thank you for rescuing me", "PraiseText": "The gnoles have hated humans for decades.=It's no surprise they turned to <PERSON><PERSON><PERSON> for help."}, {"MissionName": "DestroyTheIdolHard", "MissionID": "151", "PreReqMissions": "RescueYagagaHard", "ZoneSet": "CemeteryHillHard", "Priority": "Story", "ContactName": "CH_Mayor02Hard", "ReturnName": "CH_Mayor02Hard", "CompleteCount": "1", "Dungeon": "CH_Mission5Hard", "MissionLevel": "27", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Embodiment of Evil", "TrackerText": "The Gnoles and Nep<PERSON> are raising dead in a nearby tomb", "TrackerReturn": "Tell <PERSON><PERSON><PERSON> that you've taught <PERSON><PERSON><PERSON> a lesson", "Description": "The Gnoles have made an alliance with Nephit and seek to enslave the humans of Felbridge.", "PreReqText": "The undead have completely overrun my mausoleum.", "OfferText": "The Gnoles and Undead have formed a frightening alliance.", "ActiveText": "Gnoles and humans used to be allies.=Years ago we helped each other escape slavery.=But the two sides both wanted to be in charge afterwards.=We've fought ever since.=@And now they're in league with Nephit.=They must think undead allies will let them finally enslave us again.=@They might be right.=You'll find more gnoles and more undead in the tomb of <PERSON>.", "ReturnText": "Great job! Hopefully that set back <PERSON><PERSON><PERSON><PERSON><PERSON> and the Gnoles plans."}, {"MissionName": "DefeatCrovnagHard", "MissionID": "152", "PreReqMissions": "DestroyTheIdolHard", "ZoneSet": "CemeteryHillHard", "Priority": "Story", "ContactName": "CH_Mayor02Hard", "ReturnName": "CH_Mayor02Hard", "CompleteCount": "1", "Dungeon": "CH_Mission3Hard", "MissionLevel": "28", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Undying Vendetta", "TrackerText": "What is <PERSON><PERSON><PERSON> doing in the tomb of <PERSON>?", "TrackerReturn": "Consult with <PERSON><PERSON><PERSON> to understand <PERSON><PERSON><PERSON><PERSON>'s tale", "Description": "The undead seem to be rising from this tomb. Find out what <PERSON><PERSON><PERSON> is up to this time.", "OfferText": "I interrogated one of <PERSON><PERSON><PERSON>'s ghostly servants about the tomb of  <PERSON>.", "ActiveText": "There's a storm of undead activity in <PERSON>'s tomb.=@<PERSON><PERSON><PERSON> has a thing for old tombs, and used to work for the <PERSON><PERSON> family.=You may have defeated <PERSON><PERSON><PERSON>'s latest incarnation...=But the undead are only growing more restless.=@<PERSON><PERSON><PERSON> works his evil magic even when he has no physical form.=All signs point to <PERSON><PERSON><PERSON>'s doing something in <PERSON>'s tomb.=@Then that's my next stop.", "ReturnText": "<PERSON><PERSON><PERSON> truly is a devil, he got you to carry out his ancient vendetta for him."}, {"MissionName": "DiscoverSecretHard", "MissionID": "153", "PreReqMissions": "DefeatCrovnagHard", "ZoneSet": "CemeteryHillHard", "Priority": "Story", "ContactName": "CH_Mayor02Hard", "CompleteCount": "1", "Dungeon": "CH_Mission6Hard", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Mausoleum of the Wise", "TrackerText": "Investigate the Mausoleum in search of <PERSON><PERSON><PERSON>'s wisps", "Description": "<PERSON><PERSON><PERSON>'s wisps let him raise the dead anywhere. Another tomb seems to be on the rise.", "OfferText": "I'm worried about these Wisps N<PERSON><PERSON> is using to raise the dead.", "ActiveText": "I've never seen magic like these wisps.=There's another tomb showing activity.=@Another trick of <PERSON><PERSON><PERSON>'s or the real thing?=No way to tell without going there.=Try and learn more about the wisps while you're there.=@I'll see what I can do."}, {"MissionName": "SealTheWispsHard", "MissionID": "154", "PreReqMissions": "DiscoverSecretHard", "ZoneSet": "CemeteryHillHard", "Priority": "Story", "ContactName": "CH_Mayor02Hard", "ReturnName": "CH_ScoutHard", "CompleteCount": "1", "Dungeon": "CH_Mission7Hard", "MissionLevel": "30", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Rising Damned", "TrackerText": "End <PERSON><PERSON><PERSON>'s plans to raise every corpse in Ellyria", "TrackerReturn": "Tell Ya<PERSON>h you've solved the mystery of the rising dead", "Description": "<PERSON><PERSON><PERSON>'s wisps are created using Crystalized Thoughts. Cut him off from the source of his power.", "ActiveText": "Have you figured out what <PERSON><PERSON><PERSON>'s scheme is?", "ReturnText": "Thank you for stopping <PERSON><PERSON><PERSON>. Hopefully things return to normal soon", "ISayOnAccept": "^tI need to seal off the wisps"}, {"MissionName": "OutpostReportHard", "MissionID": "155", "PreReqMissions": "DefeatDogfatherHard", "ZoneSet": "CemeteryHillHard,BridgeTownHard", "Priority": "Story", "ContactName": "CH_ScoutHard", "ReturnName": "BT_WardenHard", "CompleteCount": "0", "MissionLevel": "30", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Outpost Report", "TrackerReturn": "Return victorious to the Felbridge", "Description": "You've beaten <PERSON><PERSON><PERSON> again, and secured the area for settlers from Wolf's End. Now to learn more of <PERSON><PERSON><PERSON>'s mysteries.", "OfferText": "I'm proud to have helped you reclaim this land for your people.", "ActiveText": "You can tell my old friends back in Felbridge I'm staying here with your people.", "ReturnText": "You've returned once again. Whatever you did out there, it's done something here.", "PraiseText": "Thank you for everything you have done here!"}, {"MissionName": "JackalTreasureHard", "MissionID": "156", "PreReqMissions": "FindTheOutpostHard", "ZoneSet": "CemeteryHillHard", "Priority": "Story", "ContactName": "CH_Mayor01H<PERSON>", "ReturnName": "CH_Mayor01H<PERSON>", "CompleteCount": "1", "Dungeon": "CH_Mission2Hard", "MissionLevel": "26", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Base of Operations", "TrackerText": "Liberate the storehouse from the gnoles.", "TrackerReturn": "Tell <PERSON><PERSON><PERSON> at the Outpost that the storehouse is secure.", "Description": "Gnole raiders have a base in an old storehouse. Clear them up so the Wolf's End settlers can use it to resettle this land.", "OfferText": "There are gnoles everywhere! We need a more secure location.", "ActiveText": "The Steward didn't do us any favors sending us here.=There are gnoles and undead everywhere.=@You need a better base of operations.=There's a storehouse nearby that's solid…=But it's filled with gnoles.=@It won't be for long. I'll take care of the gnoles.", "ReturnText": "Thanks! With those gnoles gone, we'll take the storehouse over."}, {"MissionName": "MissingPappyHard", "MissionID": "157", "PreReqMissions": "JackalTreasureHard", "ZoneSet": "CemeteryHillHard", "Priority": "Dungeon", "ContactName": "CH_Villager01Hard", "ReturnName": "CH_Villager01Hard", "CompleteCount": "1", "Dungeon": "CH_Mission4Hard", "MissionLevel": "27", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Lord <PERSON><PERSON>'s Rest", "TrackerText": "Investigate the tomb of Lord <PERSON><PERSON>", "TrackerReturn": "Tell <PERSON><PERSON><PERSON> his ancestor's bones are settled", "Description": "The <PERSON><PERSON> family once ruled Ellyria. Find out what's happened in Lord <PERSON><PERSON>'s tomb.", "OfferText": "My <PERSON>y ancestors were lords here long ago, but now the dead rise in my family tomb.", "ActiveText": "I wanted to pay my ancestors my respects.=But <PERSON><PERSON><PERSON>'s foul magic has cursed my family as well as the others.=@<PERSON><PERSON><PERSON>'s evil knows no depths.=@I will restore your ancestors to their rest.", "ReturnText": "Thank you. I'm glad my family will be at peace.", "PraiseText": "Thanks again for cleansing my family tomb."}, {"MissionName": "DefeatDogfatherHard", "MissionID": "158", "PreReqMissions": "SealTheWispsHard", "ZoneSet": "CemeteryHillHard", "Priority": "Story", "ContactName": "CH_ScoutHard", "ReturnName": "CH_ScoutHard", "CompleteCount": "1", "Dungeon": "CH_Mission8Hard", "MissionLevel": "30", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Gnole Fortress", "TrackerText": "Defeat the Gnoles and secure the crystal mines", "TrackerReturn": "Return with the news that the Gnoles are defeated", "Description": "The Gnoles still control the last source of crystals. They need to be taken care of or <PERSON><PERSON><PERSON> could return.", "OfferText": "<PERSON><PERSON><PERSON>'s taken care of, but the Gnoles remain.", "ActiveText": "The gnole fortress is to the East.=@Without <PERSON><PERSON><PERSON>, they're less of a threat.=But they still have access to the crystal mines.=@Which means they could recreate Nephit's wisps.=Or revive <PERSON><PERSON><PERSON>.=@Either way, they need to be stopped.", "PraiseText": "The crystals are secure, no more wisps."}, {"MissionName": "RetrieveHeirloomsHard", "MissionID": "159", "ZoneSet": "CemeteryHillHard", "Priority": "Side", "ContactName": "CH_Villager02Hard", "ReturnName": "CH_Villager02Hard", "CompleteCount": "20", "ProgressIcon": "a_QuestIcon_Heirloom", "ProgressText": "<PERSON><PERSON><PERSON>", "MissionLevel": "28", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Retreive Heirlooms", "TrackerText": "Hunt down gnoles and retrieve their ill-gotten loot", "TrackerReturn": "Return heirlooms to Renlin and collect your reward", "Description": "Jackals are stealing from the recent dead as well as ancient tombs. Hunt down the dogs and retrieve lost heirlooms.", "OfferText": "The gnoles have strange artifacts that protect them from the undead.", "ActiveText": "If you can, bring me those strange artifacts that protect the gnoles from the undead.=If we had them, it would be much easier to safely settle this area.", "ReturnText": "My thanks! With these, we can keep the undead at bay.", "PraiseText": "Thanks again, these strange heirlooms really do drive the undead away."}, {"MissionName": "SettleTheDeadHard", "MissionID": "160", "ZoneSet": "CemeteryHillHard", "Priority": "Side", "ContactName": "CH_Villager03Hard", "ReturnName": "CH_Villager03Hard", "CompleteCount": "30", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Settle the Dead", "TrackerText": "Return these risen dead to the dirt one-by-one", "TrackerReturn": "Collect the bounty from <PERSON><PERSON><PERSON> for \"settling\" the dead", "Description": "Corpses are rising all over this Cemetery. Convince the dead to return to the dirt with some well-placed blows.", "OfferText": "What is it with the undead rising here too!", "ActiveText": "It's like Wolf's End again!=The dead are rising here too.=@I'm dealing with it, fear not.", "ReturnText": "Thank you! We are very grateful for your help", "PraiseText": "You're a worthy hero. Thanks for your help"}, {"MissionName": "ClearMini1Hard", "MissionID": "161", "ZoneSet": "CemeteryHillHard", "Priority": "Side-", "CompleteCount": "1", "Dungeon": "CH_MiniMission1Hard", "MissionLevel": "26", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Lady <PERSON>'s Tomb", "TrackerText": "Explore the tomb of Lady <PERSON>", "Description": "While freeing a land of oppression, it's good to make an occasional loot run. It's not like this fellow will miss the gold."}, {"MissionName": "ClearMini2Hard", "MissionID": "162", "ZoneSet": "CemeteryHillHard", "Priority": "Side-", "CompleteCount": "1", "Dungeon": "CH_MiniMission2Hard", "MissionLevel": "26", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Sir <PERSON>'s Tomb", "TrackerText": "Explore the tomb of Sir <PERSON>", "Description": "While freeing a land of oppression, it's good to make an occasional loot run. It's not like this fellow will miss the gold."}, {"MissionName": "ClearMini3Hard", "MissionID": "163", "ZoneSet": "CemeteryHillHard", "Priority": "Side-", "CompleteCount": "1", "Dungeon": "CH_MiniMission3Hard", "MissionLevel": "27", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Lord <PERSON>'s Tomb", "TrackerText": "Explore the tomb of Lord <PERSON>", "Description": "While freeing a land of oppression, it's good to make an occasional loot run. It's not like this fellow will miss the gold."}, {"MissionName": "ClearMini4Hard", "MissionID": "164", "ZoneSet": "CemeteryHillHard", "Priority": "Side-", "CompleteCount": "1", "Dungeon": "CH_MiniMission4Hard", "MissionLevel": "27", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Lord <PERSON>'s Tomb", "TrackerText": "Explore the tomb of Lord <PERSON>", "Description": "While freeing a land of oppression, it's good to make an occasional loot run. It's not like this fellow will miss the gold."}, {"MissionName": "ClearMini5Hard", "MissionID": "165", "ZoneSet": "CemeteryHillHard", "Priority": "Side-", "CompleteCount": "1", "Dungeon": "CH_MiniMission5Hard", "MissionLevel": "28", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Baroness <PERSON>", "TrackerText": "Explore the tomb of Baroness <PERSON>", "Description": "While freeing a land of oppression, it's good to make an occasional loot run. It's not like this fellow will miss the gold."}, {"MissionName": "ClearMini6Hard", "MissionID": "166", "ZoneSet": "CemeteryHillHard", "Priority": "Side-", "CompleteCount": "1", "Dungeon": "CH_MiniMission6Hard", "MissionLevel": "28", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Baron <PERSON>", "TrackerText": "Explore the tomb of Baron <PERSON>", "Description": "While freeing a land of oppression, it's good to make an occasional loot run. It's not like this fellow will miss the gold."}, {"MissionName": "ClearMini7Hard", "MissionID": "167", "ZoneSet": "CemeteryHillHard", "Priority": "Side-", "CompleteCount": "1", "Dungeon": "CH_MiniMission7Hard", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "<PERSON> <PERSON><PERSON><PERSON>", "TrackerText": "Explore the tomb of Baron <PERSON><PERSON><PERSON> II", "Description": "While freeing a land of oppression, it's good to make an occasional loot run. It's not like this fellow will miss the gold."}, {"MissionName": "ClearMini8Hard", "MissionID": "168", "ZoneSet": "CemeteryHillHard", "Priority": "Side-", "CompleteCount": "1", "Dungeon": "CH_MiniMission8Hard", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Sir <PERSON>", "TrackerText": "Explore the Tomb of Sir <PERSON>", "Description": "While freeing a land of oppression, it's good to make an occasional loot run. It's not like this fellow will miss the gold."}, {"MissionName": "ClearMini9Hard", "MissionID": "169", "ZoneSet": "CemeteryHillHard", "Priority": "Side-", "CompleteCount": "1", "Dungeon": "CH_MiniMission9Hard", "MissionLevel": "30", "ExpReward": "M", "GoldReward": "M", "DisplayName": "General <PERSON>", "TrackerText": "Explore the Tomb of General <PERSON>", "Description": "While freeing a land of oppression, it's good to make an occasional loot run. It's not like this fellow will miss the gold."}, {"MissionName": "HillsHaveKnivesHard", "MissionID": "170", "PreReqMissions": "HeadToTheMountainsHard", "ZoneSet": "OldMineMountainHard", "Priority": "Story", "ContactName": "OMM_Scout01Hard", "ReturnName": "OMM_Mayor01Hard", "CompleteCount": "0", "MissionLevel": "31", "ExpReward": "L", "GoldReward": "L", "DisplayName": "The Hills Have Knives", "TrackerReturn": "Clear the road from Felbridge to the camp", "Description": "The people of Felbridge have finally ventured forth from their protected village. Help clear the path to their encampment.", "PreReqText": "Stormshard Mountains are too dangerous to go farther up", "OfferText": "Be careful, there are goblins everywhere.", "ActiveText": "There are goblins aplenty in the nearby caves.=@More goblins! I'll see to them.=Do you think they're in league with the Cult of Meylour?=@With their king killed in the war, goblins are leaderless.=@I'm sure they'll serve anyone evil enough to take them.", "ReturnText": "Good, you made it through the goblins.", "PraiseText": "Thanks for clearing the way!"}, {"MissionName": "GiveVoiceToStoneHard", "MissionID": "171", "PreReqMissions": "HillsHaveKnivesHard", "ZoneSet": "OldMineMountainHard", "Priority": "Story", "ContactName": "OMM_Mayor01Hard", "ReturnName": "OMM_Mayor01Hard", "CompleteCount": "1", "Dungeon": "OMM_Mission1Hard", "MissionLevel": "31", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Give Voice to Stone", "TrackerText": "The caves of Tamjin are filled with goblins", "TrackerReturn": "Tell <PERSON><PERSON><PERSON> about the sword you removed from the statue", "Description": "Goblins have taken up residence in the tunnels nearby. Clear them out and secure the encampment.", "PreReqText": "Check in with <PERSON><PERSON><PERSON> and makes sure the road is clear.", "OfferText": "Before we search for survivors, we need to secure this area from goblins.", "ActiveText": "There's a large force of goblins in those nearby tunnels.=@I've fought my way through more than one goblin warren.=What are they doing here, I wonder.=@I bet they're desperate for allies now that they lost the war.=@The Cult of Meylour might have taken them in.=@I'll dig down through their filth and discover the truth.", "ReturnText": "The Moai have awakened!"}, {"MissionName": "StoneOraclesSpeakHard", "MissionID": "172", "PreReqMissions": "GiveVoiceToStoneHard", "ZoneSet": "OldMineMountainHard", "Priority": "Story-", "ContactName": "OMM_Mayor01Hard", "ReturnName": "OMM_Mayor01Hard", "CompleteCount": "3", "MissionLevel": "31", "ExpReward": "L", "GoldReward": "L", "DisplayName": "The Stone Oracles Speak", "TrackerText": "Discover what <PERSON><PERSON><PERSON>'s plans are on the mountain", "TrackerReturn": "<PERSON><PERSON> restored, return to Tamjin to consult with <PERSON><PERSON><PERSON>", "Description": "You've given voice to the three Mo<PERSON>, now scattered. They know about <PERSON><PERSON><PERSON>'s evil plans.", "ActiveText": "Removing that sword made these statues start talking!=@Really? What are they saying?=They're ancient stone elementals, silenced by <PERSON><PERSON><PERSON>.=They say <PERSON><PERSON><PERSON>'s cult is doing terrible things to our people.=@How can I help?=Talk to the Moai.=Each one has revealed something important.=@I'll consult them right away!", "ReturnText": "Those Moai spirits really hate <PERSON><PERSON><PERSON>."}, {"MissionName": "GardenOfTheLostHard", "MissionID": "173", "PreReqMissions": "GiveVoiceToStoneHard", "ZoneSet": "OldMineMountainHard", "Priority": "Story", "ContactName": "OMM_Moai01Hard", "CompleteCount": "1", "Dungeon": "OMM_Mission2Hard", "MissionLevel": "31", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Rock Hulk Garden", "TrackerText": "Rescue the Felbridge prisoners from <PERSON><PERSON><PERSON>'s cult", "Description": "The Moai tells you about some human survivors, kept in chains by <PERSON><PERSON><PERSON>'s followers.", "OfferText": "Some of your kind might yet live as they once were.", "ActiveText": "I know where some of your kind were taken.=@What are you? Some spirit?=We are the ancient protectors of this mountain.=<PERSON><PERSON><PERSON> toppled us from our thrones.=@Where are the survivors?=If they live, they're in one of <PERSON><PERSON><PERSON>'s foul shrines.=You must hurry, lest their time come.=@I'm on my way!", "PraiseText": "<PERSON><PERSON><PERSON> rose in fury from the depths five decades past."}, {"MissionName": "EyeOfTheTyrantHard", "MissionID": "174", "PreReqMissions": "GiveVoiceToStoneHard", "ZoneSet": "OldMineMountainHard", "Priority": "Story", "ContactName": "OMM_Moai02Hard", "CompleteCount": "1", "Dungeon": "OMM_Mission3Hard", "MissionLevel": "31", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Eye of the Tyrant", "TrackerText": "Confront the terrible monsters that used to be human", "Description": "The Moai tells you that the true fate of the people from Felbridge lies within the Cyclops stronhold.", "OfferText": "If you would know your people's fate, you must confront them.", "ActiveText": "The Cyclopses were not always thus.=<PERSON><PERSON><PERSON> has corrupted their old forms.=They are his high priests and devoted acolytes.=Seek <PERSON><PERSON>'s secrets in their stronghold.", "PraiseText": "<PERSON><PERSON><PERSON> is ancient beyond counting and hates all living things."}, {"MissionName": "HuntedToTheEdgeHard", "MissionID": "175", "PreReqMissions": "GiveVoiceToStoneHard", "ZoneSet": "OldMineMountainHard", "Priority": "Story", "ContactName": "OMM_Moai03Hard", "CompleteCount": "1", "Dungeon": "OMM_Mission5Hard", "MissionLevel": "32", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Hunted to the Edge", "TrackerText": "Teach the gnoles that <PERSON><PERSON><PERSON> can't save them", "Description": "The Moai tells you that high in the hills, a tribe of Gnoles has turned to worshipping <PERSON><PERSON><PERSON>.", "OfferText": "The gnoles turn to <PERSON><PERSON><PERSON> in desparation.", "ActiveText": "The gnoles normally have no love for gods.=But they hate humans for betraying them.=@Who betrayed them?=The humans of Felbridge were once their allies.=The Steward betrayed them.=Now they turn to <PERSON><PERSON><PERSON> for revenge.=@Ironic. The Steward worshipped <PERSON><PERSON><PERSON> as well.=Evil gods corrupt all.", "PraiseText": "Me<PERSON><PERSON> is the essence of uncaring stone, anathema to all life."}, {"MissionName": "GriffinsRedoubtHard", "MissionID": "176", "PreReqMissions": "StoneOraclesSpeakHard", "ZoneSet": "OldMineMountainHard", "Priority": "Story", "ContactName": "OMM_Mayor01Hard", "ReturnName": "OMM_Mayor01Hard", "CompleteCount": "1", "Dungeon": "OMM_Mission7Hard", "MissionLevel": "32", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Gnole's Roost", "TrackerText": "Clear the gnoles from the mountain's peak", "TrackerReturn": "Return victorious to the expedition", "Description": "The Gnole leader roosts atop the mountain. What has <PERSON><PERSON><PERSON> done to him?", "OfferText": "The Gnole chief worships <PERSON><PERSON><PERSON>  on the mountain peak.", "ActiveText": "You can finish off the gnoles for good.=But you should know…=I believe <PERSON><PERSON><PERSON> has done something strange to the gnole leader.=Gnoles who live long enough can become monstrously powerful.=@I'll try and make sure he doesn't have that much time.", "ReturnText": "<PERSON><PERSON><PERSON>'s followers are digging furiously into the mountain."}, {"MissionName": "BloodInTheVeinsHard", "MissionID": "177", "PreReqMissions": "GriffinsRedoubtHard", "ZoneSet": "OldMineMountainHard", "Priority": "Story", "ContactName": "OMM_Mayor01Hard", "CompleteCount": "1", "Dungeon": "OMM_Mission8Hard", "MissionLevel": "33", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Veins of Meylour", "TrackerText": "Discover the nature of <PERSON><PERSON><PERSON>'s Great Work", "Description": "<PERSON><PERSON><PERSON>'s worshippers are digging deep into the mountain. What are they planning?", "OfferText": "The goblins and cyclopses are digging away in the mountain.", "ActiveText": "There are some abandoned mines in the area.=The goblins are expanding them…=But they don't seem to be digging for gold.=@Not looking for gold?=No, they seem to have some Great Work in mind.=@Any goblin work for <PERSON><PERSON><PERSON> is bad news.=@I'll put an end to it."}, {"MissionName": "GrahlsRebellionHard", "MissionID": "178", "PreReqMissions": "BloodInTheVeinsHard", "ZoneSet": "OldMineMountainHard", "Priority": "Story", "ContactName": "OMM_Mayor01Hard", "CompleteCount": "1", "Dungeon": "OMM_Mission9Hard", "MissionLevel": "33", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Growing Flame", "TrackerText": "Find out why the goblins are digging these tunnels", "Description": "Deep in the Veins of <PERSON><PERSON><PERSON>, you search for the secret of <PERSON><PERSON><PERSON>'s Great Work.", "ActiveText": "Known as the Veins of <PERSON><PERSON><PERSON>, these mines once belonged to the Earth Titan=Now <PERSON><PERSON><PERSON><PERSON>'s devotees have seized them=They seek the magic ore Alurite, and the Heart=You will need to defeat them all to seize the artifact and activate The Sword of Meylour", "ISayOnAccept": "^tI see a path deeper into the mine"}, {"MissionName": "DragonsQuarryHard", "MissionID": "179", "PreReqMissions": "GrahlsRebellionHard", "ZoneSet": "OldMineMountainHard", "Priority": "Story", "ContactName": "OMM_Mayor01Hard", "CompleteCount": "1", "Dungeon": "OMM_Mission10Hard", "MissionLevel": "33", "ExpReward": "M", "GoldReward": "M", "DisplayName": "All Shall be Ashes", "TrackerText": "Extinquish <PERSON><PERSON><PERSON>'s flame before it consumes us all", "Description": "Deeper in the Veins of <PERSON><PERSON><PERSON>, you search for the source of <PERSON><PERSON><PERSON>'s destructive machinations.", "ActiveText": "Known as the Veins of <PERSON>yl<PERSON>, these mines once belonged to the Earth Titan=Now <PERSON><PERSON><PERSON><PERSON>'s devotees have seized them=They seek the magic ore Alumite, and the Heart=You will need to defeat them all to seize the artifact and activate The Sword of Meylour", "ISayOnAccept": "^tI see a path deeper into the mine"}, {"MissionName": "CutToTheHeartHard", "MissionID": "180", "PreReqMissions": "DragonsQuarryHard", "ZoneSet": "OldMineMountainHard", "Priority": "Story", "ContactName": "OMM_Mayor01Hard", "CompleteCount": "1", "Dungeon": "OMM_Mission11Hard", "MissionLevel": "33", "ExpReward": "M", "GoldReward": "M", "DisplayName": "<PERSON><PERSON><PERSON>'s <PERSON>bers", "TrackerText": "Find out what <PERSON><PERSON><PERSON> needs to fulfill his wrath", "Description": "In the deepest pits of the Veins of <PERSON><PERSON><PERSON> you confront <PERSON><PERSON><PERSON>'s <PERSON><PERSON>.", "ActiveText": "Known as the Veins of <PERSON>yl<PERSON>, these mines once belonged to the Earth Titan=Now <PERSON><PERSON><PERSON><PERSON>'s devotees have seized them=They seek the magic ore Alumite, and the Heart=You will need to defeat them all to seize the artifact and activate The Sword of Meylour", "ISayOnAccept": "^tI see a path deeper into the mine"}, {"MissionName": "HeadToTheGladesHard", "MissionID": "181", "PreReqMissions": "CutToTheHeartHard", "ZoneSet": "OldMineMountainHard,EmeraldGladesHard", "Priority": "Story", "ContactName": "OMM_Mayor01Hard", "ReturnName": "EG_Scout01Hard", "CompleteCount": "0", "MissionLevel": "33", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Emerald Glades", "TrackerReturn": "Travel to the legendary Emerald Glades", "Description": "You answer the call to help save Emerald Glades from <PERSON><PERSON><PERSON>'s fiery wrath.", "ActiveText": "Head down the first path east of here to get to the Emerald Glades=Keep heading east", "ReturnText": "You must be a powerful hero to have come through the mines!", "PraiseText": "Thank you for reuniting our people!", "ISayOnAccept": "^tI think I can see the Glades from here"}, {"MissionName": "AbandonedArmoryHard", "MissionID": "182", "ZoneSet": "OldMineMountainHard", "Priority": "Dungeon", "ContactName": "OMM_Statue01Hard", "CompleteCount": "1", "Dungeon": "OMM_Mission4Hard", "MissionLevel": "32", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Abandoned Armory", "TrackerText": "Explore the hidden armory of <PERSON><PERSON><PERSON> the Artificer", "Description": "Long ago the great artificer <PERSON><PERSON><PERSON> forged powerful weapons. His armory, hidden near Stormfalls, is haunted and shunned.", "OfferText": "<PERSON><PERSON><PERSON>'s Exquisite Creations", "ActiveText": "Closed=Trespassers Will Be Annihilated", "PraiseText": "<PERSON><PERSON><PERSON>'s Exquisite Creations=Trespassers Will Be Annihilated=Closed"}, {"MissionName": "ForgottenForgeHard", "MissionID": "183", "PreReqMissions": "AbandonedArmoryHard", "ZoneSet": "OldMineMountainHard", "Priority": "Dungeon", "CompleteCount": "1", "Dungeon": "OMM_Mission6Hard", "MissionLevel": "32", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Forgotten Forge", "TrackerText": "Find the legendary lost forge of <PERSON><PERSON><PERSON> the Artificier", "Description": "High in the Stormshard Peaks, the forge of <PERSON><PERSON><PERSON> the Artificier lies hidden. Few are bold or foolish enough to look for it.", "ISayOnAccept": "^tI wonder who this <PERSON><PERSON><PERSON> they speak of is…"}, {"MissionName": "GetHobgoblinNoseringsHard", "MissionID": "184", "PreReqMissions": "GardenOfTheLostHard", "ZoneSet": "OldMineMountainHard", "Priority": "Side", "ContactName": "OMM_Villager01Hard", "ReturnName": "OMM_Villager01Hard", "CompleteCount": "10", "ProgressIcon": "a_QuestIcon_HobgoblinNoserings", "ProgressText": "Goblin Nosering", "MissionLevel": "31", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Recover More Rings", "TrackerText": "Recover stolen noserings from goblin lieutenants", "TrackerReturn": "Take the disgusting noserings to Or<PERSON> for a reward", "Description": "Wearing good steel as noserings? It must just be a goblin thing. Recover noserings from the tougher hobgoblins.", "PreReqText": "More goblins, more problems.", "OfferText": "Those goblin noserings are made from fine steel.", "ActiveText": "You can find noserings on the goblin <PERSON><PERSON><PERSON>.=I'll pay you well for them.", "ReturnText": "Thanks, I know just the guy to clean these.", "ISayOnAccept": "^tSeriously... More noserings?"}, {"MissionName": "CollectRockShardsHard", "MissionID": "185", "PreReqMissions": "GetHobgoblinNoseringsHard", "ZoneSet": "OldMineMountainHard", "Priority": "Side", "ContactName": "OMM_Villager01Hard", "ReturnName": "OMM_Villager01Hard", "CompleteCount": "20", "ProgressIcon": "a_QuestIcon_RockShard", "ProgressText": "Alurite", "MissionLevel": "32", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Collect Alurite", "TrackerText": "Gather the Alurite ore from defeated Rock Hulks", "TrackerReturn": "Give the Alurite to Ormos and claim your profits", "Description": "The mountains are rich in the rare ore Alurite. Ormos offers a bounty for Alurite harvested from Rock Hulks you smash.", "OfferText": "These mountains are rich in the mineral Alurite.", "ActiveText": "The strange rock creatures contain Alurite.=We can use it for enchanted weapons…=Enchanted armor…=Enchanted cookware…=Enchanted everything!=Felbridge will sparkle.=And we'll both make a profit.", "ReturnText": "Thank you! The smith in Felbridge will pay a nice price for this.", "PraiseText": "Thanks again for your help!"}, {"MissionName": "DriveAwayGnomesHard", "MissionID": "186", "PreReqMissions": "EyeOfTheTyrantHard", "ZoneSet": "OldMineMountainHard", "Priority": "Side", "ContactName": "OMM_Villager02Hard", "ReturnName": "OMM_Villager02Hard", "CompleteCount": "30", "MissionLevel": "32", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Drive Back the Gnomes", "TrackerText": "Drive the cave gnomes away wherever you find them", "TrackerReturn": "<PERSON><PERSON><PERSON> will be delighted you've driven the gnomes away", "Description": "Cave gnomes are plaguing the expedition. Driving gnomes away when you see them is a simple enough job.", "PreReqText": "Have you noticed those creepy gnomes?", "OfferText": "These Gnomes are driving me crazy!", "ActiveText": "Those pesky Gnomes hide out in the caves.=They come at night and steal our supplies.", "ReturnText": "Thank you, maybe now they will bug someone else!"}, {"MissionName": "SquashSomeSpidersHard", "MissionID": "187", "PreReqMissions": "DriveAwayGnomesHard", "ZoneSet": "OldMineMountainHard", "Priority": "Side", "ContactName": "OMM_Villager02Hard", "ReturnName": "OMM_Villager02Hard", "CompleteCount": "30", "MissionLevel": "33", "ExpReward": "M", "GoldReward": "M", "DisplayName": "<PERSON>", "TrackerText": "Squash deep spiders wherever you find them", "TrackerReturn": "Tell Amilie you have thinned the spider population", "Description": "The cursed spiders of Stormshard are the product dark magic. Destroy them wherever you find them.", "OfferText": "Please help us with our spider problem", "ActiveText": "Seems like the spiders are everywhere!=Please help us!", "ReturnText": "Thank you, maybe now I can sleep at night.", "PraiseText": "Thanks again for your help!"}, {"MissionName": "SlayCyclopsHard", "MissionID": "188", "PreReqMissions": "HuntedToTheEdgeHard", "ZoneSet": "OldMineMountainHard", "Priority": "Side", "ContactName": "OMM_Villager03Hard", "ReturnName": "OMM_Villager03Hard", "CompleteCount": "20", "MissionLevel": "32", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Cyclops Clash", "TrackerText": "Slay the wretched <PERSON><PERSON><PERSON><PERSON> wherever you find them", "TrackerReturn": "Tell Keenai the Cyclopses taken care of", "Description": "Cyclopses were once human, but now they belong to <PERSON><PERSON><PERSON>, body and soul. Give them a final rest.", "PreReqText": "My cousin was lost up here 10 years ago.", "OfferText": "It's horrible, what <PERSON><PERSON><PERSON> did to our people.", "ActiveText": "I can't believe those cyclopses were once my neighbors.=The Steward's horror went on for decades.=Please, put their tortured souls to rest.=Only death will free them from <PERSON><PERSON><PERSON>.", "ReturnText": "I'm glad to hear they are free at last, thank you."}, {"MissionName": "GatherLionJewelryHard", "MissionID": "189", "PreReqMissions": "SlayCyclopsHard", "ZoneSet": "OldMineMountainHard", "Priority": "Side", "ContactName": "OMM_Villager03Hard", "ReturnName": "OMM_Villager03Hard", "CompleteCount": "20", "ProgressIcon": "a_QuestIcon_LionJewelry", "ProgressText": "Stolen Jewelry", "MissionLevel": "32", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Recover Jewelry", "TrackerText": "Recover jewelry from the gnoles wherever you find them", "TrackerReturn": "Return the jewelry to Ke<PERSON>i and collect you bounty", "Description": "The fierce Gnoles claim the jewelry of ther victims as trophies. Hunt down <PERSON><PERSON><PERSON> and reclaim the lost treasures.", "OfferText": "TheGnoles have been stealing left and right.", "ActiveText": "The Gnoles love shiny things.=They've been stealing from us for years.", "ReturnText": "Wow, look how much you found! Thanks so much!", "PraiseText": "Thanks again for your help!"}, {"MissionName": "TheAshenDryadHard", "MissionID": "190", "PreReqMissions": "HeadToTheGladesHard", "ZoneSet": "EmeraldGladesHard", "Priority": "Story", "ContactName": "EG_Scout01Hard", "ReturnName": "EG_Mayor01Hard", "CompleteCount": "1", "Dungeon": "EG_Mission1Hard", "MissionLevel": "34", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Ashen Dryad", "TrackerText": "Follow the Ashen Dryad and learn what's going on here", "TrackerReturn": "Travel to the Ur-Tree and consult with Dryad Longroots", "Description": "<PERSON><PERSON><PERSON> seeks to burn the Ur-Tree. Follow his Ashen Dryad and stop their evil scheme.", "PreReqText": "<PERSON><PERSON><PERSON>'s secrets are in Stormshard Mountain.", "OfferText": "<PERSON><PERSON><PERSON>'s Ashen Dryads are besieging the Ur-Tree!", "ActiveText": "One of <PERSON><PERSON><PERSON>'s <PERSON><PERSON> fled to the East.=I take it he was running from you?=@I just came from the mountain.=@<PERSON><PERSON><PERSON> is transforming your people into monsters.=The Living Mountain has already destroyed most of my kind.=@<PERSON><PERSON><PERSON> will destroy all life if he has his way.=Follow that Ashen <PERSON>ad.=Capturing him will reveal more of <PERSON><PERSON><PERSON>'s plans.", "ReturnText": "Thank you, and thank <PERSON><PERSON> for sending you!", "PraiseText": "Thank you for helping our people!"}, {"MissionName": "OutOnALimbHard", "MissionID": "191", "PreReqMissions": "TheAshenDryadHard", "ZoneSet": "EmeraldGladesHard", "Priority": "Story", "ContactName": "EG_Mayor01Hard", "ReturnName": "EG_Mayor01Hard", "CompleteCount": "1", "Dungeon": "EG_Mission2Hard", "MissionLevel": "34", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Out on a Limb", "TrackerText": "Defeat the traitorous Coalglow high up in the First Tree", "TrackerReturn": "Descend the Ur-Tree and tell Longroots of your success", "Description": "The fiendish <PERSON>g<PERSON> plots to burn the Ur-Tree. Pursue her into the Tree's highest branches and stop <PERSON><PERSON><PERSON>'s plan.", "PreReqText": "Have you seen <PERSON><PERSON>? She left our tree headed west", "OfferText": "<PERSON><PERSON><PERSON>'s corrupted Dryads have infested the Ur-Tree!", "ActiveText": "<PERSON><PERSON><PERSON>'s fiends drove us from the tree.=His corrupted plants and flames are everywhere.=@I won't let <PERSON><PERSON><PERSON>'s cult destroy this tree.=You must hurry, I smell smoke!", "ReturnText": "Thank you, but the tree isn't safe yet."}, {"MissionName": "RottenToTheRootsHard", "MissionID": "192", "PreReqMissions": "OutOnALimbHard", "ZoneSet": "EmeraldGladesHard", "Priority": "Story", "ContactName": "EG_Mayor01Hard", "ReturnName": "EG_Mayor01Hard", "CompleteCount": "1", "Dungeon": "EG_Mission3Hard", "MissionLevel": "35", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Rotten to the Roots", "TrackerText": "Close the tunnels deep under the First Tree's roots", "TrackerReturn": "Consult Longroots and plan your attack on <PERSON><PERSON><PERSON>'s priests", "Description": "<PERSON><PERSON><PERSON>'s cult could attack again from the roots at any moment. Descend into the tunnels and stop them.", "OfferText": "<PERSON><PERSON><PERSON>'s abominations tunneled in from the roots.", "ActiveText": "The Priests of Meylour will just send more fire.=@Where did they come from?=They burrowed here from their temples.=@Point me towards the tunnel.=:@I'll make sure no more come come from there.", "ReturnText": "<PERSON><PERSON><PERSON> wants everything dead. Everything."}, {"MissionName": "HopeSpringsEternalHard", "MissionID": "193", "PreReqMissions": "RottenToTheRootsHard", "ZoneSet": "EmeraldGladesHard", "Priority": "Story", "ContactName": "EG_Mayor01Hard", "ReturnName": "EG_Mayor01Hard", "CompleteCount": "1", "Dungeon": "EG_Mission4Hard", "MissionLevel": "35", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Hope Springs Eternal", "TrackerText": "Confront the Priests of Meylour at the ancient temple", "TrackerReturn": "Longroots can help you find the rest of <PERSON><PERSON><PERSON>'s priests", "Description": "Defeat the Priests of Meylour and find a way to trace the murderous mountain god to his hiding place.", "OfferText": "Head to the ancient temple and defeat the <PERSON><PERSON><PERSON>'s cultists", "ActiveText": "The priests of <PERSON><PERSON><PERSON> are preparing yet another ritual.=@They never give up.=You need to find a way to <PERSON><PERSON><PERSON>'s Heart.=@<PERSON><PERSON><PERSON>'s Heart? What's that?=It is where <PERSON><PERSON><PERSON>'s power lies.=Only his priests know its location.=@Then I will make them tell me...=@One way or another.", "ReturnText": "The Cult of <PERSON>yl<PERSON> is almost defeated!"}, {"MissionName": "DELETED6", "MissionID": "194", "ZoneSet": "TOBEDELETED"}, {"MissionName": "RefugeOfTheDamnedHard", "MissionID": "195", "PreReqMissions": "HopeSpringsEternalHard", "ZoneSet": "EmeraldGladesHard", "Priority": "Story", "ContactName": "EG_Mayor01Hard", "ReturnName": "EG_Mayor01Hard", "CompleteCount": "1", "Dungeon": "EG_Mission5Hard", "MissionLevel": "35", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Refuge of the Damned", "TrackerText": "Track the <PERSON><PERSON>our cult to the far corner of the Glades", "TrackerReturn": "Tell Longroots that the Meylour hideout is cleared", "Description": "<PERSON><PERSON><PERSON>'s priests have fled from your fury to a corner of the Glades. Make them reveal <PERSON><PERSON><PERSON>'s Heart.", "OfferText": "The last of <PERSON><PERSON><PERSON>'s cult cowers in fear.", "ActiveText": "You've almost beaten them.=@But I still don't know where The Heart of Meylour is.=@I will make these last cultists tell me.=@<PERSON><PERSON><PERSON>'s terror will come to an end.", "ReturnText": "Now you can finally strike down <PERSON><PERSON><PERSON> forever.", "PraiseText": "<PERSON><PERSON><PERSON>'s followers got what they deserved."}, {"MissionName": "GatherDarkTotemsHard", "MissionID": "196", "ZoneSet": "EmeraldGladesHard", "Priority": "Side", "ContactName": "EG_Villager02Hard", "ReturnName": "EG_Villager02Hard", "CompleteCount": "20", "ProgressIcon": "a_QuestIcon_DarkTotem", "ProgressText": "Dark Totem", "MissionLevel": "34", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Collect Dark Totems", "TrackerText": "Seize the dark totems of the Ashen Dryad betrayers", "TrackerReturn": "Present the Dryad River with the seized dark totems", "Description": "Ashen Dryads carry dark totems to show fealty to the malicious <PERSON><PERSON><PERSON>. Defeat Dryads and seize these totems.", "OfferText": "Help us gather the Dark Totems from the Ashen Dryads", "ActiveText": "You can find them on the Ashen Dryads=We need them for a protection from fire spell.", "ReturnText": "Perfect, I'll get the protection spell started."}, {"MissionName": "GatherPriestMasksHard", "MissionID": "197", "PreReqMissions": "GatherDarkTotemsHard", "ZoneSet": "EmeraldGladesHard", "Priority": "Side", "ContactName": "EG_Villager02Hard", "ReturnName": "EG_Villager02Hard", "CompleteCount": "20", "ProgressIcon": "a_QuestIcon_PriestMask", "ProgressText": "<PERSON> of <PERSON><PERSON><PERSON>", "MissionLevel": "35", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Seize Fire Priest Masks", "TrackerText": "Gather masks from defeated Priest<PERSON> of Meylour", "TrackerReturn": "Present the Dryad River with the seized masks", "Description": "<PERSON><PERSON><PERSON>'s Priests wear ceremonial masks while commiting their dark deeds. Defeat the Priests and gather their bizarre masks.", "OfferText": "We also need <PERSON><PERSON><PERSON>'s <PERSON>s for the protection spell", "ActiveText": "Please hurry, the priests attacks need to be repelled.", "ReturnText": "Just what we needed. Thank you!", "PraiseText": "We should be protected now. Thanks again!"}, {"MissionName": "KillGladeEmbersHard", "MissionID": "198", "ZoneSet": "EmeraldGladesHard", "Priority": "Side", "ContactName": "EG_Villager03Hard", "ReturnName": "EG_Villager03Hard", "CompleteCount": "40", "MissionLevel": "35", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Extinguish the Fire", "TrackerText": "Destroy the fiery Embers wherever you find them", "TrackerReturn": "Tell Briarbranch you've cleared many fire Embers", "Description": "The Dryads call the ravaging fire spirits that plague Emerald Glades 'Embers.' Whatever, you decide to destroy them.", "OfferText": "Can you help us put out the fires plaguing our land?", "ActiveText": "Living fire is everywhere, please destroy it.", "ReturnText": "Thank you. I can breath better already.", "PraiseText": "Hopefully our land will grow green once more!"}, {"MissionName": "DeepgardDragonHard", "MissionID": "199", "PreReqMissions": "MeylourFinaleHard", "ZoneSet": "BridgeTownHard,CastleHard", "Priority": "Story", "ContactName": "BT_WardenHard", "ReturnName": "AC_Mayor01Hard", "CompleteCount": "1", "Dungeon": "AC_Mission1Hard", "MissionLevel": "36", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Castle Hocke", "TrackerText": "Enter Castle Hocke and discover what lies beneath it.", "TrackerReturn": "Speak to the survivors in their camp in the courtyard", "Description": "The mystic barrier surrounding Castle Hocke has fallen. Enter the castle and discover its secrets.", "PreReqText": "You've changed this land for the better.=But I fear our troubles have just begun.", "OfferText": "By destroying <PERSON><PERSON><PERSON>, you've left the path to the Sleeping Lands unblocked.", "ActiveText": "With <PERSON><PERSON><PERSON>'s barrier gone, the path to the castle is open.=My people came up from there decades ago.=We were fleeing a life of slavery in The Sleeping Lands.=The Dragons and Goblins kept us in chains.=There's no knowing what's there now.=<PERSON><PERSON><PERSON>'s barrier kept the Sleeping Lands locked away from this world.", "ReturnText": "Marshal <PERSON><PERSON><PERSON><PERSON><PERSON> is defeated, not just his dream this time.", "PraiseText": "You've stopped the dragons from invading again.=But who knows what else is down there."}, {"MissionName": "BattlesLostAndWonHard", "MissionID": "200", "PreReqMissions": "DeepgardDragonHard", "ZoneSet": "CastleHard", "Priority": "Story", "ContactName": "AC_Mayor01Hard", "CompleteCount": "1", "Dungeon": "AC_Mission3Hard", "MissionLevel": "36", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Battles Lost and Won", "TrackerText": "Find the <PERSON> Titus on the haunted batlefield", "Description": "Somewhere on this battlefield is the <PERSON>. He might know the secret of the Sleeping Lands.", "OfferText": "Our leader, <PERSON>, went to the Observatory looking for some way to fight the dragons.", "ActiveText": "Thank you for slaying that dragon.=@Who are you people?=We were with <PERSON><PERSON><PERSON>'s expedition.=@Like the people of Wolf's End?=Yes, the very same.=We fought through the goblins and into the castle.=<PERSON><PERSON><PERSON> left us to guard the camp.=He and his knights went down below.=They passed through the portal.=@And you've been trapped here ever since.=The <PERSON> helped us.=When the dragon legion came, he went to find a way to stop them.=You should find find him.", "PraiseText": "I still wonder what became of <PERSON><PERSON><PERSON> in the Sleeping Lands."}, {"MissionName": "AethericObservatoryHard", "MissionID": "201", "PreReqMissions": "BattlesLostAndWonHard", "ZoneSet": "CastleHard", "Priority": "Story", "ContactName": "AC_Titus01Hard", "ReturnName": "AC_Titus01Hard", "CompleteCount": "1", "Dungeon": "AC_Mission4Hard", "MissionLevel": "37", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Aetheric Observatory", "TrackerText": "Get to the top of the observatory before Nephit.", "TrackerReturn": "Consult with <PERSON> over the Aetheric Map", "Description": "<PERSON> kept the text of a powerful ritual in his observatory. Now <PERSON><PERSON><PERSON> wants is, no doubt for a very bad reason.", "PreReqText": "How is it you see me?", "ActiveText": "<PERSON><PERSON><PERSON> seeks an ancient ritual from the Aetheric Observatory.=When we were both alive, he asked for it.=I never trusted him with such power.=@What is this ritual?=It contacts terrible, ancient gods.=@Like <PERSON><PERSON><PERSON>. Why would he want that?=I don't know, but it's a bad idea.=A really, really bad idea.=Terrible idea.=@I get it. I'll stop him.", "ReturnText": "Stop <PERSON><PERSON><PERSON> from using the ritual."}, {"MissionName": "LastStandHard", "MissionID": "202", "PreReqMissions": "AethericObservatoryHard", "ZoneSet": "CastleHard", "Priority": "Story", "ContactName": "AC_Titus01Hard", "CompleteCount": "1", "Dungeon": "AC_Mission5Hard", "MissionLevel": "37", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Last Stand", "TrackerText": "Stop <PERSON><PERSON><PERSON>! He fled to the battlements.", "Description": "<PERSON><PERSON><PERSON> has the ritual and is fleeing to the battlements. Stop him!", "OfferText": "<PERSON><PERSON><PERSON> materialized on the ramparts after you fought him.", "ActiveText": "I take it <PERSON><PERSON><PERSON> found the ritual?=@He has it, yes.=I don't know why, but he's going to use it.=Maybe he hopes it will restore him to life.=@Is that possible?=Maybe, but it would be a monstrous existence.=<PERSON><PERSON> now, stop him before he can find out.", "PraiseText": "<PERSON><PERSON><PERSON> went west to the Castle!"}, {"MissionName": "CapstoneHard", "MissionID": "203", "PreReqMissions": "LastStandHard", "ZoneSet": "CastleHard", "Priority": "Story", "ContactName": "AC_Titus02Hard", "ReturnName": "AC_Titus03Hard", "CompleteCount": "1", "Dungeon": "AC_Mission6Hard", "MissionLevel": "37", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Capstone", "TrackerText": "<PERSON><PERSON><PERSON> Nephit into the dungeons of Castle Hocke", "TrackerReturn": "<PERSON> of <PERSON><PERSON><PERSON>'s defeat and the broken Capstone", "Description": "Pursue the Nephit into the dungeons where the legendary <PERSON><PERSON> sits. Stop him from using the ritual", "PreReqText": "Hurry! <PERSON><PERSON><PERSON> went this way!", "ActiveText": "Beyond lies the portal to the Sleeping Lands=<PERSON>'s sages created a powerful Capstone to seal the portal.=It limits the travel between worlds.=<PERSON><PERSON><PERSON> will try and break the Capstone and use its energy for his ritual.=@What happens if he does that?=Travel between the realms will be unstoppable.=@More dragons, goblins and worse.=Assuming <PERSON><PERSON><PERSON>'s ritual doesn't kill us all.", "ReturnText": "At least, <PERSON><PERSON><PERSON> is no more.", "PraiseText": "It's coming=@What is coming?=Everything=Riches and Nightmares=Monsters and Magic=Good and Evil=It's all down there, and it's all awake now"}, {"MissionName": "EmeraldThroneHard", "MissionID": "204", "ZoneSet": "CastleHard", "Priority": "Dungeon", "ContactName": "AC_Villager04Hard", "CompleteCount": "1", "Dungeon": "AC_Mission2Hard", "MissionLevel": "36", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Emerald Throne", "TrackerText": "A secret door in the Throne Room leads to the treasury", "Description": "The survivors remember a secret door in the castle's Throne Room that leads the treasury. You always have time for that.", "OfferText": "There is a secret door in the throne room that leads to the cave", "ActiveText": "Some of the dragon's soldiers are holed up down there.=Be careful, they have one of <PERSON>'s golems at their command.", "PraiseText": "That was <PERSON>'s Prime <PERSON><PERSON><PERSON>.=The first of his Golem guards.=A shame to see such a wonder perverted."}, {"MissionName": "SpiritProblemHard", "MissionID": "205", "ZoneSet": "CastleHard", "Priority": "Side", "ContactName": "AC_Villager03Hard", "ReturnName": "AC_Villager03Hard", "CompleteCount": "60", "MissionLevel": "36", "ExpReward": "M", "GoldReward": "M", "DisplayName": "A Lizard Problem", "TrackerText": "Evict the Tuatara legionnaires that infest the castle", "TrackerReturn": "Check on <PERSON><PERSON> and the other survivors in the courtyard", "Description": "You've slain their general, but the Tuatara Legion still infests the castle. Time to clean house.", "OfferText": "So many Tuatara soldiers, can you trim their numbers?", "ActiveText": "They are all over the castle=They came from below=@I've fought their kind before.=@I'll take care of them.", "ReturnText": "Thank you, maybe we can finally rebuild here.", "PraiseText": "Thanks, I already feel safer here."}, {"MissionName": "GatherDreadMasksHard", "MissionID": "206", "ZoneSet": "CastleHard", "Priority": "Side", "ContactName": "AC_Villager05Hard", "ReturnName": "AC_Villager05Hard", "CompleteCount": "20", "ProgressIcon": "a_QuestIcon_DreadMask", "ProgressText": "Dread <PERSON>", "MissionLevel": "37", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Gather Dread Masks", "TrackerText": "Defeat Dread Paladins and claim their helms as prizes", "TrackerReturn": "Check on <PERSON><PERSON> and the other survivors in the courtyard", "Description": "The great Dr<PERSON>, were golems in <PERSON>'s defense. Now the Tuatara control them.", "OfferText": "The Dread Paladin masks are the secret to controlling these golems.", "ActiveText": "The dragon general took control of the Dread Paladin Golems.=They used to be <PERSON>'s private bodyguard.=Their masks are the key to controlling them.=If you bring me any you find, perhaps I can restore control.", "ReturnText": "Thanks, I will get started on deconstructing these right away.", "PraiseText": "Thanks again for gathering the masks."}, {"MissionName": "IntoTheDepthsHard", "MissionID": "207", "PreReqMissions": "CapstoneHard", "ZoneSet": "CastleHard,ShazariDesertHard", "Priority": "Story", "ContactName": "AC_Titus03Hard", "ReturnName": "SD_Titus01Hard", "CompleteCount": "0", "MissionLevel": "37", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Into the Depths", "TrackerText": "Take to portal to the Sleeping Lands", "TrackerReturn": "Speak with <PERSON> on the other side", "Description": "The seal between the Sleeping Land and this land lies broken. Unlimited danger lies below, and unlimited opportunity.", "PreReqText": "He's headed towards the Capstone!", "OfferText": "<PERSON><PERSON><PERSON> is defeated, but the Capstone is destroyed. The link to the Sleeping Lands lies open", "ActiveText": "What are the Sleeping Lands?=Everything=Riches and Nightmares=Monsters and Magic=Good and Evil=It's all down there, and it's all awake now=@Riches?=Ha, you are still an adventurer=You are going down there, aren't you?=Well, then, I will accompany you=That gate should take us there=Or else tear us to pieces=@Enough talk, let's go", "ReturnText": "Ah. You made it over and appear to be in one piece. Excellent!"}, {"MissionName": "TempleOfShadowsHard", "MissionID": "208", "PreReqMissions": "ScarabInvasionHard", "ZoneSet": "ShazariDesertHard", "Priority": "Dungeon", "ContactName": "SD_Sage01Hard", "ReturnName": "SD_Sage01Hard", "CompleteCount": "1", "Dungeon": "SD_Mission1Hard", "MissionLevel": "38", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Unearthing the Past", "TrackerText": "Discover the secrets of the Temple of Umbra", "TrackerReturn": "Reveal what you discovered in the temple", "Description": "The ancient Magi built the pyramids thousands of years ago. Many of their secrets lie buried in their tombs.", "OfferText": "These grand pyramids are not the work of our people.", "ActiveText": "The Magi who built the pyramids left this land long ago.=We have taken some of their ruins as our own.=But others remain guarded by powerful spirits.=If it's ancient knowledge you seek…=And you're daring enough to face ancient magics…=Then you should plumb the depths of the Umbral Temple to the West.", "ReturnText": "You are a survivor and a scholar. I hope you learned much of value."}, {"MissionName": "TravelToTownOneHard", "MissionID": "209", "PreReqMissions": "IntoTheDepthsHard", "ZoneSet": "ShazariDesertHard", "Priority": "Story", "ContactName": "SD_Titus01Hard", "ReturnName": "SD_Mayor01Hard", "CompleteCount": "0", "MissionLevel": "38", "ExpReward": "L", "GoldReward": "L", "DisplayName": "The Sand People", "TrackerReturn": "Head to the Town of Kovah and speak with Mayor <PERSON><PERSON>", "Description": "The Town Elder in Kovah wishes to pay his respects to you and has urgent need of your assistance.", "OfferText": "Go to the near by village and meet the Town Elder", "ActiveText": "Welcome to the Sleeping Lands, #tn#.=@It seems a wondrous place=This country is called Shazari.=For centuries the dragons ruled here.=@I bet the dragons were cruel masters.=Terrible and cruel, yes.=But the people here are free now.=Go introduce yourself to the town elder.", "ReturnText": "I hope we find some way to avoid falling under The Emperor's power."}, {"MissionName": "DELETED7", "MissionID": "210", "ZoneSet": "TOBEDELETED"}, {"MissionName": "ScarabInvasionHard", "MissionID": "211", "PreReqMissions": "TravelToTownOneHard", "ZoneSet": "ShazariDesertHard", "Priority": "Story", "ContactName": "SD_Mayor01Hard", "ReturnName": "SD_Mayor01Hard", "CompleteCount": "1", "Dungeon": "SD_Mission2Hard", "MissionLevel": "38", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Scarab Invasion", "TrackerText": "Help <PERSON><PERSON><PERSON> reclaim its temple", "TrackerReturn": "Let the Elder know the temple is safe again", "Description": "Seelie magic has transformed the desert's insects into enormous creatures. They've overrun the town's temple.", "OfferText": "Thank you for coming, #tn#. We need your help.", "ActiveText": "Welcome to humble <PERSON><PERSON><PERSON>.=@It is an honor to meet you, Elder.=The honor is all mine.=Slayer of the dragons and Opener of the Path…=You have accomplished true wonders.=@I do what I can.=We will speak more on these matters…=But now I beg your help.=Seelie magicks plague our temple, filling it with abominations.=Centuries under dragon rule left us with no warriors to fight them.=@It will be my pleasure to take care of it, Elder.", "ReturnText": "Now our village will survive!"}, {"MissionName": "BloodAndSandHard", "MissionID": "212", "PreReqMissions": "GoSeeGladiatorHard", "ZoneSet": "ShazariDesertHard", "Priority": "Story", "ContactName": "SD_Gladiator01Hard", "ReturnName": "SD_Gladiator01Hard", "CompleteCount": "1", "Dungeon": "SD_Mission3Hard", "MissionLevel": "39", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Blood and Sand", "TrackerText": "Defeat the Pit Lord in the Shazari Arena", "TrackerReturn": "Tell Zumwalt the Pit Lord's reign is over", "Description": "The last of the dragon tyrants calls himself The Pit Lord. Bring down his slave arena and end his reign of terror.", "OfferText": "The other dragons are gone, but the foul Pit Lord remains.", "ActiveText": "Watch yourself, stranger…=The Pit Lord enslaves all who come in his grasp.=@Elder <PERSON> sent me.=@I have some experience slaying dragons.=Well, that's a rare skill indeed.=But this wily old wyrm isn't alone.=He has slaves who fight for him, which is bad enough.=Recently he got some other monsters as well.=I don't know where his reinforcements came from.=But they've got strange magicks.", "ReturnText": "The Pit Lord slain! We are a free realm at last.", "PraiseText": "I still wonder where The <PERSON> Lord got his reinforcements from.=Someone was trying to prop him up.=Someone is causing trouble for the free peoples of Shazari."}, {"MissionName": "GoblinDiplomacyHard", "MissionID": "213", "PreReqMissions": "GoSeeGoblinHard", "ZoneSet": "ShazariDesertHard", "Priority": "Story", "ContactName": "SD_Chief<PERSON><PERSON><PERSON>", "ReturnName": "SD_Chief<PERSON><PERSON><PERSON>", "CompleteCount": "1", "Dungeon": "SD_Mission4Hard", "MissionLevel": "39", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Goblin Diplomacy", "TrackerText": "What lies beyond the Eastern Wall?", "TrackerReturn": "Tell the Goblin Elder that the Seelie are readying for war", "Description": "Beyond the Eastern Wall strange forces are mustering for war. Find out what you can about them.", "OfferText": "A new force seeks to replace the dragon legions here in Shazari.", "ActiveText": "Just when I thought we could resume normal trading.=@Goblin. What do you want?=Human! I want to be free to trade with my friends and allies.=@Well, what's stopping you?=Strange new warriors, taller than men.=I believe they might be Seelie Ogres.=@What are Seelie?=An ancient faerie race, expert in magicks.=They corrupt all they touch.=They're encamped beyond the wall to the East.=@I shall investigate...=@But you best be speaking truth, goblin.=Thank you, hero! Good luck!", "ReturnText": "So, it was <PERSON><PERSON>. We shall need the Emperor's help for sure!", "PraiseText": "Find someone from Valhaven.=We need the Emperor's aid against a foe like the Seelie.=We stand no chance!"}, {"MissionName": "AncientBurialGroundsHard", "MissionID": "214", "PreReqMissions": "GoblinDiplomacyHard", "ZoneSet": "ShazariDesertHard", "Priority": "Story", "ContactName": "SD_Jackal01Hard", "ReturnName": "SD_Jackal01Hard", "CompleteCount": "1", "Dungeon": "SD_Mission5Hard", "MissionLevel": "40", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Ancient Unrest", "TrackerText": "Investigate the Burial Grounds", "TrackerReturn": "Tell <PERSON><PERSON><PERSON> what you discovered", "Description": "Search the ancient burial grounds for signs of The Emperor's involvement with Seelie magicks.", "OfferText": "The goblin elder sent you to find a Valhaven ambassador, right?", "ActiveText": "I'm certainly not the ambassador.=@Then what business do we have, stranger?=I believe the Emperor himself is behind the Seelie.=@That's a serious accusation.=Which is why I need proof.=@Why would the Emperor corrupt this land.=With the dragons gone, he seeks to add <PERSON><PERSON><PERSON> to his empire.=The ancient burial grounds seem the source of the trouble.=See if you find any sign of Imperial meddling.", "ReturnText": "I thought as much! The Emperor is ever pitiless and vile.", "PraiseText": "Meet me by the docks.=@Why?=So I can sneak you into Valhaven of course.=You've foiled the Emperor's plans here.=You're going to need help fighting your newest foe.=@And you're that help?=I have some very capable friends.=Come to the boat."}, {"MissionName": "AttuneTheAnchorHard", "MissionID": "215", "PreReqMissions": "GoblinDiplomacyHard", "ZoneSet": "ShazariDesertHard", "Priority": "Dungeon", "ContactName": "SD_Titus02Hard", "ReturnName": "SD_Titus02Hard", "CompleteCount": "1", "Dungeon": "SD_Mission6Hard", "MissionLevel": "40", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Legacy of the Magi", "TrackerText": "The Shining Pyramid holds the secrets of the Magi", "TrackerReturn": "Meet up with <PERSON> to tell him what you found", "Description": "An ancient pyramid looms off in the distance. It contains the secrets of the Magi. Closely guarded secrets.", "OfferText": "The old pyramids around here might hold something of interest.", "ActiveText": "The ancient Seelie built these pyramids.=One of them remains sealed still.=I imagine there are wonders and dangers inside.=@Why do wonders always come with danger?=The Seelie were always cruel that way.", "ReturnText": "It sounds more wondrous than I imagined!", "PraiseText": "And you took care of the dangers as well.=@It was a very strange place.=Now that you've defeated the defenses…=I'm going to spend some time plumbing its secrets.=@Good luck.=@The place is too cold and harsh for me."}, {"MissionName": "GoblinMessengerHard", "MissionID": "216", "PreReqMissions": "GoblinDiplomacyHard", "ZoneSet": "ShazariDesertHard", "Priority": "Side", "ContactName": "SD_Emissary02Hard", "ReturnName": "SD_Chief<PERSON><PERSON><PERSON>", "ActiveTarget": "SD_Emissary01Hard", "CompleteCount": "1", "MissionLevel": "39", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Goblin Messenger", "TrackerText": "Find the second goblin messenger", "TrackerReturn": "Let the Goblin chief know you've helped him out", "Description": "The Goblin messengers fled when the <PERSON><PERSON> came. Tell them it's safe to return home.", "OfferText": "Some of my people fled West when the Seelie came.", "ActiveText": "If you see my fearful messengers in other towns…=Could you tell them it's safe to come back?=Thanks to you, my friend.=@No goblin has called me friend before.=Well, friends we are!=@I guess that's true.=@Weird but true.", "ReturnText": "My thanks! We have much less to fear now that the Seelie are defeated.", "PraiseText": "I think now we goblins can return to our peaceful ways.=@Do goblins really love peace?=Long ago we did.=I think we shall again.=@I certainly hope so."}, {"MissionName": "GatherScorpionStingersHard", "MissionID": "217", "PreReqMissions": "TravelToTownOneHard", "ZoneSet": "ShazariDesertHard", "Priority": "Side", "ContactName": "SD_Acolyte01Hard", "ReturnName": "SD_Acolyte01Hard", "CompleteCount": "20", "ProgressIcon": "a_QuestIcon_ScorpionStinger", "ProgressText": "<PERSON><PERSON><PERSON>er", "MissionLevel": "38", "ExpReward": "M", "GoldReward": "M", "DisplayName": "An Ironic Hunt", "TrackerText": "Poach Stingers for anti-venom", "TrackerReturn": "Bring the stingers back to the Acolyte", "Description": "The scorpions are the plague of this land. If the acolyte had enough of their poison, she could create an anti-venom.", "OfferText": "Even without the threat of drought, travel just isn't safe anymore", "ActiveText": "There are so many dangers on the roads these days=People are turning up with nasty stings from scarab scorpions=I can't do anything about the Outlander problem, but I could at least solve the scorpion problem=If you bring me enough stingers I could make as anti-venom", "ReturnText": "Oh excellent! This is more than enough!", "PraiseText": "This anti-venom will save a lot of lives=You look like you took quite a few stings yourself=You must be made of tougher stuff than your average traveler to still be walking around=Thank you for all you've done for us!=The Sky bless you on your journey"}, {"MissionName": "DestroyWaspHivesHard", "MissionID": "218", "PreReqMissions": "ScarabInvasionHard", "ZoneSet": "ShazariDesertHard", "Priority": "Side", "ContactName": "SD_Acolyte02Hard", "ReturnName": "SD_Acolyte02Hard", "CompleteCount": "40", "MissionLevel": "39", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Heavy Hive Hittin'", "TrackerText": "Knock down a few wasp hives", "TrackerReturn": "Let the acolyte know you've thinned the Wasps", "Description": "The wasp hives are a danger to anyone that goes near them. Knock a few down to make things safer.", "OfferText": "Perhaps you could do something else for us", "ActiveText": "You see these wasp hives?=They've really gotten out of hand and the wasps attack people that get too near=The roads would be much safer if you could knock a few of them down", "ReturnText": "Great!", "PraiseText": "Looks like you took a sting or two=Let me take care of that for you=We really appreciate your sacrifice=The Sky bless you on your journey."}, {"MissionName": "CollectGoblinCharmsHard", "MissionID": "219", "PreReqMissions": "TravelToTownOneHard", "ZoneSet": "ShazariDesertHard", "Priority": "Side", "ContactName": "SD_Slave01Hard", "ReturnName": "SD_Slave01Hard", "CompleteCount": "20", "ProgressIcon": "a_QuestIcon_GoblinTag", "ProgressText": "Goblin Memory Charm", "MissionLevel": "39", "ExpReward": "M", "GoldReward": "M", "DisplayName": "You Lost Your Marbles", "TrackerText": "Reclaim fallen slave charms from the outlanders", "TrackerReturn": "Return the charms to the escapee", "Description": "Most of the goblin slaves didn't survive the escape. Find their memory charms so <PERSON><PERSON> can return them to their families.", "OfferText": "Our escape plan failed. None of the goblin slaves made it out", "ActiveText": "Find their memory charms and I can take them to their families=They fought bravely, but they just weren't strong enough=Their families deserve some closure", "ReturnText": "You found them all?", "PraiseText": "I'll make sure their families know they fought bravely=I'd come to know them all in the slave pits=They are all really great guys=Thank you for what you've done"}, {"MissionName": "CollectGiantBracersHard", "MissionID": "220", "PreReqMissions": "TravelToTownOneHard", "ZoneSet": "ShazariDesertHard", "Priority": "Side", "ContactName": "SD_Nomad01Hard", "ReturnName": "SD_Nomad01Hard", "CompleteCount": "20", "ProgressIcon": "a_QuestIcon_GiantBracer", "ProgressText": "<PERSON><PERSON>", "MissionLevel": "39", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Repo Men", "TrackerText": "Gather up bracers from the Oasis Giants", "TrackerReturn": "Bring the bracers back to Kiba Nibs", "Description": "The craftsman <PERSON><PERSON> can pay well for <PERSON><PERSON> bracers. Bring him any you find.", "OfferText": "The <PERSON>lie bracers are fascinating to me", "ActiveText": "If had a bunch of those <PERSON>lie bracers...=I could break them down and start replicating them.=Smaller size of course.=But Goblins would pay well for something like those.=Bring me what you can.", "ReturnText": "Hmm. Not exactly new anymore. Ah well, he's your pay", "PraiseText": "See?=Everyone profits!"}, {"MissionName": "CollectWormGlandsHard", "MissionID": "221", "PreReqMissions": "ScarabInvasionHard", "ZoneSet": "ShazariDesertHard", "Priority": "Side", "ContactName": "SD_Matron01Hard", "ReturnName": "SD_Matron01Hard", "CompleteCount": "30", "ProgressIcon": "a_QuestIcon_SandWormGland", "ProgressText": "Sandworm Mucus Gland", "MissionLevel": "40", "ExpReward": "M", "GoldReward": "M", "DisplayName": "It's Snot a Problem", "TrackerText": "Gather Sandworm Mucus in the Ancient Burial Grounds", "TrackerReturn": "Bring the glands to the <PERSON>ron", "Description": "The <PERSON><PERSON> believes she can make an insect repellant from Sandworm Mucus. Sounds disgusting, but maybe it will work.", "OfferText": "We may be able to keep this from happening again", "ActiveText": "Bring me enough mucus glands and I can mix up an repellent=That will keep the bugs out of the temple", "ReturnText": "This will do nicely. Take this for your trouble", "PraiseText": "Wow, this stuff smells horrid=It'll scare away the bugs though, so it's worth it=The Sky bless you on your journey"}, {"MissionName": "DELETED8", "MissionID": "222", "ZoneSet": "TOBEDELETED"}, {"MissionName": "HeadToValhaven", "MissionID": "223", "PreReqMissions": "AncientBurialGrounds", "ZoneSet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,JadeCity", "Priority": "Story", "ContactName": "SD_Jackal01", "CompleteCount": "1", "Dungeon": "JC_Mission1", "MissionLevel": "26", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Welcome Party", "TrackerText": "Head to Valhaven and meet <PERSON><PERSON><PERSON>", "Description": "Time to learn what the Emperor is really up to. Meet up with <PERSON><PERSON><PERSON> in Valhaven.", "OfferText": "You'll have to come with me to Valhaven to stop the Emperor and his Seelie allies.", "ActiveText": "The Emperor will be after you now.=@Your emperor seems like a scheming fiend.=He was a great man once.=Came out of nowhere and kicked out the Goblins.=Then he held off the dragons.=But in recent years he's grown cruel.=@And he seems to be in league with the Seelie.=Yes, that's true.=I didn't know how evil they were until today.=Come, we must hurry.=If we're lucky, The Emperor won't know we're coming."}, {"MissionName": "Meet<PERSON>ith<PERSON><PERSON>", "MissionID": "224", "PreReqMissions": "HeadToValhaven", "ZoneSet": "JadeCity", "Priority": "Story", "ReturnName": "VH_Odin01", "CompleteCount": "0", "MissionLevel": "26", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Order of the Jester", "TrackerReturn": "Meet with <PERSON><PERSON><PERSON> at the Laughing Jester Inn", "Description": "The Empire has labled you a criminal and attacked you with demons. Meet with rebel leader <PERSON><PERSON><PERSON> to find out what's going on.", "ReturnText": "You must be the Traveller <PERSON><PERSON><PERSON> spoke of. Welcome."}, {"MissionName": "BackAlleyD<PERSON>s", "MissionID": "225", "PreReqMissions": "Meet<PERSON>ith<PERSON><PERSON>", "ZoneSet": "JadeCity", "Priority": "Story", "ContactName": "VH_Odin01", "CompleteCount": "1", "Dungeon": "JC_Mission2", "MissionLevel": "27", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Back Alley Deals", "TrackerText": "Locate the Emperor's Defector <PERSON><PERSON>", "Description": "The Emperor has it out for you personally. One of his key mages was seen hiding out in the Alleys. Find and Question him.", "OfferText": "The Emperor knows your name and want you dead.", "ActiveText": "I wonder what you did to earn his wrath?=@I did foil his plans in Shazari.=That could not have pleased him.=But he would never admit to scheming against Shazar<PERSON>.=And he has put a public price on your head.=His son, the High Mage, will know the truth.=@Why would he tell me?=He seems to be out of favor with his father.=Find him while he's outside the palace and he might talk.=He frequents the alleys, cultivating thieves to his cause."}, {"MissionName": "TheProdigalSon", "MissionID": "226", "PreReqMissions": "BackAlleyD<PERSON>s", "ZoneSet": "JadeCity", "Priority": "Story", "ReturnName": "VH_Odin01", "CompleteCount": "1", "Dungeon": "JC_Mission3", "MissionLevel": "28", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Prodigal Son", "TrackerText": "Chase Down the Emperor's Son", "TrackerReturn": "<PERSON> The Emperor is <PERSON><PERSON><PERSON>", "Description": "The Emperor is <PERSON><PERSON><PERSON>, and he thinks you're trying to steal his birth right from him.", "ReturnText": "So, the Emperor is from your world!=His origins were always a mystery.=He overthrew the Goblins and set himself up ten years ago.=Thanks to his Seelie allies, he's been unstoppable.", "PraiseText": "I can sense you're destined to do great things here=Work with <PERSON><PERSON> to find a chink in the Emperor's armor"}, {"MissionName": "SpeakWithFabMab", "MissionID": "227", "PreReqMissions": "ShadowsOfThePast", "ZoneSet": "JadeCity", "Priority": "Story", "ContactName": "VH_Odin01", "ReturnName": "VH_FabMab01", "CompleteCount": "0", "MissionLevel": "28", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Dark Origins", "TrackerText": "Speak with <PERSON><PERSON>", "TrackerReturn": "<PERSON> knows more than anyone about The Seelie", "Description": "<PERSON><PERSON> knows a thing or two about The Emperor's evil Seelie allies and where their power comes from.", "OfferText": "Now we need <PERSON>'s help figuring out what to do next.", "ActiveText": "<PERSON><PERSON><PERSON> became Emperor thanks to the <PERSON><PERSON>.=Their dark magicks protect him and secure his throne.=We need a counter to their wards.=<PERSON> knows more about <PERSON><PERSON> than anyone I know.=Talk to her and see if she has any ideas.", "ReturnText": "Hello, young man|lady"}, {"MissionName": "DELETED9", "MissionID": "228", "ZoneSet": "TOBEDELETED"}, {"MissionName": "SewerSurfin", "MissionID": "229", "PreReqMissions": "TheProdigalSon", "ZoneSet": "JadeCity", "Priority": "Story", "ContactName": "VH_Odin01", "ReturnName": "VH_Odin01", "CompleteCount": "1", "Dungeon": "JC_Mission4", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Sewer Suffering", "TrackerText": "Remove the Gutter Lord from power", "TrackerReturn": "<PERSON> Odryn the Vermyn are free to fight the Emperor", "Description": "The Vermyn gang is under the Emperor's control. Take out The Gutter Lord and the rats will join the resistance.", "OfferText": "The Vermyn gangs helped The Emperor overthrow the Goblins.", "ActiveText": "I see now why the Emperor hates you.=@<PERSON><PERSON><PERSON> was a brave hero.=@He thinks I want his throne.=Do you? You took his Barony.=@He left his Barony to goblins.=Well, he's angered people here too.= The Vermyn gangs chafe under the Emperor's rule.=They would rise up against him…=Except the <PERSON>utter Lord remains loyal to the Emperor.=Go down and take out the <PERSON>utter Lord.=Then the Vermyn will join us for sure.", "ReturnText": "The Vermyn gangs are in an uproar!=With the Gutter Lord gone, some are already on our side."}, {"MissionName": "TempleOfLostDreams", "MissionID": "230", "PreReqMissions": "SpeakWithFabMab", "ZoneSet": "JadeCity", "Priority": "Story", "ContactName": "VH_FabMab01", "ReturnName": "VH_FabMab01", "CompleteCount": "1", "Dungeon": "JC_Mission5", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Fable of the Lost Temple", "TrackerText": "Recover a Seelie artifact to counter The Emperor's wards", "TrackerReturn": "Show Mab what you found in the temple", "Description": "The Emperor relies on <PERSON>lie magic for his power. Go to the Temple of Lost Dreams and find a way to counter his wards.", "OfferText": "The Emperor sold his soul to the Seelie to secure his throne.", "ActiveText": "The Seelie were the first to awake in the Sleeping Lands.=They were once wise and generous…=But time made them cruel and jealous of others.=They lost power to the Goblins and Dragons…=<PERSON><PERSON><PERSON> turned to them to help defeat the Goblin Kings.=And now they prop up his rule in Valhaven.=It takes Seelie magic to counter Seelie magic.=Go to the Temple of Lost Dreams...=There you might find something I can use to counter the Emperor's wards.", "ReturnText": "Sounds like the Temple was more dangerous than suspected.=The Emperor must have been protecting his secrets well.=Now I know a great deal more about his Seelie magic.=I should be able to get you past the outer wards of his palace.=But you will be on your own when you face him.=He is vulnerable, but still very dangerous."}, {"MissionName": "Inception", "MissionID": "231", "PreReqMissions": "Intervention", "ZoneSet": "JadeCity", "Priority": "Dungeon", "ContactName": "VH_FabMab02", "ReturnName": "VH_FabMab02", "CompleteCount": "1", "Dungeon": "JC_Mission6", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Dream Within a Dream", "TrackerText": "The Garden of Night reveals hidden truths", "TrackerReturn": "Return to <PERSON><PERSON> and tell her what you learned", "Description": "<PERSON><PERSON> thinks you will learn something important about yourself in the Garden of Night.", "OfferText": "You have left your mark on the Sleeping Lands.", "ActiveText": "Now you are a part of the fate of The Sleeping Lands.=Your actions reverberate through the world of dreams.=If you dare face your fate…=You can enter the Primal Dream and learn the truth.=@The truth about what?=The truth about yourself and your place in the world.", "ReturnText": "You have returned. I hope you learned much.", "PraiseText": "It seems a dragon was hiding in your dreams.=@I didn't even know that was possible.=You will find many strange things are possible.=You have seen but a sliver of the Sleeping Lands.=@And I aim to visit much more.=@As long as I can sleep without dragons in my mind.=You'll be lucky if that's your only problem.=This world knows you now.=And I'm not sure it means you well."}, {"MissionName": "Intervention", "MissionID": "232", "PreReqMissions": "AttackOfOpportunity", "ZoneSet": "JadeCity", "Priority": "Story", "ContactName": "VH_Odin01", "ReturnName": "VH_Odin01", "CompleteCount": "1", "Dungeon": "JC_Mission7", "MissionLevel": "30", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Royal Intervention", "TrackerText": "Assault the Palace and Confront the Emperor", "TrackerReturn": "Deliver the news to <PERSON><PERSON><PERSON> that the task is done", "Description": "The only thing left to do now is take down the <PERSON> and hopefully restore some semblance of peace to Valhaven.", "OfferText": "Now for the hard part.", "ActiveText": "The Emperor cowers in his palace.=You've taken out all his support.=The people rally against him.=Emperor <PERSON><PERSON><PERSON>'s reign of terror ends today.", "ReturnText": "Liberty!", "PraiseText": "What you have done here…=I cannot thank you enough=Valhaven is free for the first time in centuries.=Goblins, Dragons, and <PERSON><PERSON> are all defeated.=At last we shall know peace."}, {"MissionName": "AttackOfOpportunity", "MissionID": "233", "PreReqMissions": "TempleOfLostDreams", "ZoneSet": "JadeCity", "Priority": "Story-", "ContactName": "VH_Jackal02", "ReturnName": "VH_Jackal02", "CompleteCount": "3", "MissionLevel": "29", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Strategic Weakness", "TrackerText": "Take out Strategic Imperial Strongholds", "TrackerReturn": "Let <PERSON><PERSON><PERSON> know you've taken out the Emperors support.", "Description": "<PERSON><PERSON><PERSON> has given you a list of strategic targets to hit to severely hobble the Emperor's defenses.", "OfferText": "Now that you can get past the Seelie wards, it's time to strike.", "ActiveText": "Before confronting The Emperor, we need to weaken him.=Take out all his support before you confront him.=His daughters command his army and secret police.=You can find them in the East and West wings of the palace.=You also need to defeat the Imperial Guard Commander.=He lives in the barracks.", "ReturnText": "Now The Emperor's defenses are in disarray.=The time to strike has come!", "PraiseText": "You never fail to impress, #tn#."}, {"MissionName": "TheWestWing", "MissionID": "234", "PreReqMissions": "TempleOfLostDreams", "ZoneSet": "JadeCity", "Priority": "Story", "CompleteCount": "1", "Dungeon": "JC_Mini1", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The West Wing", "TrackerText": "Invade the Western Tower and Defeat Lotte", "Description": "The champion of their Emperor's Army resides in the western tower. If you can take her out, it will be a great set back to his cause."}, {"MissionName": "TheEastWing", "MissionID": "235", "PreReqMissions": "TempleOfLostDreams", "ZoneSet": "JadeCity", "Priority": "Story", "CompleteCount": "1", "Dungeon": "JC_Mini2", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The East Wing", "TrackerText": "Invade the Eastern Tower and Defeat Tanja", "Description": "The Emperor's chief spy has been spotted in the Eastern tower. Without her, the Imperial intelligence network will collapse."}, {"MissionName": "TacticalStrike", "MissionID": "236", "PreReqMissions": "TempleOfLostDreams", "ZoneSet": "JadeCity", "Priority": "Story", "CompleteCount": "1", "Dungeon": "JC_Mission8", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Imperial Barracks", "TrackerText": "Strike at the Heart fo the Imperial Barracks", "Description": "The Imperial Guard Commander lives and trains in the barracks. His troops will be future champions. Take him down."}, {"MissionName": "RingOfFire", "MissionID": "237", "PreReqMissions": "GoSeeSkitts", "ZoneSet": "JadeCity", "Priority": "Story", "ContactName": "VH_Skitts01", "ReturnName": "VH_Skitts01", "CompleteCount": "1", "Dungeon": "JC_Mission11", "MissionLevel": "27", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Ring of Fire", "TrackerText": "Infiltrate the human gangs", "TrackerReturn": "Talk to Skitts and plan your next move", "Description": "The human gangs won't let just anyone in. Prove your toughness and learn more about the gang's shadowy leaders.", "OfferText": "The human gangs here have high standards.", "ActiveText": "You got the Vermyn gangs on your side...=That's impressive.=But the human gangs are different.=If you want to get close to the leaders…=You're going to have to prove yourself to them.=@I'm tough enough, don't you worry.=Be ready for a brutal fight.=They'll gang up on you in that fighting pit.=@I can handle it.=@Probably.", "ReturnText": "Everyone's talkin' about you now, #tn#."}, {"MissionName": "ShadowsOfThePast", "MissionID": "238", "PreReqMissions": "RingOfFire", "ZoneSet": "JadeCity", "Priority": "Story", "ContactName": "VH_Skitts01", "ReturnName": "VH_Odin01", "CompleteCount": "1", "Dungeon": "JC_Mission9", "MissionLevel": "28", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Hiding Out", "TrackerText": "Stop the gang leaders before they kill <PERSON><PERSON><PERSON>", "TrackerReturn": "Tell Odryn you've got the gangs in line", "Description": "The mysterious human gang leaders set you up! They're on their way to kill <PERSON><PERSON><PERSON> in his hideout.", "OfferText": "It was a trick! They're onto you and your rebel friends!", "ActiveText": "The gang leaders set us up.=@What are you talking about?=While you were in the ring, they were on the move.=The gangs are heading for the rebel hideout.=You have to hurry back…=Cut them off before they kill <PERSON><PERSON><PERSON>!", "ReturnText": "If you hadn't stopped the gang leaders,, I'd be dead."}, {"MissionName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MissionID": "239", "PreReqMissions": "SpeakWithFabMab", "ZoneSet": "JadeCity", "Priority": "Dungeon", "CompleteCount": "1", "Dungeon": "JC_Mission10", "MissionLevel": "28", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Ancient Vault", "TrackerText": "Explore the Ancient Vault on the Temple Isle", "Description": "Rumors seem to indicate there is treasure in the Vault on the Temple Isle. It just might warrant a look."}, {"MissionName": "CollectImperialInsignias", "MissionID": "240", "PreReqMissions": "Meet<PERSON>ith<PERSON><PERSON>", "ZoneSet": "JadeCity", "Priority": "Side", "ContactName": "VH_Rebel01", "ReturnName": "VH_Rebel01", "CompleteCount": "20", "ProgressIcon": "a_QuestIcon_ImperialInsignia", "ProgressText": "Imperial Insignia", "MissionLevel": "28", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Men of Dishonor", "TrackerText": "Gather Imperial Insignias for Hank the Tank", "TrackerReturn": "Take the proof of your deeds back to <PERSON>", "Description": "<PERSON> the Tank used to be an Imperial before the Emperor changed. He'll reward you for the proof of fewer Imperial scums.", "OfferText": "I used to be an Imperial…", "ActiveText": "Back before the Emperor lost his mind and started associating with <PERSON><PERSON>.=I followed my conscience and it cost me everything.=Now I watch them terrorize these streets we used to protect.=I'll pay for any Imperial you take off the streets.", "ReturnText": "That's so many…Take this.", "PraiseText": "That's all the money I had left=No, Take it. You are fighting for a cause that is just=I'd trade all the gold in the world for things to return to the way they were=I guess the time for that has passed=Things will never be the same again."}, {"MissionName": "CollectStolenMushrooms", "MissionID": "241", "PreReqMissions": "TheProdigalSon", "ZoneSet": "JadeCity", "Priority": "Side", "ContactName": "VH_Vagrant01", "ReturnName": "VH_Vagrant01", "CompleteCount": "10", "ProgressIcon": "a_QuestIcon_StolenMushroom", "ProgressText": "<PERSON><PERSON>s", "MissionLevel": "28", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Mushroom Man", "TrackerText": "Retrieve the Stolen Mushrooms from the Ratlings", "TrackerReturn": "Get this crop back to The Mushroom Man", "Description": "Ratlings have been raiding The Mushroom Man's prized sewer garden. Won't you help The Mushroom Man?", "OfferText": "What's that?! Yep! They've robbed meh blind!", "ActiveText": "<PERSON><PERSON><PERSON><PERSON> raided mah garden and stole mah mushrooms.=I use those mushrooms to make stew and mah hearty mushroom pie.=With all this Imperial nonsense, people on the streets need mah shrooms to get by!=I don't wanna see anyone starve, do you?", "ReturnText": "Hoo-yeah! You found mah shrooms!", "PraiseText": "Now I can go back to baking mushroom pie=And Mushroom Gumbo=And Mushroom Stew=And Mushroom Soufflé=And Stuffed Mushroom Saffron Surprise=And Mushroom ala Kelp=And, let's see. I know there's more I can make with shrooms"}, {"MissionName": "CollectBrigandNecklaces", "MissionID": "242", "PreReqMissions": "Meet<PERSON>ith<PERSON><PERSON>", "ZoneSet": "JadeCity", "Priority": "Side", "ContactName": "VH_Rebel02", "ReturnName": "VH_Rebel02", "CompleteCount": "15", "ProgressIcon": "a_QuestIcon_Brigand<PERSON>ecklace", "ProgressText": "<PERSON><PERSON><PERSON>", "MissionLevel": "28", "ExpReward": "M", "GoldReward": "M", "DisplayName": "A Few Examples", "TrackerText": "Get the Brigands to find a less dangerous line of work", "TrackerReturn": "Let <PERSON> know the message has been delivered", "Description": "The Brigands are taking advantage of the chaos and running a protection racket. <PERSON> says convince them otherwise.", "OfferText": "The Imperials are bad enough, but the gangs are the worst kinds of opportunists.", "ActiveText": "They are running a protection and extortion racket.=Preying off innocent civilians that can't defend themselves.=I think it's time someone showed them that their days are finished.", "ReturnText": "Thank you traveler.", "PraiseText": "That is one less threat the people have to worry about."}, {"MissionName": "CollectDemonTears", "MissionID": "243", "PreReqMissions": "Inception", "ZoneSet": "JadeCity", "Priority": "Side", "ContactName": "VH_Monk01", "ReturnName": "VH_Monk01", "CompleteCount": "20", "ProgressIcon": "a_QuestIcon_DemonTear", "ProgressText": "Demon Tear", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Devil Might Cry", "TrackerText": "Gather Demon Tears for Odo", "TrackerReturn": "Return the tears to <PERSON><PERSON>", "Description": "<PERSON><PERSON> says the Aura of the city has been tainted by the Imperial's demons. He can cleanse the Aura with their demonic tears.", "OfferText": "It is a tragedy what the Emperor has done here, but all is not lost", "ActiveText": "I can feel the city crying out from beneath the weight of Seelie filth that has tainted it.=As venom is key to making anti-venom, demon tears are key to cleanse Valhaven's aura.", "ReturnText": "Bless you, friend.", "PraiseText": "I will begin the long process of setting things right…=It gladens my heart to meet a noble soul such as you"}, {"MissionName": "HeadToValhavenHard", "MissionID": "244", "PreReqMissions": "AncientBurialGroundsHard", "ZoneSet": "S<PERSON>iDesertHard,JadeCityHard", "Priority": "Story", "ContactName": "SD_Jackal01Hard", "CompleteCount": "1", "Dungeon": "JC_Mission1Hard", "MissionLevel": "26", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Welcome Party", "TrackerText": "Head to Valhaven and meet <PERSON><PERSON><PERSON>", "Description": "Time to learn what the Emperor is really up to. Meet up with <PERSON><PERSON><PERSON> in Valhaven.", "OfferText": "You'll have to come with me to Valhaven to stop the Emperor and his Seelie allies.", "ActiveText": "The Emperor will be after you now.=@Your emperor seems like a scheming fiend.=He was a great man once.=Came out of nowhere and kicked out the Goblins.=Then he held off the dragons.=But in recent years he's grown cruel.=@And he seems to be in league with the Seelie.=Yes, that's true.=I didn't know how evil they were until today.=Come, we must hurry.=If we're lucky, The Emperor won't know we're coming."}, {"MissionName": "MeetWithOdinHard", "MissionID": "245", "PreReqMissions": "HeadToValhavenHard", "ZoneSet": "JadeCityHard", "Priority": "Story", "ReturnName": "VH_Odin01Hard", "CompleteCount": "0", "MissionLevel": "26", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Order of the Jester", "TrackerReturn": "Meet with <PERSON><PERSON><PERSON> at the Laughing Jester Inn", "Description": "The Empire has labled you a criminal and attacked you with demons. Meet with rebel leader <PERSON><PERSON><PERSON> to find out what's going on.", "ReturnText": "You must be the Traveller <PERSON><PERSON><PERSON> spoke of. Welcome."}, {"MissionName": "BackAlleyDealsHard", "MissionID": "246", "PreReqMissions": "MeetWithOdinHard", "ZoneSet": "JadeCityHard", "Priority": "Story", "ContactName": "VH_Odin01Hard", "CompleteCount": "1", "Dungeon": "JC_Mission2Hard", "MissionLevel": "27", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Back Alley Deals", "TrackerText": "Locate the Emperor's Defector <PERSON><PERSON>", "Description": "The Emperor has it out for you personally. One of his key mages was seen hiding out in the Alleys. Find and Question him.", "OfferText": "The Emperor knows your name and want you dead.", "ActiveText": "I wonder what you did to earn his wrath?=@I did foil his plans in Shazari.=That could not have pleased him.=But he would never admit to scheming against Shazar<PERSON>.=And he has put a public price on your head.=His son, the High Mage, will know the truth.=@Why would he tell me?=He seems to be out of favor with his father.=Find him while he's outside the palace and he might talk.=He frequents the alleys, cultivating thieves to his cause."}, {"MissionName": "TheProdigalSonHard", "MissionID": "247", "PreReqMissions": "BackAlleyDealsHard", "ZoneSet": "JadeCityHard", "Priority": "Story", "ReturnName": "VH_Odin01Hard", "CompleteCount": "1", "Dungeon": "JC_Mission3Hard", "MissionLevel": "28", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Prodigal Son", "TrackerText": "Chase Down the Emperor's Son", "TrackerReturn": "<PERSON> The Emperor is <PERSON><PERSON><PERSON>", "Description": "The Emperor is <PERSON><PERSON><PERSON>, and he thinks you're trying to steal his birth right from him.", "ReturnText": "So, the Emperor is from your world!=His origins were always a mystery.=He overthrew the Goblins and set himself up ten years ago.=Thanks to his Seelie allies, he's been unstoppable.", "PraiseText": "I can sense you're destined to do great things here=Work with <PERSON><PERSON> to find a chink in the Emperor's armor"}, {"MissionName": "SpeakWithFabMabHard", "MissionID": "248", "PreReqMissions": "ShadowsOfThePastHard", "ZoneSet": "JadeCityHard", "Priority": "Story", "ContactName": "VH_Odin01Hard", "ReturnName": "VH_FabMab01Hard", "CompleteCount": "0", "MissionLevel": "28", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Dark Origins", "TrackerText": "Speak with <PERSON><PERSON>", "TrackerReturn": "<PERSON> knows more than anyone about The Seelie", "Description": "<PERSON><PERSON> knows a thing or two about The Emperor's evil Seelie allies and where their power comes from.", "OfferText": "Now we need <PERSON>'s help figuring out what to do next.", "ActiveText": "<PERSON><PERSON><PERSON> became Emperor thanks to the <PERSON><PERSON>.=Their dark magicks protect him and secure his throne.=We need a counter to their wards.=<PERSON> knows more about <PERSON><PERSON> than anyone I know.=Talk to her and see if she has any ideas.", "ReturnText": "Hello, young man|lady"}, {"MissionName": "DELETED10", "MissionID": "249", "ZoneSet": "TOBEDELETED"}, {"MissionName": "SewerSurfinHard", "MissionID": "250", "PreReqMissions": "TheProdigalSonHard", "ZoneSet": "JadeCityHard", "Priority": "Story", "ContactName": "VH_Odin01Hard", "ReturnName": "VH_Odin01Hard", "CompleteCount": "1", "Dungeon": "JC_Mission4Hard", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Sewer Suffering", "TrackerText": "Remove the Gutter Lord from power", "TrackerReturn": "<PERSON> Odryn the Vermyn are free to fight the Emperor", "Description": "The Vermyn gang is under the Emperor's control. Take out The Gutter Lord and the rats will join the resistance.", "OfferText": "The Vermyn gangs helped The Emperor overthrow the Goblins.", "ActiveText": "I see now why the Emperor hates you.=@<PERSON><PERSON><PERSON> was a brave hero.=@He thinks I want his throne.=Do you? You took his Barony.=@He left his Barony to goblins.=Well, he's angered people here too.= The Vermyn gangs chafe under the Emperor's rule.=They would rise up against him…=Except the <PERSON>utter Lord remains loyal to the Emperor.=Go down and take out the <PERSON>utter Lord.=Then the Vermyn will join us for sure.", "ReturnText": "The Vermyn gangs are in an uproar!=With the Gutter Lord gone, some are already on our side."}, {"MissionName": "TempleOfLostDreamsHard", "MissionID": "251", "PreReqMissions": "SpeakWithFabMabHard", "ZoneSet": "JadeCityHard", "Priority": "Story", "ContactName": "VH_FabMab01Hard", "ReturnName": "VH_FabMab01Hard", "CompleteCount": "1", "Dungeon": "JC_Mission5Hard", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Fable of the Lost Temple", "TrackerText": "Recover a Seelie artifact to counter The Emperor's wards", "TrackerReturn": "Show Mab what you found in the temple", "Description": "The Emperor relies on <PERSON>lie magic for his power. Go to the Temple of Lost Dreams and find a way to counter his wards.", "OfferText": "The Emperor sold his soul to the Seelie to secure his throne.", "ActiveText": "The Seelie were the first to awake in the Sleeping Lands.=They were once wise and generous…=But time made them cruel and jealous of others.=They lost power to the Goblins and Dragons…=<PERSON><PERSON><PERSON> turned to them to help defeat the Goblin Kings.=And now they prop up his rule in Valhaven.=It takes Seelie magic to counter Seelie magic.=Go to the Temple of Lost Dreams...=There you might find something I can use to counter the Emperor's wards.", "ReturnText": "Sounds like the Temple was more dangerous than suspected.=The Emperor must have been protecting his secrets well.=Now I know a great deal more about his Seelie magic.=I should be able to get you past the outer wards of his palace.=But you will be on your own when you face him.=He is vulnerable, but still very dangerous."}, {"MissionName": "InceptionHard", "MissionID": "252", "PreReqMissions": "InterventionHard", "ZoneSet": "JadeCityHard", "Priority": "Dungeon", "ContactName": "VH_FabMab02Hard", "ReturnName": "VH_FabMab02Hard", "CompleteCount": "1", "Dungeon": "JC_Mission6Hard", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Dream Within a Dream", "TrackerText": "The Garden of Night reveals hidden truths", "TrackerReturn": "Return to <PERSON><PERSON> and tell her what you learned", "Description": "<PERSON><PERSON> thinks you will learn something important about yourself in the Garden of Night.", "OfferText": "You have left your mark on the Sleeping Lands.", "ActiveText": "Now you are a part of the fate of The Sleeping Lands.=Your actions reverberate through the world of dreams.=If you dare face your fate…=You can enter the Primal Dream and learn the truth.=@The truth about what?=The truth about yourself and your place in the world.", "ReturnText": "You have returned. I hope you learned much.", "PraiseText": "It seems a dragon was hiding in your dreams.=@I didn't even know that was possible.=You will find many strange things are possible.=You have seen but a sliver of the Sleeping Lands.=@And I aim to visit much more.=@As long as I can sleep without dragons in my mind.=You'll be lucky if that's your only problem.=This world knows you now.=And I'm not sure it means you well."}, {"MissionName": "InterventionHard", "MissionID": "253", "PreReqMissions": "AttackOfOpportunityHard", "ZoneSet": "JadeCityHard", "Priority": "Story", "ContactName": "VH_Odin01Hard", "ReturnName": "VH_Odin01Hard", "CompleteCount": "1", "Dungeon": "JC_Mission7Hard", "MissionLevel": "30", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Royal Intervention", "TrackerText": "Assault the Palace and Confront the Emperor", "TrackerReturn": "Deliver the news to <PERSON><PERSON><PERSON> that the task is done", "Description": "The only thing left to do now is take down the <PERSON> and hopefully restore some semblance of peace to Valhaven.", "OfferText": "Now for the hard part.", "ActiveText": "The Emperor cowers in his palace.=You've taken out all his support.=The people rally against him.=Emperor <PERSON><PERSON><PERSON>'s reign of terror ends today.", "ReturnText": "Liberty!", "PraiseText": "What you have done here…=I cannot thank you enough=Valhaven is free for the first time in centuries.=Goblins, Dragons, and <PERSON><PERSON> are all defeated.=At last we shall know peace."}, {"MissionName": "AttackOfOpportunityHard", "MissionID": "254", "PreReqMissions": "TempleOfLostDreamsHard", "ZoneSet": "JadeCityHard", "Priority": "Story-", "ContactName": "VH_Jackal02Hard", "ReturnName": "VH_Jackal02Hard", "CompleteCount": "3", "MissionLevel": "29", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Strategic Weakness", "TrackerText": "Take out Strategic Imperial Strongholds", "TrackerReturn": "Let <PERSON><PERSON><PERSON> know you've taken out the Emperors support.", "Description": "<PERSON><PERSON><PERSON> has given you a list of strategic targets to hit to severely hobble the Emperor's defenses.", "OfferText": "Now that you can get past the Seelie wards, it's time to strike.", "ActiveText": "Before confronting The Emperor, we need to weaken him.=Take out all his support before you confront him.=His daughters command his army and secret police.=You can find them in the East and West wings of the palace.=You also need to defeat the Imperial Guard Commander.=He lives in the barracks.", "ReturnText": "Now The Emperor's defenses are in disarray.=The time to strike has come!", "PraiseText": "You never fail to impress, #tn#."}, {"MissionName": "TheWestWingHard", "MissionID": "255", "PreReqMissions": "TempleOfLostDreamsHard", "ZoneSet": "JadeCityHard", "Priority": "Story", "CompleteCount": "1", "Dungeon": "JC_Mini1Hard", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The West Wing", "TrackerText": "Invade the Western Tower and Defeat Lotte", "Description": "The champion of their Emperor's Army resides in the western tower. If you can take her out, it will be a great set back to his cause."}, {"MissionName": "TheEastWingHard", "MissionID": "256", "PreReqMissions": "TempleOfLostDreamsHard", "ZoneSet": "JadeCityHard", "Priority": "Story", "CompleteCount": "1", "Dungeon": "JC_Mini2Hard", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The East Wing", "TrackerText": "Invade the Eastern Tower and Defeat Tanja", "Description": "The Emperor's chief spy has been spotted in the Eastern tower. Without her, the Imperial intelligence network will collapse."}, {"MissionName": "TacticalStrikeHard", "MissionID": "257", "PreReqMissions": "TempleOfLostDreamsHard", "ZoneSet": "JadeCityHard", "Priority": "Story", "CompleteCount": "1", "Dungeon": "JC_Mission8Hard", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Imperial Barracks", "TrackerText": "Strike at the Heart fo the Imperial Barracks", "Description": "The Imperial Guard Commander lives and trains in the barracks. His troops will be future champions. Take him down."}, {"MissionName": "RingOfFireHard", "MissionID": "258", "PreReqMissions": "GoSeeSkittsHard", "ZoneSet": "JadeCityHard", "Priority": "Story", "ContactName": "VH_Skitts01Hard", "ReturnName": "VH_Skitts01Hard", "CompleteCount": "1", "Dungeon": "JC_Mission11Hard", "MissionLevel": "27", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Ring of Fire", "TrackerText": "Infiltrate the human gangs", "TrackerReturn": "Talk to Skitts and plan your next move", "Description": "The human gangs won't let just anyone in. Prove your toughness and learn more about the gang's shadowy leaders.", "OfferText": "The human gangs here have high standards.", "ActiveText": "You got the Vermyn gangs on your side...=That's impressive.=But the human gangs are different.=If you want to get close to the leaders…=You're going to have to prove yourself to them.=@I'm tough enough, don't you worry.=Be ready for a brutal fight.=They'll gang up on you in that fighting pit.=@I can handle it.=@Probably.", "ReturnText": "Everyone's talkin' about you now, #tn#."}, {"MissionName": "ShadowsOfThePastHard", "MissionID": "259", "PreReqMissions": "RingOfFireHard", "ZoneSet": "JadeCityHard", "Priority": "Story", "ContactName": "VH_Skitts01Hard", "ReturnName": "VH_Odin01Hard", "CompleteCount": "1", "Dungeon": "JC_Mission9Hard", "MissionLevel": "28", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Hiding Out", "TrackerText": "Stop the gang leaders before they kill <PERSON><PERSON><PERSON>", "TrackerReturn": "Tell Odryn you've got the gangs in line", "Description": "The mysterious human gang leaders set you up! They're on their way to kill <PERSON><PERSON><PERSON> in his hideout.", "OfferText": "It was a trick! They're onto you and your rebel friends!", "ActiveText": "The gang leaders set us up.=@What are you talking about?=While you were in the ring, they were on the move.=The gangs are heading for the rebel hideout.=You have to hurry back…=Cut them off before they kill <PERSON><PERSON><PERSON>!", "ReturnText": "If you hadn't stopped the gang leaders,, I'd be dead."}, {"MissionName": "VaultHunterHard", "MissionID": "260", "PreReqMissions": "SpeakWithFabMabHard", "ZoneSet": "JadeCityHard", "Priority": "Dungeon", "CompleteCount": "1", "Dungeon": "JC_Mission10Hard", "MissionLevel": "28", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Ancient Vault", "TrackerText": "Explore the Ancient Vault on the Temple Isle", "Description": "Rumors seem to indicate there is treasure in the Vault on the Temple Isle. It just might warrant a look."}, {"MissionName": "CollectImperialInsigniasHard", "MissionID": "261", "PreReqMissions": "MeetWithOdinHard", "ZoneSet": "JadeCityHard", "Priority": "Side", "ContactName": "VH_Rebel01Hard", "ReturnName": "VH_Rebel01Hard", "CompleteCount": "40", "ProgressIcon": "a_QuestIcon_ImperialInsignia", "ProgressText": "Imperial Insignia", "MissionLevel": "28", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Men of Dishonor", "TrackerText": "Gather Imperial Insignias for Hank the Tank", "TrackerReturn": "Take the proof of your deeds back to <PERSON>", "Description": "<PERSON> the Tank used to be an Imperial before the Emperor changed. He'll reward you for the proof of fewer Imperial scums.", "OfferText": "I used to be an Imperial…", "ActiveText": "Back before the Emperor lost his mind and started associating with <PERSON><PERSON>.=I followed my conscience and it cost me everything.=Now I watch them terrorize these streets we used to protect.=I'll pay for any Imperial you take off the streets.", "ReturnText": "That's so many…Take this.", "PraiseText": "That's all the money I had left=No, Take it. You are fighting for a cause that is just=I'd trade all the gold in the world for things to return to the way they were=I guess the time for that has passed=Things will never be the same again."}, {"MissionName": "CollectStolenMushroomsHard", "MissionID": "262", "PreReqMissions": "TheProdigalSonHard", "ZoneSet": "JadeCityHard", "Priority": "Side", "ContactName": "VH_Vagrant01Hard", "ReturnName": "VH_Vagrant01Hard", "CompleteCount": "20", "ProgressIcon": "a_QuestIcon_StolenMushroom", "ProgressText": "<PERSON><PERSON>s", "MissionLevel": "28", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Mushroom Man", "TrackerText": "Retrieve the Stolen Mushrooms from the Ratlings", "TrackerReturn": "Get this crop back to The Mushroom Man", "Description": "Ratlings have been raiding The Mushroom Man's prized sewer garden. Won't you help The Mushroom Man?", "OfferText": "What's that?! Yep! They've robbed meh blind!", "ActiveText": "<PERSON><PERSON><PERSON><PERSON> raided mah garden and stole mah mushrooms.=I use those mushrooms to make stew and mah hearty mushroom pie.=With all this Imperial nonsense, people on the streets need mah shrooms to get by!=I don't wanna see anyone starve, do you?", "ReturnText": "Hoo-yeah! You found mah shrooms!", "PraiseText": "Now I can go back to baking mushroom pie=And Mushroom Gumbo=And Mushroom Stew=And Mushroom Soufflé=And Stuffed Mushroom Saffron Surprise=And Mushroom ala Kelp=And, let's see. I know there's more I can make with shrooms"}, {"MissionName": "CollectBrigandNecklacesHard", "MissionID": "263", "PreReqMissions": "MeetWithOdinHard", "ZoneSet": "JadeCityHard", "Priority": "Side", "ContactName": "VH_Rebel02Hard", "ReturnName": "VH_Rebel02Hard", "CompleteCount": "30", "ProgressIcon": "a_QuestIcon_Brigand<PERSON>ecklace", "ProgressText": "<PERSON><PERSON><PERSON>", "MissionLevel": "28", "ExpReward": "M", "GoldReward": "M", "DisplayName": "A Few Examples", "TrackerText": "Get the Brigands to find a less dangerous line of work", "TrackerReturn": "Let <PERSON> know the message has been delivered", "Description": "The Brigands are taking advantage of the chaos and running a protection racket. <PERSON> says convince them otherwise.", "OfferText": "The Imperials are bad enough, but the gangs are the worst kinds of opportunists.", "ActiveText": "They are running a protection and extortion racket.=Preying off innocent civilians that can't defend themselves.=I think it's time someone showed them that their days are finished.", "ReturnText": "Thank you traveler.", "PraiseText": "That is one less threat the people have to worry about."}, {"MissionName": "CollectDemonTearsHard", "MissionID": "264", "PreReqMissions": "InceptionHard", "ZoneSet": "JadeCityHard", "Priority": "Side", "ContactName": "VH_Monk01Hard", "ReturnName": "VH_Monk01Hard", "CompleteCount": "40", "ProgressIcon": "a_QuestIcon_DemonTear", "ProgressText": "Demon Tear", "MissionLevel": "29", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Devil Might Cry", "TrackerText": "Gather Demon Tears for Odo", "TrackerReturn": "Return the tears to <PERSON><PERSON>", "Description": "<PERSON><PERSON> says the Aura of the city has been tainted by the Imperial's demons. He can cleanse the Aura with their demonic tears.", "OfferText": "It is a tragedy what the Emperor has done here, but all is not lost", "ActiveText": "I can feel the city crying out from beneath the weight of Seelie filth that has tainted it.=As venom is key to making anti-venom, demon tears are key to cleanse Valhaven's aura.", "ReturnText": "Bless you, friend.", "PraiseText": "I will begin the long process of setting things right…=It gladens my heart to meet a noble soul such as you"}, {"MissionName": "GoSeeGladiator", "MissionID": "265", "PreReqMissions": "ScarabInvasion", "ZoneSet": "ShazariDesert", "Priority": "Story", "ContactName": "SD_Mayor01", "ReturnName": "SD_Gladiator01", "CompleteCount": "0", "MissionLevel": "24", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Speak with <PERSON><PERSON><PERSON><PERSON>", "TrackerText": "Speak with <PERSON><PERSON><PERSON><PERSON>, an escaped Gladiator", "TrackerReturn": "The last of the dragon's slaves are rising in revolt.", "Description": "The Pit Lord <PERSON> is the last remaining dragon in Shazari desert. Help end his reign of terror.", "OfferText": "You defeated Grand Marshal <PERSON><PERSON><PERSON><PERSON>, but one dragon remains.", "ActiveText": "The dragon <PERSON><PERSON><PERSON><PERSON><PERSON> ruled this land for centuries.=@He and his legion are dust and bones now.=Thanks to you.=@Thanks to me.=But one dragon remains, The Pit Lord.=He runs the Arena, where slaves fight for his pleasure.=@I think it's time I met this Pit Lord.=Go see <PERSON><PERSON><PERSON><PERSON>, he used to fight in the arena.=He can tell you more.", "ReturnText": "Stay alert stranger, the Pit Lord's men are everywhere."}, {"MissionName": "GoSee<PERSON><PERSON>lin", "MissionID": "266", "PreReqMissions": "BloodAndSand", "ZoneSet": "ShazariDesert", "Priority": "Story", "ContactName": "SD_Gladiator01", "ReturnName": "SD_Chief01", "CompleteCount": "0", "MissionLevel": "24", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Goblin Rapprochement", "TrackerText": "Talk with the allegedly nice goblins.", "TrackerReturn": "See if these goblins really are peaceful and in need of aid", "Description": "Overcome your doubts and speak to the goblin elder. He might not be behind the evil in Shazari. Maybe.", "OfferText": "I've heard that the goblins might know who has been causing our recent troubles.", "ActiveText": "You should talk to the goblin elder.=@Goblins! They cause nothing but trouble.=@If there's a war party nearby, I'll see to them.=No, friend, no!=These goblins are peaceful.=@Goblins don't know the word peace.=These do, trust me.=Speak to them if you want to learn the true source of evil here.=@I shall reserve judgement.=@But I wager they're all the cause we will find.", "ReturnText": "Ahh, welcome, friend! Thank you for coming."}, {"MissionName": "GoSeeGladiatorHard", "MissionID": "267", "PreReqMissions": "ScarabInvasionHard", "ZoneSet": "ShazariDesertHard", "Priority": "Story", "ContactName": "SD_Mayor01Hard", "ReturnName": "SD_Gladiator01Hard", "CompleteCount": "0", "MissionLevel": "24", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Speak with <PERSON><PERSON><PERSON><PERSON>", "TrackerText": "Speak with <PERSON><PERSON><PERSON><PERSON>, an escaped Gladiator", "TrackerReturn": "The last of the dragon's slaves are rising in revolt.", "Description": "The Pit Lord <PERSON> is the last remaining dragon in Shazari desert. Help end his reign of terror.", "OfferText": "You defeated Grand Marshal <PERSON><PERSON><PERSON><PERSON>, but one dragon remains.", "ActiveText": "The dragon <PERSON><PERSON><PERSON><PERSON><PERSON> ruled this land for centuries.=@He and his legion are dust and bones now.=Thanks to you.=@Thanks to me.=But one dragon remains, The Pit Lord.=He runs the Arena, where slaves fight for his pleasure.=@I think it's time I met this Pit Lord.=Go see <PERSON><PERSON><PERSON><PERSON>, he used to fight in the arena.=He can tell you more.", "ReturnText": "Stay alert stranger, the Pit Lord's men are everywhere."}, {"MissionName": "GoSeeGoblinHard", "MissionID": "268", "PreReqMissions": "BloodAndSandHard", "ZoneSet": "ShazariDesertHard", "Priority": "Story", "ContactName": "SD_Gladiator01Hard", "ReturnName": "SD_Chief<PERSON><PERSON><PERSON>", "CompleteCount": "0", "MissionLevel": "24", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Goblin Rapprochement", "TrackerText": "Talk with the allegedly nice goblins.", "TrackerReturn": "See if these goblins really are peaceful and in need of aid", "Description": "Overcome your doubts and speak to the goblin elder. He might not be behind the evil in Shazari. Maybe.", "OfferText": "I've heard that the goblins might know who has been causing our recent troubles.", "ActiveText": "You should talk to the goblin elder.=@Goblins! They cause nothing but trouble.=@If there's a war party nearby, I'll see to them.=No, friend, no!=These goblins are peaceful.=@Goblins don't know the word peace.=These do, trust me.=Speak to them if you want to learn the true source of evil here.=@I shall reserve judgement.=@But I wager they're all the cause we will find.", "ReturnText": "Ahh, welcome, friend! Thank you for coming."}, {"MissionName": "GoSeeSkitts", "MissionID": "269", "PreReqMissions": "SewerSurfin", "ZoneSet": "JadeCity", "Priority": "Story", "ContactName": "VH_Odin01", "ReturnName": "VH_Skitts01", "CompleteCount": "0", "MissionLevel": "28", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Speak with <PERSON><PERSON>", "TrackerText": "<PERSON><PERSON> has a contact in the gangs", "TrackerReturn": "Contact Skitts and find out how to infiltrate the gangs", "Description": "<PERSON><PERSON> is a shady guy, but his underworld contacts can help the rebels win the human gangs over to your cause.", "OfferText": "This city's gangs are mostly human and are loyal to the Emperor.", "ActiveText": "But I hear from <PERSON><PERSON> they might be won over.=Humans usually support The Emperor.=But he relies too much on The Seelie now.=The human gangs will fight him…=If we can knock out their leaders.=But I don't know who the leaders are.=My contact <PERSON><PERSON> will know.=Go see what he suggests.", "ReturnText": "So you're the troublemaker, eh?"}, {"MissionName": "GoSeeSkittsHard", "MissionID": "270", "PreReqMissions": "SewerSurfinHard", "ZoneSet": "JadeCityHard", "Priority": "Story", "ContactName": "VH_Odin01Hard", "ReturnName": "VH_Skitts01Hard", "CompleteCount": "0", "MissionLevel": "28", "ExpReward": "L", "GoldReward": "L", "DisplayName": "Speak with <PERSON><PERSON>", "TrackerText": "<PERSON><PERSON> has a contact in the gangs", "TrackerReturn": "Contact Skitts and find out how to infiltrate the gangs", "Description": "<PERSON><PERSON> is a shady guy, but his underworld contacts can help the rebels win the human gangs over to your cause.", "OfferText": "This city's gangs are mostly human and are loyal to the Emperor.", "ActiveText": "But I hear from <PERSON><PERSON> they might be won over.=Humans usually support The Emperor.=But he relies too much on The Seelie now.=The human gangs will fight him…=If we can knock out their leaders.=But I don't know who the leaders are.=My contact <PERSON><PERSON> will know.=Go see what he suggests.", "ReturnText": "So you're the troublemaker, eh?"}, {"MissionName": "GoblinRiver", "MissionID": "271", "PreReqMissions": "ClearYourHouse", "ZoneSet": "NewbieRoad", "Priority": "Story", "ContactName": "AnnaOutside", "ReturnName": "NR_QuestAnna01", "CompleteCount": "1", "Dungeon": "GoblinRiverDungeon", "MissionLevel": "3", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Last of the Goblins", "TrackerText": "Follow the river to the Goblin Encampment", "TrackerReturn": "Let <PERSON> know the Goblins are finished in Wolf's End.", "Description": "<PERSON> knows where the Goblin camp is, but Wolf's End hasn't had the strength to confront them. Do you?", "OfferText": "I know where the Goblin Chief's camp is.", "ActiveText": "The goblins are hiding on the other side of the forest.=@What're they doing there?=Hiding, like I said.=The goblins are running scared.=But there are way more of them than we can handle.=Especially with all these undead suddenly rising up.=@I'll get to the undead soon.=@But I have goblin slaying to attend to first.=Head through the woods and follow the river.=You'll find your fill of goblins there.=Once you finish I'll catch up.", "ReturnText": "You beat the big bad goblin? Nice work, champ!", "PraiseText": "With the Goblin Chief cut down, we can reclaim this land.=That piece of property by the river is lovely.=Maybe I'll build a house there.=Raise some sheep or something.=Or start an adventurer's guild.=Or maybe an inn.=It would be a nice place for a little keep.=I could be a Baroness.=Why not?"}, {"MissionName": "GoblinRiverHard", "MissionID": "272", "PreReqMissions": "RescueAnnaHard", "ZoneSet": "NewbieRoadHard", "Priority": "Story", "ContactName": "AnnaOutsideHard", "ReturnName": "NR_QuestAnna01Hard", "CompleteCount": "1", "Dungeon": "GoblinRiverDungeonHard", "MissionLevel": "3", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Last of the Goblins", "TrackerText": "Follow the river to the Goblin Encampment", "TrackerReturn": "Let <PERSON> know the Goblins are finished in Wolf's End.", "Description": "<PERSON> knows where the Goblin camp is, but Wolf's End hasn't had the strength to confront them. Do you?", "OfferText": "I know where the Goblin Chief's camp is.", "ActiveText": "The goblins are hiding on the other side of the forest.=@What're they doing there?=Hiding, like I said.=The goblins are running scared.=But there are way more of them than we can handle.=Especially with all these undead suddenly rising up.=@I'll get to the undead soon.=@But I have goblin slaying to attend to first.=Head through the woods and follow the river.=You'll find your fill of goblins there.=Once you finish I'll catch up.", "ReturnText": "You beat the big bad goblin? Nice work, champ!", "PraiseText": "With the Goblin Chief cut down, we can reclaim this land.=That piece of property by the river is lovely.=Maybe I'll build a house there.=Raise some sheep or something.=Or start an adventurer's guild.=Or maybe an inn.=It would be a nice place for a little keep.=I could be a Baroness.=Why not?"}, {"MissionName": "MeylourFinale", "MissionID": "273", "PreReqMissions": "RefugeOfTheDamned", "ZoneSet": "EmeraldGlades,BridgeTown", "Priority": "Story", "ContactName": "EG_Mayor01", "ReturnName": "BT_Warden", "CompleteCount": "1", "Dungeon": "OMM_Mission12", "MissionLevel": "21", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Death to <PERSON><PERSON><PERSON>", "TrackerText": "Go to the mountain's core and destroy <PERSON><PERSON><PERSON>", "TrackerReturn": "Go see if <PERSON><PERSON><PERSON>'s barrier is truly dispelled.", "Description": "You've discovered where <PERSON><PERSON><PERSON> himself hides. Fight your way to the mountain's core and destroy the evil titan.", "OfferText": "<PERSON><PERSON><PERSON> will not rest until all life in Ellyria burns.", "ActiveText": "The <PERSON><PERSON><PERSON> ritual is disrupted?=@It is. I need your help finding <PERSON><PERSON><PERSON> himself.=<PERSON><PERSON><PERSON>'s foul magic leaves a trace.=The cult tried to unleash his power.=Now I can help you follow that power to its source.=Follow the trail deep into the mountain's heart.=There you'll find <PERSON><PERSON><PERSON>.=@There I will destroy <PERSON><PERSON><PERSON>.", "ReturnText": "The magic barrier that sealed Castle Hocke is dispelled."}, {"MissionName": "MeylourFinaleHard", "MissionID": "274", "PreReqMissions": "RefugeOfTheDamnedHard", "ZoneSet": "EmeraldGladesHard,BridgeTownHard", "Priority": "Story", "ContactName": "EG_Mayor01Hard", "ReturnName": "BT_WardenHard", "CompleteCount": "1", "Dungeon": "OMM_Mission12Hard", "MissionLevel": "21", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Death to <PERSON><PERSON><PERSON>", "TrackerText": "Go to the mountain's core and destroy <PERSON><PERSON><PERSON>", "TrackerReturn": "Go see if <PERSON><PERSON><PERSON>'s barrier is truly dispelled.", "Description": "You've discovered where <PERSON><PERSON><PERSON> himself hides. Fight your way to the mountain's core and destroy the evil titan.", "OfferText": "<PERSON><PERSON><PERSON> will not rest until all life in Ellyria burns.", "ActiveText": "The <PERSON><PERSON><PERSON> ritual is disrupted?=@It is. I need your help finding <PERSON><PERSON><PERSON> himself.=<PERSON><PERSON><PERSON>'s foul magic leaves a trace.=The cult tried to unleash his power.=Now I can help you follow that power to its source.=Follow the trail deep into the mountain's heart.=There you'll find <PERSON><PERSON><PERSON>.=@There I will destroy <PERSON><PERSON><PERSON>.", "ReturnText": "The magic barrier that sealed Castle Hocke is dispelled."}, {"MissionName": "NRTales1Keep", "MissionID": "275", "PreReqMissions": "NRTales1Keep", "ZoneSet": "NewbieRoad", "Priority": "Dungeon", "ContactName": "KP_Steward", "ReturnName": "KP_Steward", "CompleteCount": "1", "Dungeon": "NR_Tales1Keep", "MissionLevel": "31", "ExpReward": "L", "GoldReward": "L", "DisplayName": "With Friends Like These", "TrackerText": "Recapture the escaped spy", "TrackerReturn": "Tell the steward you’ve captured the spy. Again.", "Description": "The hero <PERSON><PERSON> accidentally helped a spy escape his cell. You need to recapture him.", "OfferText": "We caught a spy in your keep. But he escaped.", "ActiveText": "A spy was hiding in your keep.=@What spy? From where?=We didn’t have a chance to find out.=@How did he escape?= I think you should ask <PERSON><PERSON>.=@Who in the world is <PERSON><PERSON>?= A supposed hero.=He was questioning the prisoner.=@I’ll have a word with him.", "ReturnText": "The spy is secure in jail once more.", "PraiseText": "We still don’t know where he came from.=@Keep questioning him.=@I need to learn who these new enemies are."}, {"MissionName": "NRTales2Anna", "MissionID": "276", "PreReqMissions": "NRTales1Keep", "ZoneSet": "NewbieRoad", "Priority": "Dungeon", "ContactName": "AnnaOutside", "ReturnName": "AnnaOutside", "CompleteCount": "1", "Dungeon": "NR_Tales2Anna", "MissionLevel": "31", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Dinner Party", "TrackerText": "Go to <PERSON>’s new house for a good, home-cooked meal.", "TrackerReturn": "Tell <PERSON> about the strangers using devourers as weapons.", "Description": "<PERSON> has built a house where the goblin camp was, and she’s invited you to dinner.", "OfferText": "My new house is finished, you should come to dinner.", "ActiveText": "I built a new house by the waterfall. =@You mean where the goblin camp was? =Yes! But I cleaned out all the goblin nastiness.=@It sounds nice, I’d love to see it.=Come on over whenever.=@I'll bring some honey mead.", "ReturnText": "It's going to take weeks to get rid of these weeds.", "PraiseText": "Where did the devourers come from?@There was some stranger there.=@He’d turned the devourers into weapons.=Sounds like someone’s raiding Hsalt’s secret labs.=@I’ll need to be on the look out for these spies."}, {"MissionName": "NRTales3Bazaar", "MissionID": "277", "PreReqMissions": "NRTales1Keep", "ZoneSet": "NewbieRoad", "Priority": "Dungeon", "ContactName": "NR_Mayor01", "ReturnName": "NR_Mayor01", "CompleteCount": "1", "Dungeon": "NR_Tales3Bazaar", "MissionLevel": "31", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Beach Bazaar", "TrackerText": "Investigate the goblin merchants", "TrackerReturn": "That’s TWO krakens you’ve slain.", "Description": "Shazari goblins have set up a store in the smuggler’s cave. Make sure they’re not up to no good.", "OfferText": "Some goblin merchants have moved into the smuggler’s cave.", "ActiveText": "They say they’re friendly goblin merchants.=@I know it sounds strange...=@But there are friendly goblins.=Well, I don’t trust them.=Can you go see what they’re doing in that cave?=@Trust me, there really are nice goblins.=@But if it makes you feel better...=@I will check in on them.=Hmm. Careful.=They’re tricky.", "ReturnText": "There really are nice goblins? Huh.", "PraiseText": "I suppose I’ll learn to like them.=@They’re much easier to like than krakens.=@And I took care of the last kraken.=That’s what you said last time.=@This time I’m sure it was the last one.=You’re positive it’s the last Kraken?=@Well, I’m pretty sure it was.=@Probably."}, {"MissionName": "NRTales4Research", "MissionID": "278", "PreReqMissions": "NRTales1Keep", "ZoneSet": "NewbieRoad", "Priority": "Dungeon", "ContactName": "NR_Researcher", "ReturnName": "NR_Researcher", "CompleteCount": "1", "Dungeon": "NR_Tales4Research", "MissionLevel": "31", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Lost Library", "TrackerText": "We’ve lost contact with the savants exploring <PERSON><PERSON><PERSON>’s tomb.", "TrackerReturn": "The Lost Library isn’t lost anymore.", "Description": "Savants from the king are excavating <PERSON><PERSON><PERSON>’s tomb, but no one has heard from them in days. They could be in trouble.", "OfferText": "My fellow savants have lost contact, I fear the worst.", "ActiveText": "The King sent me and my fellow savants to research <PERSON><PERSON><PERSON>.=@That’s a dangerous subject.=I fear you are correct.=I’ve lost contact with my comrades.=They were excavating <PERSON><PERSON><PERSON>’s tomb.=@I know it well.=@Better let me take a look.=@If we’re lucky they discovered something great...=@And are too busy to let you know.=Perhaps that’s the case.=Perhaps not.", "ReturnText": "What a grand discovery!", "PraiseText": "This ancient library will reveal wonders, I’m sure.=@It seems we’re not the only ones interested in it.=@These spies are cropping up everywhere."}, {"MissionName": "NRTales5DreamCave", "MissionID": "279", "PreReqMissions": "NRTales4Research", "ZoneSet": "NewbieRoad", "Priority": "Dungeon", "ContactName": "NR_Researcher", "ReturnName": "NR_Researcher", "CompleteCount": "1", "Dungeon": "NR_Tales5DreamCave", "MissionLevel": "31", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Wall of Sleep", "TrackerText": "Discover what lies beyond the Dream Dragon’s lair", "TrackerReturn": "Tell the savant that some secrets are best left buried.", "Description": "The king’s savants have discovered a secret chamber beyond the Dream Dragon’s lair.", "OfferText": "We found a map in the Lost Library.", "ActiveText": "My fellow savants discovered something in the library.=There’s some sort of hidden chamber in the dream tomb.=@Where I defeated <PERSON><PERSON><PERSON><PERSON><PERSON>’s dream form?=Yes, we’ve begun excavations.=You might be interested in what they find.=@I better go and make sure they don’t find anything dangerous.=@Your curiosity will get you savants killed.=Not as long as you’re here to protect us!=@Someday you’ll need to protect yourselves.", "ReturnText": "I can’t sleep. These nightmares…", "PraiseText": "All of the savants are plagued by nightmares.=@It will pass.=@I defeated the dream hordes.=@But you savants need to steer clear of such dangers.=Maybe we should stick to our books."}, {"MissionName": "SDTales0GetStarted", "MissionID": "280", "PreReqMissions": "NRTales5DreamCave", "ZoneSet": "ShazariDesert", "Priority": "Story", "ContactName": "NR_Mayor01", "ReturnName": "SD_OgreMagus", "CompleteCount": "0", "MissionLevel": "32", "ExpReward": "L", "GoldReward": "L", "DisplayName": "TODO", "TrackerText": "GOTO SHAZARI", "Description": "GOTO SHAZARI", "OfferText": "TODO", "ActiveText": "TODO", "ReturnText": "TODO", "PraiseText": "TODO"}, {"MissionName": "SDTales1Escort", "MissionID": "281", "PreReqMissions": "SDTales0GetStarted", "ZoneSet": "ShazariDesert", "Priority": "Dungeon", "ContactName": "SD_OgreMagus", "ReturnName": "SD_OgreMagus", "CompleteCount": "1", "Dungeon": "SD_Tales1Escort", "MissionLevel": "32", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Kings Dead and Gone", "TrackerText": "Escort the magus through his ancestor’s tomb", "TrackerReturn": "Collect your reward from the magus.", "Description": "A friendly ogre mage needs help retrieving a family heirloom from a tomb.", "OfferText": "You’re the big tomb despoiler, aren’t you?", "ActiveText": "You desecrated the Tomb of the <PERSON><PERSON><PERSON><PERSON>. =@I defeated the evil dwelling in there, yes.= That was my family’s tomb you stomped through.=@The monsters defiled it, not me.=Maybe so, but I need to get in there.=But those monsters are back.=Escort me to the sarcophagus and I’ll reward you.", "ReturnText": "You were a little helpful, I suppose.", "PraiseText": "What do you want?=@You’d be dead if it weren’t for me!= But I wouldn’t have been there if it weren’t for you.=@I wouldn’t have been there if you hadn’t...=Let’s not argue about what you did wrong.=@Now listen here...=Here’s your reward.=Go on, enjoy it.=Move along youngling.=I’ve got more important things to do."}, {"MissionName": "SDTales2Surprise", "MissionID": "282", "PreReqMissions": "SDTales0GetStarted", "ZoneSet": "ShazariDesert", "Priority": "Dungeon", "ContactName": "SD_MeanG<PERSON>lin", "ReturnName": "SD_MeanG<PERSON>lin", "CompleteCount": "1", "Dungeon": "SD_Tales2Surprise", "MissionLevel": "32", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Honors and Adulations", "TrackerText": "The goblins want to thank you for your good work", "TrackerReturn": "See if that nasty trog is still hanging around.", "Description": "The goblins want to show you their appreciation for a job well done.", "OfferText": "You’ve done so much good, we want to throw you a party.", "ActiveText": "Defeating the bad, weak goblins...=Slaying the dragons...=You’re the best hero ever!=@Thank you for saying so.=We goblins really appreciate you.=@I never thought I’d hear a goblin say that.=We want to throw you a party.=We’ve got sweets and honey mead.=Just head downstairs.=@That’s very kind of you!", "ReturnText": "You! You’re alive.", "PraiseText": "Still alive, huh?=@No thanks to you, trog!=I surrender!=It was just a joke!=Please don’t kill me!=@I’ll spare you, but...=@You’ll be in a cell for a long, long time."}, {"MissionName": "SDTales3Remodel", "MissionID": "283", "PreReqMissions": "SDTales0GetStarted", "ZoneSet": "ShazariDesert", "Priority": "Dungeon", "ContactName": "SD_NiceGoblin", "ReturnName": "SD_NiceGoblin", "CompleteCount": "1", "Dungeon": "SD_Tales3Remodel", "MissionLevel": "32", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Everything Must Go", "TrackerText": "The old arena is now a bazaar with a pest problem", "TrackerReturn": "Let the entrepreneur know it’s all clear.", "Description": "The old arena is now a goblin bazaar, but they’re having issues with the old residents.", "OfferText": "We’re having a monstrous pest problem in the bazaar.", "ActiveText": "Can you help us get the shops open again?=@You turned the arena into a shopping bazaar?=Rugs and candied fruits instead of blood and guts.=@That sounds much nicer, good work!=It was nicer, but there are pests everywhere.=@What kind of pests?=Giant spiders, bats, flying imps.=Please, help us get back to business!=@This place has seen too much death already.=@I’ll set things back on course for you.", "ReturnText": "Many thanks! For you, half off everything!", "PraiseText": "For you, half off everything!@Thank you.=@I do love candied fruits.=You must try the fresh figs.=@Send a case to my keep."}, {"MissionName": "SDTales4Oasis", "MissionID": "284", "PreReqMissions": "SDTales0GetStarted", "ZoneSet": "ShazariDesert", "Priority": "Dungeon", "ContactName": "SD_Mayor01", "ReturnName": "SD_Mayor01", "CompleteCount": "1", "Dungeon": "SD_Tales4Oasis", "MissionLevel": "32", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Water Wars", "TrackerText": "Find out what’s happening to all the water.", "TrackerReturn": "Let the people know that rain is coming soon.", "Description": "Someone is stealing water from the oasis. Find out who’s behind this dry spell.", "OfferText": "Someone is destroying our plans to revive the desert", "ActiveText": "We’ve been using  water from the Ogre Magi oasis to revive Shazari.=@Very clever and generous of you.=We were successfully undoing the damage the dragons wrought.=But now someone is siphoning off our water.=And there are carnivorous plants everywhere!=@Devourers in the desert?=@Don’t worry, I know how to take care of them.", "ReturnText": "The water’s flowing again, thank you!", "PraiseText": "Thank you!=@For now your problem is solved.=@But there’s a water titan out there looking to cause trouble.=What’s a water titan?=@If it’s anything like <PERSON><PERSON><PERSON>, you don’t want to meet it.=@Let me know if you hear any more titan talk."}, {"MissionName": "SDTales5Defense", "MissionID": "285", "PreReqMissions": "SDTales0GetStarted", "ZoneSet": "ShazariDesert", "Priority": "Dungeon", "ContactName": "SD_Sage01", "ReturnName": "SD_Sage01", "CompleteCount": "1", "Dungeon": "SD_Tales5Defense", "MissionLevel": "32", "ExpReward": "M", "GoldReward": "M", "DisplayName": "The Desert Rises", "TrackerText": "Settlers in the desert are being swarmed by monsters.", "TrackerReturn": "Tell SOMEONE the settlers are safe.", "Description": "Shazari settlers are exploring new lands, but have come under siege by monsters.", "OfferText": "Some of my brethren in the desert need help!", "ActiveText": "A group of settlers went into the desert two months ago.=They hope to open trade with Bitterfrost.=They found a good site for a caravanserai...=But now monsters besiege them from all sides.=@Trade with Bitterfrost would benefit all of us.=@I’ll help them secure their settlement.=@Plus, I really do hate monsters.", "ReturnText": "The caravanserai is secure.", "PraiseText": "Many thanks for your help.=@I look forward to learning more about Bitterfrost.=Their smoked fish is said to be exquisite.=@I’m more interested in tales of their gem mines."}, {"MissionName": "SDTales6Time", "MissionID": "286", "PreReqMissions": "SDTales0GetStarted", "ZoneSet": "ShazariDesert", "Priority": "Dungeon", "ContactName": "SD_Savant", "ReturnName": "SD_Savant", "CompleteCount": "1", "Dungeon": "SD_Tales6Time", "MissionLevel": "32", "ExpReward": "M", "GoldReward": "M", "DisplayName": "Times Gone By", "TrackerText": "<PERSON> is in the construct pyramid, doing something strange.", "TrackerReturn": "Let the savant know <PERSON> has things under control.", "Description": "The wizard <PERSON> has lost control of the situation inside the construct pyramid.", "OfferText": "Your friend <PERSON> seems to be having problems in his pyramid.", "ActiveText": "The King sent me to study with <PERSON>.=He has made great discoveries in the construct pyramid.=But I fear he might be losing control.=Or even losing his mind.=@What’s wrong with him?=He has lost himself in time.=You should go visit him, you’ll see.=@I will pay <PERSON> a visit at once.", "ReturnText": "<PERSON> has time under control again?", "PraiseText": "Titus is back to normal?=@I hope so.=@That pyramid is a gate to other eras.=@I'm not sure it’s safe for anyone."}, {"MissionName": "ACTales0GetStarted", "MissionID": "287", "PreReqMissions": "SDTales6Time", "ZoneSet": "Castle", "Priority": "Story", "ContactName": "SD_Savant", "ReturnName": "AC_Mayor01", "CompleteCount": "0", "MissionLevel": "33", "ExpReward": "L", "GoldReward": "L", "DisplayName": "TODO", "TrackerText": "GOTO THE CASTLE", "Description": "GOTO THE CASTLE", "OfferText": "TODO", "ActiveText": "TODO", "ReturnText": "TODO", "PraiseText": "TODO"}, {"MissionName": "ACTales1Rivals", "MissionID": "288", "PreReqMissions": "ACTales0GetStarted", "ZoneSet": "Castle", "Priority": "Dungeon", "ContactName": "AC_Mayor01", "ReturnName": "AC_Mayor01", "CompleteCount": "1", "Dungeon": "AC_Tales1Rivals", "MissionLevel": "33", "ExpReward": "M", "GoldReward": "M", "DisplayName": "TODO", "TrackerText": "TODO", "TrackerReturn": "TODO", "Description": "TODO", "OfferText": "TODO", "ActiveText": "TODO", "ReturnText": "TODO", "PraiseText": "TODO"}, {"MissionName": "ACTales2Ghosts", "MissionID": "289", "PreReqMissions": "ACTales0GetStarted", "ZoneSet": "Castle", "Priority": "Dungeon", "ContactName": "AC_Mayor01", "ReturnName": "AC_Mayor01", "CompleteCount": "1", "Dungeon": "AC_Tales2Ghosts", "MissionLevel": "33", "ExpReward": "M", "GoldReward": "M", "DisplayName": "TODO", "TrackerText": "TODO", "TrackerReturn": "TODO", "Description": "TODO", "OfferText": "TODO", "ActiveText": "TODO", "ReturnText": "TODO", "PraiseText": "TODO"}, {"MissionName": "ACTales3Refugees", "MissionID": "290", "PreReqMissions": "ACTales0GetStarted", "ZoneSet": "Castle", "Priority": "Dungeon", "ContactName": "AC_Mayor01", "ReturnName": "AC_Mayor01", "CompleteCount": "1", "Dungeon": "AC_Tales3Refugees", "MissionLevel": "33", "ExpReward": "M", "GoldReward": "M", "DisplayName": "TODO", "TrackerText": "TODO", "TrackerReturn": "TODO", "Description": "TODO", "OfferText": "TODO", "ActiveText": "TODO", "ReturnText": "TODO", "PraiseText": "TODO"}, {"MissionName": "ACTales4Secrets", "MissionID": "291", "PreReqMissions": "ACTales0GetStarted", "ZoneSet": "Castle", "Priority": "Dungeon", "ContactName": "AC_Mayor01", "ReturnName": "AC_Mayor01", "CompleteCount": "1", "Dungeon": "AC_Tales4Secrets", "MissionLevel": "33", "ExpReward": "M", "GoldReward": "M", "DisplayName": "TODO", "TrackerText": "TODO", "TrackerReturn": "TODO", "Description": "TODO", "OfferText": "TODO", "ActiveText": "TODO", "ReturnText": "TODO", "PraiseText": "TODO"}, {"MissionName": "ACTales5Fued", "MissionID": "292", "PreReqMissions": "ACTales0GetStarted", "ZoneSet": "Castle", "Priority": "Dungeon", "ContactName": "AC_Mayor01", "ReturnName": "AC_Mayor01", "CompleteCount": "1", "Dungeon": "AC_Tales5Feud", "MissionLevel": "33", "ExpReward": "M", "GoldReward": "M", "DisplayName": "TODO", "TrackerText": "TODO", "TrackerReturn": "TODO", "Description": "TODO", "OfferText": "TODO", "ActiveText": "TODO", "ReturnText": "TODO", "PraiseText": "TODO"}, {"MissionName": "ACTales6Embassy", "MissionID": "293", "PreReqMissions": "ACTales0GetStarted", "ZoneSet": "Castle", "Priority": "Dungeon", "ContactName": "AC_Mayor01", "ReturnName": "AC_Mayor01", "CompleteCount": "1", "Dungeon": "AC_Tales5Embassy", "MissionLevel": "33", "ExpReward": "M", "GoldReward": "M", "DisplayName": "TODO", "TrackerText": "TODO", "TrackerReturn": "TODO", "Description": "TODO", "OfferText": "TODO", "ActiveText": "TODO", "ReturnText": "TODO", "PraiseText": "TODO"}]
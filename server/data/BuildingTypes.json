[{"BuildingName": "<PERSON><PERSON>", "BuildingID": "1", "DisplayName": "<PERSON><PERSON> of Power", "Type": "<PERSON><PERSON>", "Rank": "0", "PlayerLevel": "0", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "0", "Art": "a_Upgrade_Tome_0", "BackgroundArt": "a_Upgrade_Tome_1", "UpgradeIcon": "a_TomeUpgradeIcon", "UpgradeDescription": "Nothing"}, {"BuildingName": "<PERSON><PERSON>", "BuildingID": "1", "DisplayName": "Tome of Power Level 1", "Type": "<PERSON><PERSON>", "Rank": "1", "PlayerLevel": "1", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "45", "Art": "a_Upgrade_Tome_1", "BackgroundArt": "a_Upgrade_Tome_1", "UpgradeIcon": "a_TomeUpgradeIcon", "UpgradeDescription": "Unlocks all Rank 2 abilities for training"}, {"BuildingName": "<PERSON><PERSON>", "BuildingID": "1", "DisplayName": "Tome of Power Level 2", "Type": "<PERSON><PERSON>", "Rank": "2", "PlayerLevel": "5", "GoldCost": "5872", "IdolCost": "3", "UpgradeTime": "14400", "Art": "a_Upgrade_Tome_2", "BackgroundArt": "a_Upgrade_Tome_1", "UpgradeIcon": "a_TomeUpgradeIcon", "UpgradeDescription": "Unlocks all Rank 3 abilities for training"}, {"BuildingName": "<PERSON><PERSON>", "BuildingID": "1", "DisplayName": "Tome of Power Level 3", "Type": "<PERSON><PERSON>", "Rank": "3", "PlayerLevel": "10", "GoldCost": "32867", "IdolCost": "17", "UpgradeTime": "49200", "Art": "a_Upgrade_Tome_3", "BackgroundArt": "a_Upgrade_Tome_1", "UpgradeIcon": "a_TomeUpgradeIcon", "UpgradeDescription": "Unlocks all Rank 4 abilities for training"}, {"BuildingName": "<PERSON><PERSON>", "BuildingID": "1", "DisplayName": "Tome of Power Level 4", "Type": "<PERSON><PERSON>", "Rank": "4", "PlayerLevel": "15", "GoldCost": "91888", "IdolCost": "39", "UpgradeTime": "142800", "Art": "a_Upgrade_Tome_4", "BackgroundArt": "a_Upgrade_Tome_1", "UpgradeIcon": "a_TomeUpgradeIcon", "UpgradeDescription": "Unlocks all Rank 5 abilities for training"}, {"BuildingName": "<PERSON><PERSON>", "BuildingID": "1", "DisplayName": "<PERSON><PERSON> of Power Level 5", "Type": "<PERSON><PERSON>", "Rank": "5", "PlayerLevel": "20", "GoldCost": "209646", "IdolCost": "55", "UpgradeTime": "259200", "Art": "a_Upgrade_Tome_5", "BackgroundArt": "a_Upgrade_Tome_1", "UpgradeIcon": "a_TomeUpgradeIcon", "UpgradeDescription": "Unlocks all Rank 6 abilities for training"}, {"BuildingName": "<PERSON><PERSON>", "BuildingID": "1", "DisplayName": "<PERSON><PERSON> of Power Level 6", "Type": "<PERSON><PERSON>", "Rank": "6", "PlayerLevel": "25", "GoldCost": "432172", "IdolCost": "85", "UpgradeTime": "343800", "Art": "a_Upgrade_Tome_6", "BackgroundArt": "a_Upgrade_Tome_1", "UpgradeIcon": "a_TomeUpgradeIcon", "UpgradeDescription": "Unlocks all Rank 7 abilities for training"}, {"BuildingName": "<PERSON><PERSON>", "BuildingID": "1", "DisplayName": "<PERSON>e of Power Level 7", "Type": "<PERSON><PERSON>", "Rank": "7", "PlayerLevel": "30", "GoldCost": "835058", "IdolCost": "123", "UpgradeTime": "439200", "Art": "a_Upgrade_Tome_7", "BackgroundArt": "a_Upgrade_Tome_1", "UpgradeIcon": "a_TomeUpgradeIcon", "UpgradeDescription": "Unlocks all Rank 8 abilities for training"}, {"BuildingName": "<PERSON><PERSON>", "BuildingID": "1", "DisplayName": "Tome of Power Level 8", "Type": "<PERSON><PERSON>", "Rank": "8", "PlayerLevel": "35", "GoldCost": "1539532", "IdolCost": "158", "UpgradeTime": "634800", "Art": "a_Upgrade_Tome_8", "BackgroundArt": "a_Upgrade_Tome_1", "UpgradeIcon": "a_TomeUpgradeIcon", "UpgradeDescription": "Unlocks all Rank 9 abilities for training"}, {"BuildingName": "<PERSON><PERSON>", "BuildingID": "1", "DisplayName": "<PERSON><PERSON> of Power Level 9", "Type": "<PERSON><PERSON>", "Rank": "9", "PlayerLevel": "40", "GoldCost": "2748303", "IdolCost": "198", "UpgradeTime": "715200", "Art": "a_Upgrade_Tome_9", "BackgroundArt": "a_Upgrade_Tome_1", "UpgradeIcon": "a_TomeUpgradeIcon", "UpgradeDescription": "Unlocks all Rank 10 abilities for training"}, {"BuildingName": "<PERSON><PERSON>", "BuildingID": "1", "DisplayName": "<PERSON><PERSON> of Power Level 10", "Type": "<PERSON><PERSON>", "Rank": "10", "PlayerLevel": "45", "GoldCost": "4781913", "IdolCost": "239", "UpgradeTime": "1024200", "Art": "a_Upgrade_Tome_Final", "BackgroundArt": "a_Upgrade_Tome_1", "UpgradeIcon": "a_TomeUpgradeIcon", "UpgradeDescription": "Nothing"}, {"BuildingName": "Forge", "BuildingID": "2", "DisplayName": "Magic Forge", "Type": "Forge", "Rank": "0", "PlayerLevel": "0", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "0", "Art": "a_Upgrade_Forge_0", "BackgroundArt": "a_Upgrade_Forge_0", "UpgradeIcon": "a_ForgeUpgradeIcon"}, {"BuildingName": "Forge", "BuildingID": "2", "DisplayName": "Magic Forge Level 1", "Type": "Forge", "Rank": "1", "PlayerLevel": "1", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "45", "Art": "a_Upgrade_Forge_1", "BackgroundArt": "a_Upgrade_Forge_0", "UpgradeIcon": "a_ForgeUpgradeIcon", "UpgradeDescription": "Unlocks Rank 2 charm recipes"}, {"BuildingName": "Forge", "BuildingID": "2", "DisplayName": "Magic Forge Level 2", "Type": "Forge", "Rank": "2", "PlayerLevel": "5", "GoldCost": "5872", "IdolCost": "3", "UpgradeTime": "14400", "Art": "a_Upgrade_Forge_2", "BackgroundArt": "a_Upgrade_Forge_0", "UpgradeIcon": "a_ForgeUpgradeIcon", "UpgradeDescription": "Unlocks Rank 3 charm recipes"}, {"BuildingName": "Forge", "BuildingID": "2", "DisplayName": "Magic Forge Level 3", "Type": "Forge", "Rank": "3", "PlayerLevel": "10", "GoldCost": "32867", "IdolCost": "17", "UpgradeTime": "49200", "Art": "a_Upgrade_Forge_3", "BackgroundArt": "a_Upgrade_Forge_0", "UpgradeIcon": "a_ForgeUpgradeIcon", "UpgradeDescription": "Unlocks Rank 4 charm recipes"}, {"BuildingName": "Forge", "BuildingID": "2", "DisplayName": "Magic Forge Level 4", "Type": "Forge", "Rank": "4", "PlayerLevel": "15", "GoldCost": "91888", "IdolCost": "39", "UpgradeTime": "142800", "Art": "a_Upgrade_Forge_4", "BackgroundArt": "a_Upgrade_Forge_0", "UpgradeIcon": "a_ForgeUpgradeIcon", "UpgradeDescription": "Unlocks Rank 5 charm recipes"}, {"BuildingName": "Forge", "BuildingID": "2", "DisplayName": "Magic Forge Level 5", "Type": "Forge", "Rank": "5", "PlayerLevel": "20", "GoldCost": "209646", "IdolCost": "55", "UpgradeTime": "259200", "Art": "a_Upgrade_Forge_5", "BackgroundArt": "a_Upgrade_Forge_0", "UpgradeIcon": "a_ForgeUpgradeIcon", "UpgradeDescription": "Unlocks Rank 6 charm recipes"}, {"BuildingName": "Forge", "BuildingID": "2", "DisplayName": "Magic Forge Level 6", "Type": "Forge", "Rank": "6", "PlayerLevel": "25", "GoldCost": "432172", "IdolCost": "85", "UpgradeTime": "343800", "Art": "a_Upgrade_Forge_6", "BackgroundArt": "a_Upgrade_Forge_0", "UpgradeIcon": "a_ForgeUpgradeIcon", "UpgradeDescription": "Unlocks Rank 7 charm recipes"}, {"BuildingName": "Forge", "BuildingID": "2", "DisplayName": "Magic Forge Level 7", "Type": "Forge", "Rank": "7", "PlayerLevel": "30", "GoldCost": "835058", "IdolCost": "123", "UpgradeTime": "439200", "Art": "a_Upgrade_Forge_7", "BackgroundArt": "a_Upgrade_Forge_0", "UpgradeIcon": "a_ForgeUpgradeIcon", "UpgradeDescription": "Unlocks Rank 8 charm recipes"}, {"BuildingName": "Forge", "BuildingID": "2", "DisplayName": "Magic Forge Level 8", "Type": "Forge", "Rank": "8", "PlayerLevel": "35", "GoldCost": "1539532", "IdolCost": "158", "UpgradeTime": "634800", "Art": "a_Upgrade_Forge_8", "BackgroundArt": "a_Upgrade_Forge_0", "UpgradeIcon": "a_ForgeUpgradeIcon", "UpgradeDescription": "Unlocks Rank 9 charm recipes"}, {"BuildingName": "Forge", "BuildingID": "2", "DisplayName": "Magic Forge Level 9", "Type": "Forge", "Rank": "9", "PlayerLevel": "40", "GoldCost": "2748303", "IdolCost": "198", "UpgradeTime": "715200", "Art": "a_Upgrade_Forge_9", "BackgroundArt": "a_Upgrade_Forge_0", "UpgradeIcon": "a_ForgeUpgradeIcon", "UpgradeDescription": "Unlocks Rank 10 charm recipes"}, {"BuildingName": "Forge", "BuildingID": "2", "DisplayName": "Magic Forge Level 10", "Type": "Forge", "Rank": "10", "PlayerLevel": "45", "GoldCost": "4781913", "IdolCost": "239", "UpgradeTime": "1024200", "Art": "a_Upgrade_Forge_Final", "BackgroundArt": "a_Upgrade_Forge_0", "UpgradeIcon": "a_ForgeUpgradeIcon"}, {"BuildingName": "JusticarTower", "BuildingID": "3", "DisplayName": "Flamefury Dais", "Type": "Tower", "Rank": "0", "PlayerLevel": "0", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "0", "Art": "a_Upgrade_Tower", "BackgroundArt": "a_Upgrade_ConduitBarbarian_1", "UpgradeIcon": "a_BuildingIcon_frostwarden1"}, {"BuildingName": "JusticarTower", "BuildingID": "3", "DisplayName": "Flamefury Dais Level 1", "Type": "Tower", "Rank": "1", "PlayerLevel": "10", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "120", "Art": "a_Upgrade_Tower_Justicar1", "BackgroundArt": "a_Upgrade_ConduitBarbarian_1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "JusticarTower", "BuildingID": "3", "DisplayName": "Flamefury Dais Level 2", "Type": "Tower", "Rank": "2", "PlayerLevel": "10", "GoldCost": "32867", "IdolCost": "17", "UpgradeTime": "14400", "Art": "a_Upgrade_Tower_Justicar2", "BackgroundArt": "a_Upgrade_ConduitBarbarian_1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "JusticarTower", "BuildingID": "3", "DisplayName": "Flamefury Dais Level 3", "Type": "Tower", "Rank": "3", "PlayerLevel": "15", "GoldCost": "91888", "IdolCost": "39", "UpgradeTime": "49200", "Art": "a_Upgrade_Tower_Justicar3", "BackgroundArt": "a_Upgrade_ConduitBarbarian_1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "JusticarTower", "BuildingID": "3", "DisplayName": "Flamefury Dais Level 4", "Type": "Tower", "Rank": "4", "PlayerLevel": "20", "GoldCost": "209646", "IdolCost": "55", "UpgradeTime": "142800", "Art": "a_Upgrade_Tower_Justicar4", "BackgroundArt": "a_Upgrade_ConduitBarbarian_1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "JusticarTower", "BuildingID": "3", "DisplayName": "Flamefury Dais Level 5", "Type": "Tower", "Rank": "5", "PlayerLevel": "25", "GoldCost": "432172", "IdolCost": "85", "UpgradeTime": "259200", "Art": "a_Upgrade_Tower_Justicar5", "BackgroundArt": "a_Upgrade_ConduitBarbarian_5", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "JusticarTower", "BuildingID": "3", "DisplayName": "Flamefury Dais Level 6", "Type": "Tower", "Rank": "6", "PlayerLevel": "30", "GoldCost": "835058", "IdolCost": "123", "UpgradeTime": "343800", "Art": "a_Upgrade_Tower_Justicar6", "BackgroundArt": "a_Upgrade_ConduitBarbarian_1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "JusticarTower", "BuildingID": "3", "DisplayName": "Flamefury Dais Level 7", "Type": "Tower", "Rank": "7", "PlayerLevel": "35", "GoldCost": "1539532", "IdolCost": "158", "UpgradeTime": "439200", "Art": "a_Upgrade_Tower_Justicar7", "BackgroundArt": "a_Upgrade_ConduitBarbarian_1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "JusticarTower", "BuildingID": "3", "DisplayName": "Flamefury Dais Level 8", "Type": "Tower", "Rank": "8", "PlayerLevel": "40", "GoldCost": "2748303", "IdolCost": "198", "UpgradeTime": "634800", "Art": "a_Upgrade_Tower_Justicar8", "BackgroundArt": "a_Upgrade_ConduitBarbarian_1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "JusticarTower", "BuildingID": "3", "DisplayName": "Flamefury Dais Level 9", "Type": "Tower", "Rank": "9", "PlayerLevel": "45", "GoldCost": "4781913", "IdolCost": "239", "UpgradeTime": "715200", "Art": "a_Upgrade_Tower_Justicar9", "BackgroundArt": "a_Upgrade_ConduitBarbarian_1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "JusticarTower", "BuildingID": "3", "DisplayName": "Flamefury Dais Level 10", "Type": "Tower", "Rank": "10", "PlayerLevel": "50", "GoldCost": "8155866", "IdolCost": "299", "UpgradeTime": "1024200", "Art": "a_Upgrade_Tower_JusticarFinal", "BackgroundArt": "a_Upgrade_ConduitBarbarian_10", "UpgradeIcon": "a_BuildingIcon_frostwarden1"}, {"BuildingName": "SentinelTower", "BuildingID": "4", "DisplayName": "Stormgaze Refuge", "Type": "Tower", "Rank": "0", "PlayerLevel": "0", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "0", "Art": "a_Upgrade_Tower"}, {"BuildingName": "SentinelTower", "BuildingID": "4", "DisplayName": "Stormgaze Refuge Level 1", "Type": "Tower", "Rank": "1", "PlayerLevel": "10", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "120", "Art": "a_Upgrade_Tower_Sentinel1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "SentinelTower", "BuildingID": "4", "DisplayName": "Stormgaze Refuge Level 2", "Type": "Tower", "Rank": "2", "PlayerLevel": "10", "GoldCost": "32867", "IdolCost": "17", "UpgradeTime": "14400", "Art": "a_Upgrade_Tower_Sentinel2", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "SentinelTower", "BuildingID": "4", "DisplayName": "Stormgaze Refuge Level 3", "Type": "Tower", "Rank": "3", "PlayerLevel": "15", "GoldCost": "91888", "IdolCost": "39", "UpgradeTime": "49200", "Art": "a_Upgrade_Tower_Sentinel3", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "SentinelTower", "BuildingID": "4", "DisplayName": "Stormgaze Refuge Level 4", "Type": "Tower", "Rank": "4", "PlayerLevel": "20", "GoldCost": "209646", "IdolCost": "55", "UpgradeTime": "142800", "Art": "a_Upgrade_Tower_Sentinel4", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "SentinelTower", "BuildingID": "4", "DisplayName": "Stormgaze Refuge Level 5", "Type": "Tower", "Rank": "5", "PlayerLevel": "25", "GoldCost": "432172", "IdolCost": "85", "UpgradeTime": "259200", "Art": "a_Upgrade_Tower_Sentinel5", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "SentinelTower", "BuildingID": "4", "DisplayName": "Stormgaze Refuge Level 6", "Type": "Tower", "Rank": "6", "PlayerLevel": "30", "GoldCost": "835058", "IdolCost": "123", "UpgradeTime": "343800", "Art": "a_Upgrade_Tower_Sentinel6", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "SentinelTower", "BuildingID": "4", "DisplayName": "Stormgaze Refuge Level 7", "Type": "Tower", "Rank": "7", "PlayerLevel": "35", "GoldCost": "1539532", "IdolCost": "158", "UpgradeTime": "439200", "Art": "a_Upgrade_Tower_Sentinel7", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "SentinelTower", "BuildingID": "4", "DisplayName": "Stormgaze Refuge Level 8", "Type": "Tower", "Rank": "8", "PlayerLevel": "40", "GoldCost": "2748303", "IdolCost": "198", "UpgradeTime": "634800", "Art": "a_Upgrade_Tower_Sentinel8", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "SentinelTower", "BuildingID": "4", "DisplayName": "Stormgaze Refuge Level 9", "Type": "Tower", "Rank": "9", "PlayerLevel": "45", "GoldCost": "4781913", "IdolCost": "239", "UpgradeTime": "715200", "Art": "a_Upgrade_Tower_Sentinel9", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "SentinelTower", "BuildingID": "4", "DisplayName": "Stormgaze Refuge Level 10", "Type": "Tower", "Rank": "10", "PlayerLevel": "50", "GoldCost": "8155866", "IdolCost": "299", "UpgradeTime": "1024200", "Art": "a_Upgrade_Tower_SentinelFinal"}, {"BuildingName": "TemplarTower", "BuildingID": "5", "DisplayName": "Templar Citadel", "Type": "Tower", "Rank": "0", "PlayerLevel": "0", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "0", "Art": "a_Upgrade_Tower", "BackgroundArt": "a_templar1", "UpgradeIcon": "a_BuildingIcon_frostwarden1"}, {"BuildingName": "TemplarTower", "BuildingID": "5", "DisplayName": "Templar Citadel Level 1", "Type": "Tower", "Rank": "1", "PlayerLevel": "10", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "120", "Art": "a_Upgrade_Tower_Templar1", "BackgroundArt": "a_templar1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "TemplarTower", "BuildingID": "5", "DisplayName": "Templar Citadel Level 2", "Type": "Tower", "Rank": "2", "PlayerLevel": "10", "GoldCost": "32867", "IdolCost": "17", "UpgradeTime": "14400", "Art": "a_Upgrade_Tower_Templar2", "BackgroundArt": "a_templar1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "TemplarTower", "BuildingID": "5", "DisplayName": "Templar Citadel Level 3", "Type": "Tower", "Rank": "3", "PlayerLevel": "15", "GoldCost": "91888", "IdolCost": "39", "UpgradeTime": "49200", "Art": "a_Upgrade_Tower_Templar3", "BackgroundArt": "a_templar1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "TemplarTower", "BuildingID": "5", "DisplayName": "Templar Citadel Level 4", "Type": "Tower", "Rank": "4", "PlayerLevel": "20", "GoldCost": "209646", "IdolCost": "55", "UpgradeTime": "142800", "Art": "a_Upgrade_Tower_Templar4", "BackgroundArt": "a_templar1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "TemplarTower", "BuildingID": "5", "DisplayName": "Templar Citadel Level 5", "Type": "Tower", "Rank": "5", "PlayerLevel": "25", "GoldCost": "432172", "IdolCost": "85", "UpgradeTime": "259200", "Art": "a_Upgrade_Tower_Templar5", "BackgroundArt": "a_templar1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "TemplarTower", "BuildingID": "5", "DisplayName": "Templar Citadel Level 6", "Type": "Tower", "Rank": "6", "PlayerLevel": "30", "GoldCost": "835058", "IdolCost": "123", "UpgradeTime": "343800", "Art": "a_Upgrade_Tower_Templar6", "BackgroundArt": "a_templar1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "TemplarTower", "BuildingID": "5", "DisplayName": "Templar Citadel Level 7", "Type": "Tower", "Rank": "7", "PlayerLevel": "35", "GoldCost": "1539532", "IdolCost": "158", "UpgradeTime": "439200", "Art": "a_Upgrade_Tower_Templar7", "BackgroundArt": "a_templar1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "TemplarTower", "BuildingID": "5", "DisplayName": "Templar Citadel Level 8", "Type": "Tower", "Rank": "8", "PlayerLevel": "40", "GoldCost": "2748303", "IdolCost": "198", "UpgradeTime": "634800", "Art": "a_Upgrade_Tower_Templar8", "BackgroundArt": "a_templar1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "TemplarTower", "BuildingID": "5", "DisplayName": "Templar Citadel Level 9", "Type": "Tower", "Rank": "9", "PlayerLevel": "45", "GoldCost": "4781913", "IdolCost": "239", "UpgradeTime": "715200", "Art": "a_Upgrade_Tower_Templar9", "BackgroundArt": "a_templar1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "TemplarTower", "BuildingID": "5", "DisplayName": "Templar Citadel Level 10", "Type": "Tower", "Rank": "10", "PlayerLevel": "50", "GoldCost": "8155866", "IdolCost": "299", "UpgradeTime": "1024200", "Art": "a_Upgrade_Tower_TemplarFinal", "BackgroundArt": "a_templar1", "UpgradeIcon": "a_BuildingIcon_frostwarden1"}, {"BuildingName": "FrostwardenTower", "BuildingID": "6", "DisplayName": "Coldsnap Conduit", "Type": "Tower", "Rank": "0", "PlayerLevel": "0", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "0", "Art": "a_Upgrade_Tower", "BackgroundArt": "a_TowerAndBG_frostwarden1", "UpgradeIcon": "a_BuildingIcon_frostwarden1"}, {"BuildingName": "FrostwardenTower", "BuildingID": "6", "DisplayName": "Coldsnap Conduit Level 1", "Type": "Tower", "Rank": "1", "PlayerLevel": "10", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "120", "Art": "a_Upgrade_Tower_Frostwarden1", "BackgroundArt": "a_TowerAndBG_frostwarden1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "FrostwardenTower", "BuildingID": "6", "DisplayName": "Coldsnap Conduit Level 2", "Type": "Tower", "Rank": "2", "PlayerLevel": "10", "GoldCost": "32867", "IdolCost": "17", "UpgradeTime": "14400", "Art": "a_Upgrade_Tower_Frostwarden2", "BackgroundArt": "a_TowerAndBG_frostwarden1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "FrostwardenTower", "BuildingID": "6", "DisplayName": "Coldsnap Conduit Level 3", "Type": "Tower", "Rank": "3", "PlayerLevel": "15", "GoldCost": "91888", "IdolCost": "39", "UpgradeTime": "49200", "Art": "a_Upgrade_Tower_Frostwarden3", "BackgroundArt": "a_TowerAndBG_frostwarden1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "FrostwardenTower", "BuildingID": "6", "DisplayName": "Coldsnap Conduit Level 4", "Type": "Tower", "Rank": "4", "PlayerLevel": "20", "GoldCost": "209646", "IdolCost": "55", "UpgradeTime": "142800", "Art": "a_Upgrade_Tower_Frostwarden4", "BackgroundArt": "a_TowerAndBG_frostwarden1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "FrostwardenTower", "BuildingID": "6", "DisplayName": "Coldsnap Conduit Level 5", "Type": "Tower", "Rank": "5", "PlayerLevel": "25", "GoldCost": "432172", "IdolCost": "85", "UpgradeTime": "259200", "Art": "a_Upgrade_Tower_Frostwarden5", "BackgroundArt": "a_TowerAndBG_frostwarden1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "FrostwardenTower", "BuildingID": "6", "DisplayName": "Coldsnap Conduit Level 6", "Type": "Tower", "Rank": "6", "PlayerLevel": "30", "GoldCost": "835058", "IdolCost": "123", "UpgradeTime": "343800", "Art": "a_Upgrade_Tower_Frostwarden6", "BackgroundArt": "a_TowerAndBG_frostwarden1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "FrostwardenTower", "BuildingID": "6", "DisplayName": "Coldsnap Conduit Level 7", "Type": "Tower", "Rank": "7", "PlayerLevel": "35", "GoldCost": "1539532", "IdolCost": "158", "UpgradeTime": "439200", "Art": "a_Upgrade_Tower_Frostwarden7", "BackgroundArt": "a_TowerAndBG_frostwarden1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "FrostwardenTower", "BuildingID": "6", "DisplayName": "Coldsnap Conduit Level 8", "Type": "Tower", "Rank": "8", "PlayerLevel": "40", "GoldCost": "2748303", "IdolCost": "198", "UpgradeTime": "634800", "Art": "a_Upgrade_Tower_Frostwarden8", "BackgroundArt": "a_TowerAndBG_frostwarden1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "FrostwardenTower", "BuildingID": "6", "DisplayName": "Coldsnap Conduit Level 9", "Type": "Tower", "Rank": "9", "PlayerLevel": "45", "GoldCost": "4781913", "IdolCost": "239", "UpgradeTime": "715200", "Art": "a_Upgrade_Tower_Frostwarden9", "BackgroundArt": "a_TowerAndBG_frostwarden1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "FrostwardenTower", "BuildingID": "6", "DisplayName": "Coldsnap Conduit Level 10", "Type": "Tower", "Rank": "10", "PlayerLevel": "50", "GoldCost": "8155866", "IdolCost": "299", "UpgradeTime": "1024200", "Art": "a_Upgrade_Tower_FrostwardenFinal", "BackgroundArt": "a_TowerAndBG_frostwarden1", "UpgradeIcon": "a_BuildingIcon_frostwarden1"}, {"BuildingName": "Flameseer<PERSON>ower", "BuildingID": "7", "DisplayName": "Magmaheart Furnace", "Type": "Tower", "Rank": "0", "PlayerLevel": "0", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "0", "Art": "a_Upgrade_Tower", "BackgroundArt": "a_towerbg_flameseer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1"}, {"BuildingName": "Flameseer<PERSON>ower", "BuildingID": "7", "DisplayName": "Magmaheart Furnace Level 1", "Type": "Tower", "Rank": "1", "PlayerLevel": "10", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "120", "Art": "a_Upgrade_Tower_Flameseer1", "BackgroundArt": "a_towerbg_flameseer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "Flameseer<PERSON>ower", "BuildingID": "7", "DisplayName": "Magmaheart Furnace Level 2", "Type": "Tower", "Rank": "2", "PlayerLevel": "10", "GoldCost": "32867", "IdolCost": "17", "UpgradeTime": "14400", "Art": "a_Upgrade_Tower_Flameseer2", "BackgroundArt": "a_towerbg_flameseer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "Flameseer<PERSON>ower", "BuildingID": "7", "DisplayName": "Magmaheart Furnace Level 3", "Type": "Tower", "Rank": "3", "PlayerLevel": "15", "GoldCost": "91888", "IdolCost": "39", "UpgradeTime": "49200", "Art": "a_Upgrade_Tower_Flameseer3", "BackgroundArt": "a_towerbg_flameseer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "Flameseer<PERSON>ower", "BuildingID": "7", "DisplayName": "Magmaheart Furnace Level 4", "Type": "Tower", "Rank": "4", "PlayerLevel": "20", "GoldCost": "209646", "IdolCost": "55", "UpgradeTime": "142800", "Art": "a_Upgrade_Tower_Flameseer4", "BackgroundArt": "a_towerbg_flameseer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "Flameseer<PERSON>ower", "BuildingID": "7", "DisplayName": "Magmaheart Furnace Level 5", "Type": "Tower", "Rank": "5", "PlayerLevel": "25", "GoldCost": "432172", "IdolCost": "85", "UpgradeTime": "259200", "Art": "a_Upgrade_Tower_Flameseer5", "BackgroundArt": "a_towerbg_flameseer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "Flameseer<PERSON>ower", "BuildingID": "7", "DisplayName": "Magmaheart Furnace Level 6", "Type": "Tower", "Rank": "6", "PlayerLevel": "30", "GoldCost": "835058", "IdolCost": "123", "UpgradeTime": "343800", "Art": "a_Upgrade_Tower_Flameseer6", "BackgroundArt": "a_towerbg_flameseer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "Flameseer<PERSON>ower", "BuildingID": "7", "DisplayName": "Magmaheart Furnace Level 7", "Type": "Tower", "Rank": "7", "PlayerLevel": "35", "GoldCost": "1539532", "IdolCost": "158", "UpgradeTime": "439200", "Art": "a_Upgrade_Tower_Flameseer7", "BackgroundArt": "a_towerbg_flameseer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "Flameseer<PERSON>ower", "BuildingID": "7", "DisplayName": "Magmaheart Furnace Level 8", "Type": "Tower", "Rank": "8", "PlayerLevel": "40", "GoldCost": "2748303", "IdolCost": "198", "UpgradeTime": "634800", "Art": "a_Upgrade_Tower_Flameseer8", "BackgroundArt": "a_towerbg_flameseer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "Flameseer<PERSON>ower", "BuildingID": "7", "DisplayName": "Magmaheart Furnace Level 9", "Type": "Tower", "Rank": "9", "PlayerLevel": "45", "GoldCost": "4781913", "IdolCost": "239", "UpgradeTime": "715200", "Art": "a_Upgrade_Tower_Flameseer9", "BackgroundArt": "a_towerbg_flameseer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "Flameseer<PERSON>ower", "BuildingID": "7", "DisplayName": "Magmaheart Furnace Level 10", "Type": "Tower", "Rank": "10", "PlayerLevel": "50", "GoldCost": "8155866", "IdolCost": "299", "UpgradeTime": "1024200", "Art": "a_Upgrade_Tower_FlameseerFinal", "BackgroundArt": "a_towerbg_flameseer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1"}, {"BuildingName": "NecromancerTower", "BuildingID": "8", "DisplayName": "Necromancer Tower", "Type": "Tower", "Rank": "0", "PlayerLevel": "0", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "0", "Art": "a_Upgrade_Tower", "BackgroundArt": "a_towerbg_necromancer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1"}, {"BuildingName": "NecromancerTower", "BuildingID": "8", "DisplayName": "Necromancer Tower Level 1", "Type": "Tower", "Rank": "1", "PlayerLevel": "10", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "120", "Art": "a_Upgrade_Tower_Necromancer1", "BackgroundArt": "a_towerbg_necromancer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "NecromancerTower", "BuildingID": "8", "DisplayName": "Necromancer Tower Level 2", "Type": "Tower", "Rank": "2", "PlayerLevel": "10", "GoldCost": "32867", "IdolCost": "17", "UpgradeTime": "14400", "Art": "a_Upgrade_Tower_Necromancer2", "BackgroundArt": "a_towerbg_necromancer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "NecromancerTower", "BuildingID": "8", "DisplayName": "Necromancer Tower Level 3", "Type": "Tower", "Rank": "3", "PlayerLevel": "15", "GoldCost": "91888", "IdolCost": "39", "UpgradeTime": "49200", "Art": "a_Upgrade_Tower_Necromancer3", "BackgroundArt": "a_towerbg_necromancer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "NecromancerTower", "BuildingID": "8", "DisplayName": "Necromancer Tower Level 4", "Type": "Tower", "Rank": "4", "PlayerLevel": "20", "GoldCost": "209646", "IdolCost": "55", "UpgradeTime": "142800", "Art": "a_Upgrade_Tower_Necromancer4", "BackgroundArt": "a_towerbg_necromancer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "NecromancerTower", "BuildingID": "8", "DisplayName": "Necromancer Tower Level 5", "Type": "Tower", "Rank": "5", "PlayerLevel": "25", "GoldCost": "432172", "IdolCost": "85", "UpgradeTime": "259200", "Art": "a_Upgrade_Tower_Necromancer5", "BackgroundArt": "a_towerbg_necromancer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "NecromancerTower", "BuildingID": "8", "DisplayName": "Necromancer Tower Level 6", "Type": "Tower", "Rank": "6", "PlayerLevel": "30", "GoldCost": "835058", "IdolCost": "123", "UpgradeTime": "343800", "Art": "a_Upgrade_Tower_Necromancer6", "BackgroundArt": "a_towerbg_necromancer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "NecromancerTower", "BuildingID": "8", "DisplayName": "Necromancer Tower Level 7", "Type": "Tower", "Rank": "7", "PlayerLevel": "35", "GoldCost": "1539532", "IdolCost": "158", "UpgradeTime": "439200", "Art": "a_Upgrade_Tower_Necromancer7", "BackgroundArt": "a_towerbg_necromancer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "NecromancerTower", "BuildingID": "8", "DisplayName": "Necromancer Tower Level 8", "Type": "Tower", "Rank": "8", "PlayerLevel": "40", "GoldCost": "2748303", "IdolCost": "198", "UpgradeTime": "634800", "Art": "a_Upgrade_Tower_Necromancer8", "BackgroundArt": "a_towerbg_necromancer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "NecromancerTower", "BuildingID": "8", "DisplayName": "Necromancer Tower Level 9", "Type": "Tower", "Rank": "9", "PlayerLevel": "45", "GoldCost": "4781913", "IdolCost": "239", "UpgradeTime": "715200", "Art": "a_Upgrade_Tower_Necromancer9", "BackgroundArt": "a_towerbg_necromancer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "NecromancerTower", "BuildingID": "8", "DisplayName": "Necromancer Tower Level 10", "Type": "Tower", "Rank": "10", "PlayerLevel": "50", "GoldCost": "8155866", "IdolCost": "299", "UpgradeTime": "1024200", "Art": "a_Upgrade_Tower_NecromancerFinal", "BackgroundArt": "a_towerbg_necromancer1", "UpgradeIcon": "a_BuildingIcon_frostwarden1"}, {"BuildingName": "ExecutionerTower", "BuildingID": "9", "DisplayName": "Elysian Soultrap", "Type": "Tower", "Rank": "0", "PlayerLevel": "0", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "0", "Art": "a_Upgrade_Tower", "BackgroundArt": "a_executioner1", "UpgradeIcon": "a_BuildingIcon_frostwarden1"}, {"BuildingName": "ExecutionerTower", "BuildingID": "9", "DisplayName": "Elysian Soultrap Level 1", "Type": "Tower", "Rank": "1", "PlayerLevel": "10", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "120", "Art": "a_Upgrade_Tower_Executioner1", "BackgroundArt": "a_executioner1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "ExecutionerTower", "BuildingID": "9", "DisplayName": "Elysian Soultrap Level 2", "Type": "Tower", "Rank": "2", "PlayerLevel": "10", "GoldCost": "32867", "IdolCost": "17", "UpgradeTime": "14400", "Art": "a_Upgrade_Tower_Executioner2", "BackgroundArt": "a_executioner1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "ExecutionerTower", "BuildingID": "9", "DisplayName": "Elysian Soultrap Level 3", "Type": "Tower", "Rank": "3", "PlayerLevel": "15", "GoldCost": "91888", "IdolCost": "39", "UpgradeTime": "49200", "Art": "a_Upgrade_Tower_Executioner3", "BackgroundArt": "a_executioner1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "ExecutionerTower", "BuildingID": "9", "DisplayName": "Elysian Soultrap Level 4", "Type": "Tower", "Rank": "4", "PlayerLevel": "20", "GoldCost": "209646", "IdolCost": "55", "UpgradeTime": "142800", "Art": "a_Upgrade_Tower_Executioner4", "BackgroundArt": "a_executioner1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "ExecutionerTower", "BuildingID": "9", "DisplayName": "Elysian Soultrap Level 5", "Type": "Tower", "Rank": "5", "PlayerLevel": "25", "GoldCost": "432172", "IdolCost": "85", "UpgradeTime": "259200", "Art": "a_Upgrade_Tower_Executioner5", "BackgroundArt": "a_executioner1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "ExecutionerTower", "BuildingID": "9", "DisplayName": "Elysian Soultrap Level 6", "Type": "Tower", "Rank": "6", "PlayerLevel": "30", "GoldCost": "835058", "IdolCost": "123", "UpgradeTime": "343800", "Art": "a_Upgrade_Tower_Executioner6", "BackgroundArt": "a_executioner1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "ExecutionerTower", "BuildingID": "9", "DisplayName": "Elysian Soultrap Level 7", "Type": "Tower", "Rank": "7", "PlayerLevel": "35", "GoldCost": "1539532", "IdolCost": "158", "UpgradeTime": "439200", "Art": "a_Upgrade_Tower_Executioner7", "BackgroundArt": "a_executioner1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "ExecutionerTower", "BuildingID": "9", "DisplayName": "Elysian Soultrap Level 8", "Type": "Tower", "Rank": "8", "PlayerLevel": "40", "GoldCost": "2748303", "IdolCost": "198", "UpgradeTime": "634800", "Art": "a_Upgrade_Tower_Executioner8", "BackgroundArt": "a_executioner1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "ExecutionerTower", "BuildingID": "9", "DisplayName": "Elysian Soultrap Level 9", "Type": "Tower", "Rank": "9", "PlayerLevel": "45", "GoldCost": "4781913", "IdolCost": "239", "UpgradeTime": "715200", "Art": "a_Upgrade_Tower_Executioner9", "BackgroundArt": "a_executioner1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "ExecutionerTower", "BuildingID": "9", "DisplayName": "Elysian Soultrap Level 10", "Type": "Tower", "Rank": "10", "PlayerLevel": "50", "GoldCost": "8155866", "IdolCost": "299", "UpgradeTime": "1024200", "Art": "a_Upgrade_Tower_ExecutionerFinal", "BackgroundArt": "a_executioner1", "UpgradeIcon": "a_BuildingIcon_frostwarden1"}, {"BuildingName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BuildingID": "10", "DisplayName": "Twisted Nethertotem", "Type": "Tower", "Rank": "0", "PlayerLevel": "0", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "0", "Art": "a_Upgrade_Tower", "BackgroundArt": "a_shadowwalker1", "UpgradeIcon": "a_BuildingIcon_frostwarden1"}, {"BuildingName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BuildingID": "10", "DisplayName": "Twisted Nethertotem Level 1", "Type": "Tower", "Rank": "1", "PlayerLevel": "10", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "120", "Art": "a_Upgrade_Tower_Shadowwalker1", "BackgroundArt": "a_shadowwalker1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BuildingID": "10", "DisplayName": "Twisted Nethertotem Level 2", "Type": "Tower", "Rank": "2", "PlayerLevel": "10", "GoldCost": "32867", "IdolCost": "17", "UpgradeTime": "14400", "Art": "a_Upgrade_Tower_Shadowwalker2", "BackgroundArt": "a_shadowwalker1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BuildingID": "10", "DisplayName": "Twisted Nethertotem Level 3", "Type": "Tower", "Rank": "3", "PlayerLevel": "15", "GoldCost": "91888", "IdolCost": "39", "UpgradeTime": "49200", "Art": "a_Upgrade_Tower_Shadowwalker3", "BackgroundArt": "a_shadowwalker1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BuildingID": "10", "DisplayName": "Twisted Nethertotem Level 4", "Type": "Tower", "Rank": "4", "PlayerLevel": "20", "GoldCost": "209646", "IdolCost": "55", "UpgradeTime": "142800", "Art": "a_Upgrade_Tower_Shadowwalker4", "BackgroundArt": "a_shadowwalker1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BuildingID": "10", "DisplayName": "Twisted Nethertotem Level 5", "Type": "Tower", "Rank": "5", "PlayerLevel": "25", "GoldCost": "432172", "IdolCost": "85", "UpgradeTime": "259200", "Art": "a_Upgrade_Tower_Shadowwalker5", "BackgroundArt": "a_shadowwalker1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BuildingID": "10", "DisplayName": "Twisted Nethertotem Level 6", "Type": "Tower", "Rank": "6", "PlayerLevel": "30", "GoldCost": "835058", "IdolCost": "123", "UpgradeTime": "343800", "Art": "a_Upgrade_Tower_Shadowwalker6", "BackgroundArt": "a_shadowwalker1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BuildingID": "10", "DisplayName": "Twisted Nethertotem Level 7", "Type": "Tower", "Rank": "7", "PlayerLevel": "35", "GoldCost": "1539532", "IdolCost": "158", "UpgradeTime": "439200", "Art": "a_Upgrade_Tower_Shadowwalker7", "BackgroundArt": "a_shadowwalker1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BuildingID": "10", "DisplayName": "Twisted Nethertotem Level 8", "Type": "Tower", "Rank": "8", "PlayerLevel": "40", "GoldCost": "2748303", "IdolCost": "198", "UpgradeTime": "634800", "Art": "a_Upgrade_Tower_Shadowwalker8", "BackgroundArt": "a_shadowwalker1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BuildingID": "10", "DisplayName": "Twisted Nethertotem Level 9", "Type": "Tower", "Rank": "9", "PlayerLevel": "45", "GoldCost": "4781913", "IdolCost": "239", "UpgradeTime": "715200", "Art": "a_Upgrade_Tower_Shadowwalker9", "BackgroundArt": "a_shadowwalker1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BuildingID": "10", "DisplayName": "Twisted Nethertotem Level 10", "Type": "Tower", "Rank": "10", "PlayerLevel": "50", "GoldCost": "8155866", "IdolCost": "299", "UpgradeTime": "1024200", "Art": "a_Upgrade_Tower_ShadowwalkerFinal", "BackgroundArt": "a_shadowwalker1", "UpgradeIcon": "a_BuildingIcon_frostwarden1"}, {"BuildingName": "SoulthiefTower", "BuildingID": "11", "DisplayName": "<PERSON><PERSON><PERSON>", "Type": "Tower", "Rank": "0", "PlayerLevel": "0", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "0", "Art": "a_Upgrade_Tower", "BackgroundArt": "a_revenant1", "UpgradeIcon": "a_BuildingIcon_frostwarden1"}, {"BuildingName": "SoulthiefTower", "BuildingID": "11", "DisplayName": "Soulthief Lair Level 1", "Type": "Tower", "Rank": "1", "PlayerLevel": "10", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "120", "Art": "a_Upgrade_Tower_Blademaster1", "BackgroundArt": "a_revenant1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "SoulthiefTower", "BuildingID": "11", "DisplayName": "Soulthief Lair Level 2", "Type": "Tower", "Rank": "2", "PlayerLevel": "10", "GoldCost": "32867", "IdolCost": "17", "UpgradeTime": "14400", "Art": "a_Upgrade_Tower_Blademaster2", "BackgroundArt": "a_revenant1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "SoulthiefTower", "BuildingID": "11", "DisplayName": "Soulthief Lair Level 3", "Type": "Tower", "Rank": "3", "PlayerLevel": "15", "GoldCost": "91888", "IdolCost": "39", "UpgradeTime": "49200", "Art": "a_Upgrade_Tower_Blademaster3", "BackgroundArt": "a_revenant1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "SoulthiefTower", "BuildingID": "11", "DisplayName": "Soulthief Lair Level 4", "Type": "Tower", "Rank": "4", "PlayerLevel": "20", "GoldCost": "209646", "IdolCost": "55", "UpgradeTime": "142800", "Art": "a_Upgrade_Tower_Blademaster4", "BackgroundArt": "a_revenant1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "SoulthiefTower", "BuildingID": "11", "DisplayName": "Soulthief Lair Level 5", "Type": "Tower", "Rank": "5", "PlayerLevel": "25", "GoldCost": "432172", "IdolCost": "85", "UpgradeTime": "259200", "Art": "a_Upgrade_Tower_Blademaster5", "BackgroundArt": "a_revenant1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "SoulthiefTower", "BuildingID": "11", "DisplayName": "Soulthief Lair Level 6", "Type": "Tower", "Rank": "6", "PlayerLevel": "30", "GoldCost": "835058", "IdolCost": "123", "UpgradeTime": "343800", "Art": "a_Upgrade_Tower_Blademaster6", "BackgroundArt": "a_revenant1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "SoulthiefTower", "BuildingID": "11", "DisplayName": "Soulthief Lair Level 7", "Type": "Tower", "Rank": "7", "PlayerLevel": "35", "GoldCost": "1539532", "IdolCost": "158", "UpgradeTime": "439200", "Art": "a_Upgrade_Tower_Blademaster7", "BackgroundArt": "a_revenant1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "SoulthiefTower", "BuildingID": "11", "DisplayName": "Soulthief Lair Level 8", "Type": "Tower", "Rank": "8", "PlayerLevel": "40", "GoldCost": "2748303", "IdolCost": "198", "UpgradeTime": "634800", "Art": "a_Upgrade_Tower_Blademaster8", "BackgroundArt": "a_revenant1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "SoulthiefTower", "BuildingID": "11", "DisplayName": "Soulthief Lair Level 9", "Type": "Tower", "Rank": "9", "PlayerLevel": "45", "GoldCost": "4781913", "IdolCost": "239", "UpgradeTime": "715200", "Art": "a_Upgrade_Tower_Blademaster9", "BackgroundArt": "a_revenant1", "UpgradeIcon": "a_BuildingIcon_frostwarden1", "UpgradeDescription": "Unlocks the next 5 talent points for training"}, {"BuildingName": "SoulthiefTower", "BuildingID": "11", "DisplayName": "Soulthief Lair Level 10", "Type": "Tower", "Rank": "10", "PlayerLevel": "50", "GoldCost": "8155866", "IdolCost": "299", "UpgradeTime": "1024200", "Art": "a_Upgrade_Tower_BlademasterFinal", "BackgroundArt": "a_revenant1", "UpgradeIcon": "a_BuildingIcon_frostwarden1"}, {"BuildingName": "Keep", "BuildingID": "12", "DisplayName": "Hero's Keep", "Type": "Keep", "Rank": "0", "PlayerLevel": "0", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "0", "Art": "a_Upgrade_Keep_5", "BackgroundArt": "a_Upgrade_Keep_1", "UpgradeIcon": "a_Upgrade_Keep_1"}, {"BuildingName": "<PERSON>n", "BuildingID": "13", "DisplayName": "Hatchery", "Type": "<PERSON>n", "Rank": "0", "PlayerLevel": "0", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "0", "Art": "a_Upgrade_Barn_0", "BackgroundArt": "a_Upgrade_PetHouse_1", "UpgradeIcon": "a_BarnUpgradeIcon"}, {"BuildingName": "<PERSON>n", "BuildingID": "13", "DisplayName": "Hatchery Level 1", "Type": "<PERSON>n", "Rank": "1", "PlayerLevel": "5", "GoldCost": "0", "IdolCost": "0", "UpgradeTime": "150", "Art": "a_Upgrade_Barn_1", "BackgroundArt": "a_Upgrade_PetHouse_1", "UpgradeIcon": "a_BarnUpgradeIcon", "UpgradeDescription": "Increases the max pet level cap by 2"}, {"BuildingName": "<PERSON>n", "BuildingID": "13", "DisplayName": "Hatchery Level 2", "Type": "<PERSON>n", "Rank": "2", "PlayerLevel": "5", "GoldCost": "5872", "IdolCost": "3", "UpgradeTime": "14400", "Art": "a_Upgrade_Barn_2", "BackgroundArt": "a_Upgrade_PetHouse_1", "UpgradeIcon": "a_BarnUpgradeIcon", "UpgradeDescription": "Increases the max pet level cap by 2"}, {"BuildingName": "<PERSON>n", "BuildingID": "13", "DisplayName": "Hatchery Level 3", "Type": "<PERSON>n", "Rank": "3", "PlayerLevel": "10", "GoldCost": "32867", "IdolCost": "17", "UpgradeTime": "49200", "Art": "a_Upgrade_Barn_3", "BackgroundArt": "a_Upgrade_PetHouse_1", "UpgradeIcon": "a_BarnUpgradeIcon", "UpgradeDescription": "Increases the max pet level cap by 2"}, {"BuildingName": "<PERSON>n", "BuildingID": "13", "DisplayName": "Hatchery Level 4", "Type": "<PERSON>n", "Rank": "4", "PlayerLevel": "15", "GoldCost": "91888", "IdolCost": "39", "UpgradeTime": "142800", "Art": "a_Upgrade_Barn_4", "BackgroundArt": "a_Upgrade_PetHouse_1", "UpgradeIcon": "a_BarnUpgradeIcon", "UpgradeDescription": "Increases the max pet level cap by 2"}, {"BuildingName": "<PERSON>n", "BuildingID": "13", "DisplayName": "Hatchery Level 5", "Type": "<PERSON>n", "Rank": "5", "PlayerLevel": "20", "GoldCost": "209646", "IdolCost": "55", "UpgradeTime": "259200", "Art": "a_Upgrade_Barn_5", "BackgroundArt": "a_Upgrade_PetHouse_1", "UpgradeIcon": "a_BarnUpgradeIcon", "UpgradeDescription": "Increases the max pet level cap by 2"}, {"BuildingName": "<PERSON>n", "BuildingID": "13", "DisplayName": "Hatchery Level 6", "Type": "<PERSON>n", "Rank": "6", "PlayerLevel": "25", "GoldCost": "432172", "IdolCost": "85", "UpgradeTime": "343800", "Art": "a_Upgrade_Barn_6", "BackgroundArt": "a_Upgrade_PetHouse_1", "UpgradeIcon": "a_BarnUpgradeIcon", "UpgradeDescription": "Increases the max pet level cap by 2"}, {"BuildingName": "<PERSON>n", "BuildingID": "13", "DisplayName": "Hatchery Level 7", "Type": "<PERSON>n", "Rank": "7", "PlayerLevel": "30", "GoldCost": "835058", "IdolCost": "123", "UpgradeTime": "439200", "Art": "a_Upgrade_Barn_7", "BackgroundArt": "a_Upgrade_PetHouse_1", "UpgradeIcon": "a_BarnUpgradeIcon", "UpgradeDescription": "Increases the max pet level cap by 2"}, {"BuildingName": "<PERSON>n", "BuildingID": "13", "DisplayName": "Hatchery Level 8", "Type": "<PERSON>n", "Rank": "8", "PlayerLevel": "35", "GoldCost": "1539532", "IdolCost": "158", "UpgradeTime": "634800", "Art": "a_Upgrade_Barn_8", "BackgroundArt": "a_Upgrade_PetHouse_1", "UpgradeIcon": "a_BarnUpgradeIcon", "UpgradeDescription": "Increases the max pet level cap by 2"}, {"BuildingName": "<PERSON>n", "BuildingID": "13", "DisplayName": "Hatchery Level 9", "Type": "<PERSON>n", "Rank": "9", "PlayerLevel": "40", "GoldCost": "2748303", "IdolCost": "198", "UpgradeTime": "715200", "Art": "a_Upgrade_Barn_9", "BackgroundArt": "a_Upgrade_PetHouse_1", "UpgradeIcon": "a_BarnUpgradeIcon", "UpgradeDescription": "Increases the max pet level cap by 2"}, {"BuildingName": "<PERSON>n", "BuildingID": "13", "DisplayName": "Hatchery Level 10", "Type": "<PERSON>n", "Rank": "10", "PlayerLevel": "45", "GoldCost": "4781913", "IdolCost": "239", "UpgradeTime": "1024200", "Art": "a_Upgrade_Barn_Final", "BackgroundArt": "a_Upgrade_PetHouse_1", "UpgradeIcon": "a_BarnUpgradeIcon"}]
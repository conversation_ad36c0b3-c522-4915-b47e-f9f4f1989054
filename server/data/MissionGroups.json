[{"GroupName": "Slaying", "DisplayName": "Combat Achievements", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["AvoidDamage100000", "AvoidDamage10000000", "AvoidDamage100000000"], "Mission2": ["ProcKillTrog1000", "ProcKillTrog5000", "ProcKillTrog10000"], "Mission3": ["SlayJackal1000", "SlayJackal5000", "SlayJackalHard10000"], "Mission4": ["KillWith2ndPower1250", "KillWith2ndPower3000", "KillWith2ndPowerHard8000"], "Mission5": ["KillBossIn10SecondsHard"], "Mission6": ["ProcKillUndead1000", "ProcKillUndead5000", "ProcKillUndead10000"], "Mission7": ["HealFromProc10000", "HealFromProc100000", "HealFromProc1000000"], "Mission8": ["KillDaylight5000", "KillDaylight8000", "KillDaylight15000"], "Mission9": ["TotalRangeDamage300000", "TotalRangeDamage10000000", "TotalRangeDamage300000000"], "Mission10": ["HealFromOrbs100000", "HealFromOrbs1000000", "HealFromOrbs100000000"], "Mission11": ["SlayLizard1000", "SlayLizard5000", "SlayLizardHard10000"]}, {"GroupName": "Slaying", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["KillWhileMeleeBuff500", "KillWhileMeleeBuff2000", "KillWhileMeleeBuff6000"], "Mission2": ["KillCrowdControl1000", "KillCrowdControl5000", "KillCrowdControlHard10000"], "Mission3": ["GainTotalProc800", "GainTotalProc3000", "GainTotalProc12000"], "Mission4": ["SlayGoblin1000", "SlayGoblin5000", "SlayGoblinHard10000"], "Mission5": ["SlayGhost1000", "SlayGhost5000", "SlayGhostHard10000"], "Mission6": ["ProcKillSylvan1000", "ProcKillSylvan5000", "ProcKillSylvan10000"], "Mission7": ["ApexAdventurer"], "Mission8": ["TotalPowerDamage300000", "TotalPowerDamage10000000", "TotalPowerDamage300000000"], "Mission9": ["KillInOneBlow10", "KillInOneBlow15", "KillInOneBlow20"], "Mission10": ["TakeTotalDamage100000", "TakeTotalDamage1000000", "TakeTotalDamage100000000"], "Mission11": ["KillWith1stPower1250", "KillWith1stPower3000", "KillWith1stPowerHard8000"]}, {"GroupName": "Slaying", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["AllBossesWakingHard"], "Mission2": ["MasterTotal"], "Mission3": ["KillWhile20HP500", "KillWhile20HP2000", "KillWhile20HPHard6000"], "Mission4": ["SlayDevourer1000", "SlayDevourer5000", "SlayDevourerHard10000"], "Mission5": ["ProcKillInfernal1000", "ProcKillInfernal5000", "ProcKillInfernal10000"], "Mission6": ["KillWhileMagicBuff500", "KillWhileMagicBuff2000", "KillWhileMagicBuff6000"], "Mission7": ["SlayGriffin100", "SlayGriffin500", "SlayGriffinHard1000"], "Mission8": ["SlaySpider1000", "SlaySpider5000", "SlaySpiderHard10000"], "Mission9": ["KillBossesHard500", "KillBossesHard1000", "KillBossesHard5000"], "Mission10": ["SlayKrakensHard100"], "Mission11": ["ProcKillDraconic1000", "ProcKillDraconic5000", "ProcKillDraconic10000"]}, {"GroupName": "Slaying", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["SlayPsychophage1000", "SlayPsychophage5000", "SlayPsychophageHard10000"], "Mission2": ["KillWithRanged2000", "KillWithRanged6000", "KillWithRangedHard12000"], "Mission3": ["KillBossInAirHard"], "Mission4": ["KillWith3rdPower1250", "KillWith3rdPower3000", "KillWith3rdPowerHard8000"], "Mission5": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Mission6": ["AllBossesWaking"], "Mission7": ["KillOfEachType10000", "KillOfEachType50000", "KillOfEachType100000"], "Mission8": ["ActiveProcsSec5", "ActiveProcsSec8", "ActiveProcsSec12"], "Mission9": ["TheProcDoctor"], "Mission10": ["SlaySkeletons1000", "SlaySkeletons5000", "SlaySkeletonsHard10000"], "Mission11": ["TotalMeleeDamage300000", "TotalMeleeDamage10000000", "TotalMeleeDamage300000000"]}, {"GroupName": "Slaying", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["ReignInBlood"], "Mission2": ["KillBossesNormal500", "KillBossesNormal1000", "KillBossesNormal5000"], "Mission3": ["KillNightfall5000", "KillNightfall8000", "KillNightfall15000"], "Mission4": ["KillBossIn10Seconds"], "Mission5": ["ProcKillMythic1000", "ProcKillMythic5000", "ProcKillMythic10000"], "Mission6": ["KillWhileSpeedBuff500", "KillWhileSpeedBuff2000", "KillWhileSpeedBuff6000"], "Mission7": ["SlayGnome1000", "SlayGnome5000", "SlayGnomeHard10000"], "Mission8": ["KillBuffed9001"], "Mission9": ["KillWhileFullHP1000", "KillWhileFullHP5000", "KillWhileFullHPHard10000"], "Mission10": ["SlayDragon500", "SlayDragon1000", "SlayDragonHard5000"], "Mission11": ["MasterType"]}, {"GroupName": "Exploration", "DisplayName": "Exploration Achievements", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["OhThePlacesYouWillGo"], "Mission2": ["CompleteQuestWolfsEnd"], "Mission3": ["CompleteQuestEmeraldGlades"], "Mission4": ["OpenChestA", "OpenChestB", "OpenChestC"], "Mission5": ["VisitAllOfFelbridge"], "Mission6": ["FallForSecondA", "FallForSecondB", "FallForSecondC"], "Mission7": ["VisitEveryZone"]}, {"GroupName": "Exploration", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission2": ["CompleteQuestBlackRoseMire"], "Mission3": ["CompleteQuestDeepgardCastle"], "Mission4": ["LootGoldTutorialMisisons"], "Mission5": ["VisitAllOfCemeteryHill"], "Mission6": ["OpenYourMapTimes"], "Mission7": ["Get5StarBoat"]}, {"GroupName": "Exploration", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission2": ["CompleteQuestFelbridge"], "Mission3": ["CompleteQuestA", "CompleteQuestB", "CompleteQuestC"], "Mission4": ["CompleteAllQuestWaking"], "Mission5": ["VisitAllOfStormshardMountain"], "Mission6": ["EncounterEveryMonsterWaking"], "Mission7": ["RelaxInThrone"]}, {"GroupName": "Exploration", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission2": ["CompleteQuestCemeteryHill"], "Mission3": ["KingOfTheWorldNew"], "Mission4": ["VisitAllOfWolfsEnd"], "Mission5": ["VisitAllOfEmeraldGlades"], "Mission6": ["EncounterEveryBossWaking"], "Mission7": ["VisitEveryVendorWaking"]}, {"GroupName": "Exploration", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission2": ["CompleteQuestStormshardMountain"], "Mission3": ["DanceOnTheMoai"], "Mission4": ["VisitAllOfBlackRoseMire"], "Mission5": ["VisitAllOfDeepgardCastle"], "Mission6": ["InspectPlayers"], "Mission7": ["MountThroughWolfsEnd"]}, {"GroupName": "Dungeon", "DisplayName": "Dungeon Achievements", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["dunreward1"], "Mission2": ["CompleteDungeonStormshardMountain"], "Mission3": ["CompleteDungeonBlackRoseMire"], "Mission4": ["CompleteDungeonDeepgardCastleHard"], "Mission5": ["CompleteAllDungeonWakingHard"], "Mission6": ["NephitsDungeonKill40MonstersAtSameTime"], "Mission7": ["SvarsSpiteFullPartyNoPoisonDamageTakenHard"], "Mission8": ["MindlessQueensGladeFullClearWithoutGettingWet"], "Mission9": ["BanditCampKillBothBossesAtTheSameTimeHard"], "Mission10": ["TheMouthOfMeylourKill30RockHulksAtSameTime"], "Mission11": ["PappysBonesKillBossOnlyUsingBasicRangedHard"]}, {"GroupName": "Dungeon", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["dunreward2"], "Mission2": ["CompleteDungeonEmeraldGlades"], "Mission3": ["CompleteDungeonWolfsEnd"], "Mission4": ["CompleteDungeonCemeteryHillHard"], "Mission5": ["Get5StarsEveryDungeon"], "Mission6": ["NephitsDungeonKillBoss15SecHard"], "Mission7": ["LairOfTheOoyakKill6BabyOoyakAtSameTime"], "Mission8": ["MindlessQueensGladeKillBossWithoutGettingHitByHerProjectilesHard"], "Mission9": ["SvaggsLastStandKill20SpidersAtSameTime"], "Mission10": ["DerelictionOfDutyFullPartyFullClearNoVigilHitsHard"], "Mission11": ["YagagasJailbreakDefeatBoss30SecAfterEnteringDungeonHardNoDeaths"]}, {"GroupName": "Dungeon", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["dunreward3"], "Mission2": ["CompleteDungeonDeepgardCastle"], "Mission3": ["CompleteAllDungeonWaking"], "Mission4": ["CompleteDungeonFelbridgeHard"], "Mission5": ["RunRamparts"], "Mission6": ["DreamDragonsLairFullPartyKillBossNoManaHard"], "Mission7": ["LairOfTheOoyakDefeatBoss30SecAfterEnteringDungeonHardNoDeaths"], "Mission8": ["ArachnaesSwampSpeedRun4MinNoWetHard"], "Mission9": ["SvaggsLastStandKillBothBossesBefreKillingTheTwoHenchmenHard"], "Mission10": ["DerelictionOfDutyKillAllSpawnersBeforeTheySummonTotal15Ghosts"], "Mission11": ["KamaksIdolCrowdControlBoss3TimesHard"]}, {"GroupName": "Dungeon", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["dunreward4"], "Mission2": ["CompleteDungeonCemeteryHill"], "Mission3": ["CompleteDungeonStormshardMountainHard"], "Mission4": ["CompleteDungeonBlackRoseMireHard"], "Mission5": ["CompleteDungeonA", "CompleteDungeonB", "CompleteDungeonC"], "Mission6": ["LairOfTheCastoutSpeedRun4MinHardNoDeaths"], "Mission7": ["BroodvictorsChallengePerfectAccuracy"], "Mission8": ["TheGreatGreenSvathBossDontGetHitWithFireBallsHard"], "Mission9": ["TheMouthOfMeylourFullPartyCheerAtSword"], "Mission10": ["JackalsStorehouseDefeatBossOnlyUsingBasicMelee"], "Mission11": ["RevenantOfTheWarslainKillBossAnd12GhostAtSameTime"]}, {"GroupName": "Dungeon", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["dunreward5"], "Mission2": ["CompleteDungeonFelbridge"], "Mission3": ["CompleteDungeonEmeraldGladesHard"], "Mission4": ["CompleteDungeonWolfsEndHard"], "Mission5": ["CompleteDungeonHardA", "CompleteDungeonHardB", "CompleteDungeonHardC"], "Mission6": ["SvarsSpiteFullPartyCheerWithDragonPetsAtStatue"], "Mission7": ["BroodvictorsChallengeKillAllPsychophagesOnPlatformsWithoutBeingHit"], "Mission8": ["MysteryOfTheYornakKillBossBeforeAnyFliersHard"], "Mission9": ["TheMouthOfMeylourOpen2Chest1SecApartHard"], "Mission10": ["PappysBonesKnockAMonsterIntoAPit"], "Mission11": ["SecretOfTheWispsKill60MonstersWithWispBuffsActiveHard"]}, {"GroupName": "Crafting", "DisplayName": "Craft and Collection Achievements", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["MasterCrafter"], "Mission2": ["MineDragonoreA", "MineDragonoreB", "MineDragonoreC"], "Mission3": ["FindAllItemFelbridge"], "Mission4": ["FindAllItemsWaking"], "Mission5": ["FindMaterialInfernalA", "FindMaterialInfernalB", "FindMaterialInfernalC"], "Mission6": ["CraftCharmBlueA", "CraftCharmBlueB", "CraftCharmBlueC"], "Mission7": ["CraftCharmWhiteA", "CraftCharmWhiteB", "CraftCharmWhiteC"], "Mission8": ["ReachCraftingLevel25"], "Mission9": ["CollectAllRunes"], "Mission10": ["EnhanceItemToA", "EnhanceItemToB", "EnhanceItemToC"]}, {"GroupName": "Crafting", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Mission2": ["CollectGoldFromMonstersA", "CollectGoldFromMonstersB", "CollectGoldFromMonstersC"], "Mission3": ["FindAllItemCemeteryHill"], "Mission4": ["GrabHealthOrbsA", "GrabHealthOrbsB", "GrabHealthOrbsC"], "Mission5": ["FindMaterialUndeadA", "FindMaterialUndeadB", "FindMaterialUndeadC"], "Mission6": ["CraftCharmRedA", "CraftCharmRedB", "CraftCharmRedC"], "Mission7": ["MineDragonOreInOneDay"], "Mission8": ["ReachCraftingLevel10"], "Mission9": ["CraftCharmLevelA", "CraftCharmLevelB", "CraftCharmLevelC"]}, {"GroupName": "Crafting", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["MasterMoney"], "Mission2": ["CollectGoldFromChestsA", "CollectGoldFromChestsB", "CollectGoldFromChestsC"], "Mission3": ["FindAllItemStormshardMountain"], "Mission4": ["SpendDragonoreA", "SpendDragonoreB", "SpendDragonoreC"], "Mission5": ["FindMaterialSylvanA", "FindMaterialSylvanB", "FindMaterialSylvanC"], "Mission6": ["CraftCharmGreenA", "CraftCharmGreenB", "CraftCharmGreenC"], "Mission7": ["BuyVendorItemsA", "BuyVendorItemsB", "BuyVendorItemsC"], "Mission8": ["GetGoldQuestsA", "GetGoldQuestsB", "GetGoldQuestsC"], "Mission9": ["SocketCharmsA", "SocketCharmsB", "SocketCharmsC"]}, {"GroupName": "Crafting", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Mission2": ["FindAllItemWolfsEnd"], "Mission3": ["FindAllItemEmeraldGlades"], "Mission4": ["SpendGoldA", "SpendGoldB", "SpendGoldC"], "Mission5": ["FindMaterialDraconicA", "FindMaterialDraconicB", "FindMaterialDraconicC"], "Mission6": ["CraftCharmYellowA", "CraftCharmYellowB", "CraftCharmYellowC"], "Mission7": ["ObtainPetsA", "ObtainPetsB", "ObtainPetsC"], "Mission8": ["SocketCharmLevelA", "SocketCharmLevelB", "SocketCharmLevelC"], "Mission9": ["ReplaceCharmsA", "ReplaceCharmsB"]}, {"GroupName": "Crafting", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["MasterColor"], "Mission2": ["FindAllItemBlackRoseMire"], "Mission3": ["FindAllItemDeepgardCastle"], "Mission4": ["FindMaterialTrogA", "FindMaterialTrogB", "FindMaterialTrogC"], "Mission5": ["FindMaterialMythicA", "FindMaterialMythicB", "FindMaterialMythicC"], "Mission6": ["CraftCharmPurpleA", "CraftCharmPurpleB", "CraftCharmPurpleC"], "Mission7": ["ObtainMountsA", "ObtainMountsB", "ObtainMountsC"], "Mission8": ["CraftCharmsA", "CraftCharmsB", "CraftCharmsC"], "Mission9": ["EnhanceTotalItems25"]}, {"GroupName": "Blitzing", "DisplayName": "Blitzing Achievements", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission2": ["FallInToAPit"], "Mission3": ["CompletePvP"], "Mission4": ["CompleteClass"], "Mission5": ["ChangeDirections1000000000"], "Mission6": ["DieInPartyAsTheLeader"]}, {"GroupName": "Blitzing", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission2": ["MountRemountAirborn"], "Mission3": ["CompleteDungeon"], "Mission4": ["CompleteSlaying"], "Mission5": ["DanceNude"], "Mission6": ["WearFullItemLevel25"]}, {"GroupName": "Blitzing", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission2": ["DieInWater(They All Float)"], "Mission3": ["CompleteCrafting"], "Mission4": ["ReviveFullHP100Times"], "Mission5": ["GetSnared100000"], "Mission6": ["WearFullItemLevel50"]}, {"GroupName": "Blitzing", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission2": ["Die100Times"], "Mission3": ["CompleteExploration"], "Mission4": ["EatVigilDamage100000"], "Mission5": ["KillInNude1000"], "Mission6": ["CompleteAchievoX"]}, {"GroupName": "Blitzing", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission2": ["GetKilledByUndead"], "Mission3": ["CompleteSocial"], "Mission4": ["WalkBackwardsSteps1000"], "Mission5": ["FullPartyDies5Seconds"], "Mission6": ["PappysBoneGetHitBy3StairVigilsInARow"]}, {"GroupName": "PvP", "DisplayName": "Player vs. Player Achievements", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>"}, {"GroupName": "PvP", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>"}, {"GroupName": "PvP", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>"}, {"GroupName": "PvP", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>"}, {"GroupName": "PvP", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>"}, {"GroupName": "Rogue", "DisplayName": "Rogue Achievements", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["UseClassPower400", "UseClassPower1200", "UseClassPower3200"], "Mission2": ["KillWithHawkStrike1000", "KillWithHawkStrike3000", "KillWithHawkStrikeHard5000"], "Mission3": ["KillStunWith2nd500", "KillStunWith2nd1100", "KillStunWith2ndHard3000"], "Mission4": ["RespecFirstTime"], "Mission5": ["KillWithMaces10000", "KillWithMaces50000", "KillWithMacesHard100000"], "Mission6": ["KillWith1stWhileStealthed3000", "KillWith1stWhileStealthed7000", "KillWith1stWhileStealthedHard15000"]}, {"GroupName": "Rogue", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["KillWithTripleStrike3000", "KillWithTripleStrike8000", "KillWithTripleStrikeHard12000"], "Mission2": ["ReachLevel5"], "Mission3": ["KillWeakWith1st800", "KillWeakWith1st1800", "KillWeakWith1stHard3600"], "Mission4": ["KillWith2ndWhileStealthed1000", "KillWith2ndWhileStealthed3000", "KillWith2ndWhileStealthedHard7000"], "Mission5": ["KillWithWeaken2000", "KillWithWeaken6000", "KillWithWeakenHard10000"], "Mission6": ["ReachLevel25"]}, {"GroupName": "Rogue", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["KillWithPoisonDoT500", "KillWithPoisonDoT800", "KillWithPoisonDoTHard1200"], "Mission2": ["KillWithArmorBreak2000", "KillWithArmorBreak6000", "KillWithArmorBreakHard10000"], "Mission3": ["KillRooted2000", "KillRooted6000", "KillRootedHard12000"], "Mission4": ["TravelStealthed10000", "TravelStealthed100000", "TravelStealthed1000000"], "Mission5": ["UnlockSubClass"]}, {"GroupName": "Rogue", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["ReachLevel50"], "Mission2": ["Root6000", "Root12000", "RootHard25000"], "Mission3": ["KillWithRogueStun3000", "KillWithRogueStun8000", "KillWithRogueStunHard12000"], "Mission4": ["KillWithPoisonStrike3000", "KillWithPoisonStrike8000", "KillWithPoisonStrikeHard12000"], "Mission5": ["KillArmorBroken1000", "KillArmorBroken4000", "KillArmorBrokenHard10000"]}, {"GroupName": "Rogue", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["KillWithSteelWhirlwind6000", "KillWithSteelWhirlwind12000", "KillWithSteelWhirlwindHard20000"], "Mission2": ["Stun3000", "Stun7000", "StunHard14000"], "Mission3": ["StealthBelow20HP100", "StealthBelow20HP300", "StealthBelow20HP650"], "Mission4": ["KillWithClassPower5000", "KillWithClassPower15000", "KillWithClassPowerHard45000"], "Mission5": ["Respec20Times"]}, {"GroupName": "Mage", "DisplayName": "Mage Achievements", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["KillWithMageFire3000", "KillWithMageFire8000", "KillWithMageFireHard12000"], "Mission2": ["KillWithHailStorm3000", "KillWithHailStorm7000", "KillWithHailStormHard15000"], "Mission3": ["SummonHelpers1000", "SummonHelpers5000", "SummonHelpers10000"], "Mission4": ["KillDoTWithPower3000", "KillDoTWithPower7000", "KillDoTWithPowerHard15000"], "Mission5": ["KillWithStaff100", "KillWithStaff1000", "KillWithStaffHard5000"], "Mission6": ["Respec20Times"]}, {"GroupName": "Mage", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["KillWithPoisonCloud2000", "KillWithPoisonCloud6000", "KillWithPoisonCloudHard10000"], "Mission2": ["KillWithFirePillar3000", "KillWithFirePillar7000", "KillWithFirePillarHard15000"], "Mission3": ["SnareWithMagic3000", "SnareWithMagic7000", "SnareWithMagicHard15000"], "Mission4": ["UseClassPower400", "UseClassPower1200", "UseClassPower3200"], "Mission5": ["ReachLevel5"], "Mission6": ["RespecFirstTime"]}, {"GroupName": "Mage", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["KillWithMagePoisonDoT500", "KillWithMagePoisonDoT800", "KillWithMagePoisonDoTHard1200"], "Mission2": ["KillWithVineLance3000", "KillWithVineLance7000", "KillWithVineLanceHard15000"], "Mission3": ["TravelWithMageFire10000", "TravelWithMageFire100000", "TravelWithMageFire1000000"], "Mission4": ["KillWithClassPower5000", "KillWithClassPower15000", "KillWithClassPowerHard45000"], "Mission5": ["UnlockSubClass"]}, {"GroupName": "Mage", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["KillWithHelper3000", "KillWithHelper7000", "KillWithHelperHard15000"], "Mission2": ["FreezeWithIceNova6000", "FreezeWithIceNova12000", "FreezeWithIceNovaHard20000"], "Mission3": ["TravelWithIceLance10000", "TravelWithIceLance100000", "TravelWithIceLance1000000"], "Mission4": ["KillSnaredWithPower5000", "KillSnaredWithPower10000", "KillSnaredWithPower20000"], "Mission5": ["ReachLevel25"]}, {"GroupName": "Mage", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["KillWithMeteor3000", "KillWithMeteor7000", "KillWithMeteorHard15000"], "Mission2": ["FreezeWithIceLance3000", "FreezeWithIceLance7000", "FreezeWithIceLanceHard15000"], "Mission3": ["HelperTakeDamage100000", "HelperTakeDamage10000000", "HelperTakeDamage100000000"], "Mission4": ["KillFrozenWithPower3000", "KillFrozenWithPower7000", "KillFrozenWithPowerHard12000"], "Mission5": ["ReachLevel50"]}, {"GroupName": "<PERSON><PERSON><PERSON>", "DisplayName": "Pa<PERSON>in <PERSON>s", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["KillWithCleave3000", "KillWithCleave8000", "KillWithCleaveHard12000"], "Mission2": ["HealWithHealingTouch100000", "HealWithHealingTouch1000000", "HealWithHealingTouch100000000"], "Mission3": ["StunWithShieldSlam5000", "StunWithShieldSlam10000", "StunWithShieldSlamHard20000"], "Mission4": ["HealNumPlayerWithHealTeam1000", "HealNumPlayerWithHealTeam5000", "HealNumPlayerWithHealTeamHard10000"], "Mission5": ["ReachLevel5"], "Mission6": ["RespecFirstTime"]}, {"GroupName": "<PERSON><PERSON><PERSON>", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["KillWithSmash3000", "KillWithSmash8000", "KillWithSmashHard12000"], "Mission2": ["UseHealingTouch20HP2000", "UseHealingTouch20HP6000", "UseHealingTouch20HP12000"], "Mission3": ["KillStunWith1st3000", "KillStunWith1st7000", "KillStunWith1stHard12000"], "Mission4": ["UseHealTeamNumPlayer20HP500", "UseHealTeamNumPlayer20HP2000", "UseHealTeamNumPlayer20HPHard5000"], "Mission5": ["ReachLevel25"], "Mission6": ["KillTauntedWithBasicRange2000", "KillTauntedWithBasicRange6000", "KillTauntedWithBasicRange12000"]}, {"GroupName": "<PERSON><PERSON><PERSON>", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["KillWithShieldSlam3000", "KillWithShieldSlam8000", "KillWithShieldSlamHard12000"], "Mission2": ["TauntWithWarcry5000", "TauntWithWarcry12000", "TauntWithWarcryHard26000"], "Mission3": ["KillWithJumpSlam5000", "KillWithJumpSlam12000", "KillWithJumpSlamHard28000"], "Mission4": ["UseClassPower400", "UseClassPower1200", "UseClassPower3200"], "Mission5": ["ReachLevel50"]}, {"GroupName": "<PERSON><PERSON><PERSON>", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["KillWithSkewer3000", "KillWithSkewer8000", "KillWithSkewerHard12000"], "Mission2": ["KillWithWarcry3000", "KillWithWarcry8000", "KillWithWarcryHard12000"], "Mission3": ["UseGuardOfTheWarrior2000", "UseGuardOfTheWarrior6000", "UseGuardOfTheWarrior12000"], "Mission4": ["KillWithClassPower5000", "KillWithClassPower15000", "KillWithClassPowerHard45000"], "Mission5": ["UnlockSubClass"]}, {"GroupName": "<PERSON><PERSON><PERSON>", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["HealTotalWithHealTeam500000", "HealTotalWithHealTeam50000000", "HealTotalWithHealTeam500000000"], "Mission2": ["KillTauntedWith1st5000", "KillTauntedWith1st12000", "KillTauntedWith1stHard25000"], "Mission3": ["AbosrbDamageWithGOTW100000", "AbosrbDamageWithGOTW10000000", "AbosrbDamageWithGOTW100000000"], "Mission4": ["KillWithAxes10000", "KillWithAxes50000", "KillWithAxes100000"], "Mission5": ["Respec20Times"]}, {"GroupName": "Social", "DisplayName": "Social Achievements", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["PartyWithPlayersA", "PartyWithPlayersB", "PartyWithPlayersC"], "Mission2": ["JoinAGuild"], "Mission3": ["PartyAllPaladins"], "Mission4": ["DefeatBossesAllRogues"], "Mission5": ["PartyWithPlayersHardA", "PartyWithPlayersHardB", "PartyWithPlayersHardC"], "Mission6": ["<PERSON><PERSON><PERSON><PERSON>"], "Mission7": ["WitnessPlayersGetAchievementsA", "WitnessPlayersGetAchievementsB", "WitnessPlayersGetAchievementsC"], "Mission8": ["Have20OfEachClassInYourGuild"], "Mission9": ["FullPartyMounted"], "Mission10": ["HaveGuildmatesA", "HaveGuildmatesB", "HaveGuildmatesC"], "Mission11": ["RelaxWhileSomeoneDances"]}, {"GroupName": "Social", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["GainFriendsA", "GainFriendsB", "GainFriendsC"], "Mission2": ["HelpPlayersQuestA", "HelpPlayersQuestB", "HelpPlayersQuestC"], "Mission3": ["PartyAllRogues"], "Mission4": ["FarmUpCraftingInGroupA", "FarmUpCraftingInGroupB", "FarmUpCraftingInGroupC"], "Mission5": ["PartyWithGuildMatesHardA", "PartyWithGuildMatesHardB", "PartyWithGuildMatesHardC"], "Mission6": ["TeamHelper"], "Mission7": ["WitnessPlayersGetItemsA", "WitnessPlayersGetItemsB", "WitnessPlayersGetItemsC"], "Mission8": ["HaveLevel50InGuildA", "HaveLevel50InGuildB", "HaveLevel50InGuildC"], "Mission9": ["EveryOneInPartyBeLevel50"], "Mission10": ["DealMostDamageInAGroupA", "DealMostDamageInAGroupB", "DealMostDamageInAGroupC"], "Mission11": ["PartyWithAllOfTheSameEliteClassA", "PartyWithAllOfTheSameEliteClassB", "PartyWithAllOfTheSameEliteClassC"]}, {"GroupName": "Social", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["LetOthersGetChestA", "LetOthersGetChestB", "LetOthersGetChestC"], "Mission2": ["PartyWithGuildMatesA", "PartyWithGuildMatesB", "PartyWithGuildMatesC"], "Mission3": ["<PERSON><PERSON><PERSON>"], "Mission4": ["FarmUpGoldInGroupA", "FarmUpGoldInGroupB", "FarmUpGoldInGroupC"], "Mission5": ["DefeatBossesAllMagesHard"], "Mission6": ["PartyAnimal"], "Mission7": ["BeFriendsWith10OfEachClass"], "Mission8": ["DefeatBossesWithGuildmates"], "Mission9": ["PartyWithSamePlayerA", "PartyWithSamePlayerB", "PartyWithSamePlayerC"], "Mission10": ["WaveToPlayersA", "WaveToPlayersB", "WaveToPlayersC"]}, {"GroupName": "Social", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["InviteToPartyA", "InviteToPartyB", "InviteToPartyC"], "Mission2": ["RunDungeonInARowWithParty5"], "Mission3": ["DefeatBossesAllMages"], "Mission4": ["SlayDragonsAsGroupHardA", "SlayDragonsAsGroupHardB", "SlayDragonsAsGroupHardC"], "Mission5": ["DefeatBossesAllPaladinsHard"], "Mission6": ["AFamilyThatSlaysTogether"], "Mission7": ["HaveMagesInYourGuildA", "HaveMagesInYourGuildB"], "Mission8": ["DefeatBossesWithGuildmatesHard"], "Mission9": ["HavePaladinsInYourGuildA", "HavePaladinsInYourGuildB"], "Mission10": ["VisitFriendsHouseA", "VisitFriendsHouseB", "VisitFriendsHouseC"]}, {"GroupName": "Social", "ProgressReward": "<PERSON><PERSON><PERSON><PERSON>", "Mission1": ["TeleportToFriendsA", "TeleportToFriendsB", "TeleportToFriendsC"], "Mission2": ["PartyAllMages"], "Mission3": ["DefeatBossesAllPaladins"], "Mission4": ["BeInvitedToGroupA", "BeInvitedToGroupB", "BeInvitedToGroupC"], "Mission5": ["DefeatBossesAllRoguesHard"], "Mission6": ["SomeoneIsWatching"], "Mission7": ["HaveRoguesInYourGuildA", "HaveRoguesInYourGuildB"], "Mission8": ["FullPartyDancingDuringBossFight"], "Mission9": ["WitnessPlayersLevelUpA", "WitnessPlayersLevelUpB", "WitnessPlayersLevelUpC"], "Mission10": ["CheerToPlayersA", "CheerToPlayersB", "CheerToPlayersC"]}]